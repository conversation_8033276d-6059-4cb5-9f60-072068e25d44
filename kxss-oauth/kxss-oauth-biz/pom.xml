<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>kxss-oauth</artifactId>
        <groupId>top.kx.kxss</groupId>
        <version>4.13.1</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>kxss-oauth-biz</artifactId>
    <name>${project.artifactId}</name>
    <description>认证服务-业务模块</description>
    <dependencies>
        <dependency>
            <groupId>top.kx.kxss</groupId>
            <artifactId>kxss-oauth-entity</artifactId>
            <version>${kxss-project.version}</version>
        </dependency>
        <dependency>
            <groupId>top.kx.kxss</groupId>
            <artifactId>kxss-system-biz</artifactId>
            <version>${kxss-project.version}</version>
        </dependency>
        <dependency>
            <groupId>top.kx.kxss</groupId>
            <artifactId>kxss-base-biz</artifactId>
            <version>${kxss-project.version}</version>
        </dependency>
        <!-- jwt 只有认证服务需要使用 -->
        <dependency>
            <groupId>top.kx.basic</groupId>
            <artifactId>kxss-boot</artifactId>
        </dependency>
        <dependency>
            <groupId>top.kx.basic</groupId>
            <artifactId>kxss-jwt-starter</artifactId>
        </dependency>
        <!-- log 只有认证服务biz模块引用，其他服务都在controller模块 -->
        <dependency>
            <groupId>top.kx.basic</groupId>
            <artifactId>kxss-log-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.whvcse</groupId>
            <artifactId>easy-captcha</artifactId>
        </dependency>


    </dependencies>


</project>
