/*
 * Copyright 2002-2011 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package top.kx.kxss.oauth.granter;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import top.kx.basic.base.R;
import top.kx.basic.base.entity.SuperEntity;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.exception.BizException;
import top.kx.kxss.base.entity.user.BaseEmployee;
import top.kx.kxss.base.entity.user.BaseEmployeeRoleRel;
import top.kx.kxss.oauth.vo.param.LoginParamVO;
import top.kx.kxss.oauth.vo.result.LoginResultVO;
import top.kx.kxss.system.entity.tenant.DefUser;
import top.kx.kxss.system.vo.update.tenant.DefUserUpdateVO;

import java.util.List;

import static top.kx.kxss.oauth.granter.WxEmployeeMobileTokenGranter.GRANT_TYPE;


/**
 * 微信端手机号登录获取token
 *
 * <AUTHOR> Syer
 * <AUTHOR>
 * @date 2020年03月31日10:22:55
 */
@Component(GRANT_TYPE)
@RequiredArgsConstructor
public class WxEmployeeMobileTokenGranter extends AbstractTokenGranter implements TokenGranter {

    public static final String GRANT_TYPE = "WX_EMPLOYEE_MOBILE";

    @Override
    public R<LoginResultVO> checkParam(LoginParamVO loginParam) {
        String mobile = loginParam.getMobile();
        if (StrUtil.isBlank(mobile)) {
            return R.fail("请授权手机号");
        }

        return R.success(null);
    }


    @Override
    protected R<LoginResultVO> checkClient() {
        return R.success(null);
    }

    @Override
    protected DefUser getUser(LoginParamVO loginParam) {
        DefUser userByMobile = defUserService.getByMobile(loginParam.getMobile());
        if (ObjectUtil.isNull(userByMobile)) {
            throw new BizException("用户不存在，请联系管理员");
        }
        BaseEmployee employeeByUser = baseEmployeeService.getEmployeeByUser(userByMobile.getId());
        if (ObjectUtil.isNull(employeeByUser)) {
            throw new BizException("员工不存在，请联系管理员");
        }
        List<BaseEmployeeRoleRel> employeeRoleRelList = baseEmployeeRoleRelService.list(Wraps.<BaseEmployeeRoleRel>lbQ().eq(SuperEntity::getDeleteFlag, 0).eq(BaseEmployeeRoleRel::getEmployeeId, employeeByUser.getId()));
        if (ObjectUtil.isEmpty(employeeRoleRelList)) {
            throw new BizException("您没有被分配角色，请联系管理员");
        }
        defUserService.updateById(DefUserUpdateVO.builder()
                        .id(userByMobile.getId())
                        .empOpenId(loginParam.getOpenId())
                .build());
        return userByMobile;
    }

}
