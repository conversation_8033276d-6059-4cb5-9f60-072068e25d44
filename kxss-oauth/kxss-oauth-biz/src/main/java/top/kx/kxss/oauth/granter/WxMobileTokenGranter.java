/*
 * Copyright 2002-2011 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package top.kx.kxss.oauth.granter;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import top.kx.basic.base.R;
import top.kx.kxss.base.entity.member.MemberInfo;
import top.kx.kxss.base.vo.save.member.MemberInfoSaveVO;
import top.kx.kxss.oauth.vo.param.LoginParamVO;
import top.kx.kxss.oauth.vo.result.LoginResultVO;
import top.kx.kxss.system.entity.tenant.DefUser;
import top.kx.kxss.system.vo.save.tenant.DefUserSaveVO;

import static top.kx.kxss.oauth.granter.WxMobileTokenGranter.GRANT_TYPE;


/**
 * 微信端手机号登录获取token
 *
 * <AUTHOR> Syer
 * <AUTHOR>
 * @date 2020年03月31日10:22:55
 */
@Component(GRANT_TYPE)
@RequiredArgsConstructor
public class WxMobileTokenGranter extends AbstractTokenGranter implements TokenGranter {

    public static final String GRANT_TYPE = "WX_MOBILE";

    @Override
    public R<LoginResultVO> checkParam(LoginParamVO loginParam) {
        String mobile = loginParam.getMobile();
        if (StrUtil.isBlank(mobile)) {
            return R.fail("请授权手机号");
        }

        return R.success(null);
    }


    @Override
    protected R<LoginResultVO> checkClient() {
        return R.success(null);
    }

    @Override
    protected DefUser getUser(LoginParamVO loginParam) {
        DefUser userByMobile = defUserService.getByMobile(loginParam.getMobile());
        if (ObjectUtil.isNull(userByMobile)) {
            DefUserSaveVO defUserSaveVO = DefUserSaveVO.builder()
                    .mobile(loginParam.getMobile())
                    .username(loginParam.getMobile())
                    .nickName("微信用户")
                    .wxOpenId(loginParam.getOpenId())
                    .sex("1")
                    .state(true)
                    .build();
            userByMobile = defUserService.save(defUserSaveVO);
        }
        MemberInfo memberInfo = memberInfoService.getByUserId(userByMobile.getId());
        if (ObjectUtil.isNull(memberInfo)) {
            MemberInfoSaveVO memberInfoSaveVO = MemberInfoSaveVO.builder()
                    .mobile(userByMobile.getMobile())
                    .name(userByMobile.getNickName())
                    .code(memberInfoService.getCode())
                    .sex(userByMobile.getSex())
                    .userId(userByMobile.getId())
                    .productNum(0)
                    .build();
            memberInfoService.save(memberInfoSaveVO);
        }
        return userByMobile;
    }

}
