package top.kx.kxss.oauth.biz;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.AntPathMatcher;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.jackson.JsonUtil;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.basic.utils.CollHelper;
import top.kx.basic.utils.StrPool;
import top.kx.basic.utils.TreeUtil;
import top.kx.kxss.base.service.system.BaseRoleService;
import top.kx.kxss.base.vo.result.user.RouterMeta;
import top.kx.kxss.base.vo.result.user.VueRouter;
import top.kx.kxss.common.constant.BizConstant;
import top.kx.kxss.common.constant.RoleConstant;
import top.kx.kxss.model.enumeration.HttpMethod;
import top.kx.kxss.model.enumeration.system.ResourceTypeEnum;
import top.kx.kxss.system.entity.application.DefResource;
import top.kx.kxss.system.entity.application.DefResourceApi;
import top.kx.kxss.system.enumeration.tenant.ResourceOpenWithEnum;
import top.kx.kxss.system.service.application.DefResourceService;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 资源大业务
 *
 * <AUTHOR>
 * @date 2021/10/29 19:42
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ResourceBiz {
    private static final AntPathMatcher PATH_MATCHER = new AntPathMatcher();
    private final DefResourceService defResourceService;
    private final BaseRoleService baseRoleService;

    /**
     * 是否所有的子都是视图
     */
    private static boolean hideChildrenInMenu(List<VueRouter> children) {
        if (CollUtil.isEmpty(children)) {
            return false;
        }

        return children.stream().noneMatch(item -> ResourceTypeEnum.MENU.eq(item.getResourceType()));
    }

    /**
     * 查询当前用户可用的 功能 + 字段 + 视图
     *
     * @param applicationId 应用id
     * @return 资源树
     */
    public List<String> findVisibleResource(Long employeeId, Long applicationId) {
        List<DefResource> list;
        boolean isAdmin = baseRoleService.checkRole(employeeId, RoleConstant.TENANT_ADMIN);
        List<String> resourceCodes = Arrays.asList(ResourceTypeEnum.BUTTON.getCode(), ResourceTypeEnum.VIEW.getCode(), ResourceTypeEnum.FIELD.getCode());
        if (isAdmin) {
            list = defResourceService.findResourceListByApplicationId(applicationId != null ? Collections.singletonList(applicationId) : Collections.emptyList(), resourceCodes);
        } else {
            List<Long> resourceIdList = baseRoleService.findResourceIdByEmployeeId(applicationId, employeeId);
            if (resourceIdList.isEmpty()) {
                return Collections.emptyList();
            }

            list = defResourceService.findByIdsAndType(resourceIdList, resourceCodes);
        }
        return CollHelper.split(list, DefResource::getCode, StrPool.SEMICOLON);
    }

    /**
     * 查询当前用户可用的 菜单 + 视图
     * <p>
     * 1. 租户管理员拥有指定应用的所有 资源
     * 2. 还没成为员工的用户，没有任何 资源
     *
     * @param applicationId 应用id
     * @return 资源树
     */
    public List<VueRouter> findVisibleRouter(Long applicationId, Long employeeId, String subGroup) {
        List<DefResource> list;
        boolean isAdmin = baseRoleService.checkRole(employeeId, RoleConstant.TENANT_ADMIN);
        List<String> menuCodes = Arrays.asList(ResourceTypeEnum.MENU.getCode(), ResourceTypeEnum.VIEW.getCode());
        if (isAdmin) {
            list = defResourceService.findResourceListByApplicationId(applicationId != null ? Collections.singletonList(applicationId) : Collections.emptyList(), menuCodes);
        } else {
            List<Long> resourceIdList = baseRoleService.findResourceIdByEmployeeId(applicationId, employeeId);
            if (resourceIdList.isEmpty()) {
                return Collections.emptyList();
            }

            list = defResourceService.findByIdsAndType(resourceIdList, menuCodes);
        }

        if (StrUtil.isNotEmpty(subGroup)) {
            list = list.stream().filter(item -> subGroup.equals(item.getSubGroup())).collect(Collectors.toList());
        }

        List<VueRouter> routers = BeanPlusUtil.copyToList(list, VueRouter.class);
        List<VueRouter> tree = TreeUtil.buildTree(routers);
        forEachTree(tree, 1);
        return tree;
    }

    /**
     * 检查指定接口是否有访问权限
     *
     * @param path   请求路径
     * @param method 请求方法
     * @return 是否有权限
     */
    public Boolean checkUri(String path, String method) {
        Long employeeId = ContextUtil.getEmployeeId();
        Long applicationId = ContextUtil.getApplicationId();
        log.info("path={}, method={}, employeeId={}, applicationId={}", path, method, employeeId, applicationId);
        if (StrUtil.isEmpty(path) || StrUtil.isEmpty(method)) {
            return false;
        }
        boolean isAdmin = baseRoleService.checkRole(employeeId, RoleConstant.TENANT_ADMIN);
        List<DefResourceApi> apiList;
        if (isAdmin) {
            // 租户管理员 拥有分配给该企业的所有 资源权限
            List<DefResource> resourceList = defResourceService.findResourceListByApplicationId(
                    applicationId != null ? Arrays.asList(applicationId) : Collections.emptyList(), Collections.emptyList()
            );
            if (resourceList.isEmpty()) {
                return false;
            }
            List<Long> resourceIdList = resourceList.stream().map(DefResource::getId).distinct().collect(Collectors.toList());
            apiList = defResourceService.findApiByResourceId(resourceIdList);
        } else {
            // 普通用户 需要校验 uri + method 的权限
            List<Long> resourceIdList = baseRoleService.findResourceIdByEmployeeId(applicationId, employeeId);
            if (resourceIdList.isEmpty()) {
                return false;
            }
            apiList = defResourceService.findApiByResourceId(resourceIdList);
        }

        if (apiList.isEmpty()) {
            return false;
        }

        return apiList.parallelStream().distinct().anyMatch(item -> {
            String uri = item.getUri();

            /*
             * 若您确定只使用lamp-boot，而非lamp-cloud，请将def_resource_api表中uri的代理的前缀(/base、/system、/oauth)去除，即可 删除删除删除 if里面的代码！
             * 因为脚本数据是基于lamp-cloud配置的，所以uri地址会多一段gateway代理前缀。如
             * lamp-cloud 中地址为：/base/baseEmployee/page
             * 对应lamp-boot的地址为：/baseEmployee/page
             * 其中/base是因为使用了gateway增加的！
             */
//            if (!StrUtil.startWithAny(uri, "/gateway")) {
//                uri = StrUtil.subSuf(uri, StrUtil.indexOf(uri, '/', 1));
//            }

            boolean matchUri = PATH_MATCHER.match(StrUtil.trim(uri), StrUtil.trim(path));
            log.info("path={}, uri={}, matchUri={}, method={} apiId={}", path, uri, matchUri, item.getRequestMethod(), item.getId());
            if (HttpMethod.ALL.name().equalsIgnoreCase(item.getRequestMethod())) {
                return matchUri;
            }
            return matchUri && StrUtil.equalsIgnoreCase(method, item.getRequestMethod());
        });
    }

    private void forEachTree(List<VueRouter> tree, int level) {
        if (CollUtil.isEmpty(tree)) {
            return;
        }
        for (VueRouter item : tree) {
            log.debug("level={}, label={}", level, item.getName());
            RouterMeta meta = null;
            if (StrUtil.isNotEmpty(item.getMetaJson()) && !StrPool.BRACE.equals(item.getMetaJson())) {
                meta = JsonUtil.parse(item.getMetaJson(), RouterMeta.class);
            }
            if (meta == null) {
                meta = new RouterMeta();
            }
            if (StrUtil.isEmpty(meta.getTitle())) {
                meta.setTitle(item.getName());
            }
            meta.setIcon(item.getIcon());
            if (ResourceOpenWithEnum.INNER_CHAIN.eq(item.getOpenWith())) {
                //  是否内嵌页面
                meta.setFrameSrc(item.getComponent());
                item.setComponent(BizConstant.IFRAME);
            } else if (ResourceOpenWithEnum.OUTER_CHAIN.eq(item.getOpenWith())) {
                // 是否外链
                item.setComponent(BizConstant.IFRAME);
            }

            // 视图需要隐藏
            if (ResourceTypeEnum.VIEW.eq(item.getResourceType())) {
                meta.setHideMenu(true);
            }
            // 是否所有的子都是视图
            meta.setHideChildrenInMenu(hideChildrenInMenu(item.getChildren()));
            item.setMeta(meta);

            // 若当前菜单的 子菜单至少有一个菜单，将它设置为 LAYOUT
            if (CollUtil.isNotEmpty(item.getChildren()) && !hideChildrenInMenu(item.getChildren())) {
                item.setComponent(BizConstant.LAYOUT);
            }

            if (CollUtil.isNotEmpty(item.getChildren())) {
                forEachTree(item.getChildren(), level + 1);
            }
        }
    }

    /**
     * 检测员工是否拥有指定应用的权限
     *
     * @param applicationId 应用id
     * @param employeeId    员工id
     * @return 是否拥有指定应用的权限
     */
    public Boolean checkEmployeeHaveApplication(Long employeeId, Long applicationId) {
        boolean isAdmin = baseRoleService.checkRole(employeeId, RoleConstant.TENANT_ADMIN);
        if (isAdmin) {
            return true;
        }
        return CollUtil.isNotEmpty(baseRoleService.findResourceIdByEmployeeId(applicationId, employeeId));
    }

}
