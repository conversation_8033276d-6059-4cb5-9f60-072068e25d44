package top.kx.kxss.oauth.service;

import top.kx.kxss.base.entity.user.BaseOrg;
import top.kx.kxss.oauth.vo.param.RegisterByEmailVO;
import top.kx.kxss.oauth.vo.param.RegisterByMobileVO;
import top.kx.kxss.oauth.vo.result.OrgResultVO;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2022/9/16 12:21 PM
 * @create [2022/9/16 12:21 PM ] [tangyh] [初始创建]
 */
public interface UserInfoService {
    /**
     * 根据单位ID查找部门
     *
     * @param companyId 单位ID
     * @return java.util.List<top.kx.kxss.model.entity.base.SysOrg>
     * <AUTHOR>
     * @date 2022/9/29 11:18 PM
     * @create [2022/9/29 11:18 PM ] [tangyh] [初始创建]
     */
    List<BaseOrg> findDeptByCompany(Long companyId);

    /**
     * 查询单位和部门信息
     *
     * @return top.kx.kxss.oauth.vo.result.OrgResultVO
     * <AUTHOR>
     * @date 2022/9/15 2:37 PM
     * @create [2022/9/15 2:37 PM ] [tangyh] [初始创建]
     */
    OrgResultVO findCompanyAndDept();


    /**
     * 注册
     *
     * @param register 注册
     * @return
     */
    String registerByMobile(RegisterByMobileVO register);

    /**
     * 注册
     *
     * @param register 注册
     * @return
     */
    String registerByEmail(RegisterByEmailVO register);
}
