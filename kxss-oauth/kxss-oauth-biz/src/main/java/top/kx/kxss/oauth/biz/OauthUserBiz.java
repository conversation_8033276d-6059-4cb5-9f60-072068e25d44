package top.kx.kxss.oauth.biz;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.context.ContextUtil;
import top.kx.kxss.base.entity.member.MemberInfo;
import top.kx.kxss.base.entity.user.BaseEmployee;
import top.kx.kxss.base.service.distributor.BaseDistributorService;
import top.kx.kxss.base.service.member.MemberInfoService;
import top.kx.kxss.base.service.user.BaseEmployeeService;
import top.kx.kxss.base.vo.result.distributor.BaseDistributorResultVO;
import top.kx.kxss.base.vo.result.member.MemberInfoResultVO;
import top.kx.kxss.base.vo.result.user.BaseEmployeeResultVO;
import top.kx.kxss.common.constant.AppendixType;
import top.kx.kxss.file.service.AppendixService;
import top.kx.kxss.file.service.FileService;
import top.kx.kxss.model.vo.result.AppendixResultVO;
import top.kx.kxss.oauth.vo.result.DefUserInfoResultVO;
import top.kx.kxss.system.entity.application.DefApplication;
import top.kx.kxss.system.entity.tenant.DefUser;
import top.kx.kxss.system.service.application.DefApplicationService;
import top.kx.kxss.system.service.tenant.DefUserService;
import top.kx.kxss.system.vo.result.application.DefApplicationResultVO;

/**
 * 用户大业务
 *
 * <AUTHOR>
 * @date 2021/10/28 13:09
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OauthUserBiz {
    private final BaseEmployeeService baseEmployeeService;
    private final DefUserService defUserService;
    private final DefApplicationService defApplicationService;
    private final AppendixService appendixService;
    private final MemberInfoService memberInfoService;
    private final FileService fileService;
    private final BaseDistributorService baseDistributorService;

    public DefUserInfoResultVO getUserById(Long id) {
        // 查默认库
        DefUser defUser = defUserService.getByIdCache(id);
        if (defUser == null) {
            return null;
        }

        // 用户信息
        DefUserInfoResultVO resultVO = new DefUserInfoResultVO();
        BeanUtil.copyProperties(defUser, resultVO);

        // 用户头像
        AppendixResultVO appendix = appendixService.getByBiz(defUser.getId(), AppendixType.System.DEF__USER__AVATAR);
        if (appendix != null) {
            resultVO.setAvatarId(appendix.getId());
        }

        Long employeeId = ContextUtil.getEmployeeId();
        resultVO.setEmployeeId(employeeId);

        //查 租户库
        BaseEmployee employee = baseEmployeeService.getById(employeeId);
        BaseEmployeeResultVO baseEmployeeResultVO = BeanUtil.toBean(employee, BaseEmployeeResultVO.class);
        if (ObjectUtil.isNotNull(baseEmployeeResultVO)) {
            if (ObjectUtil.isNotNull(baseEmployeeResultVO.getAvatarId())) {
                baseEmployeeResultVO.setAvatarFile(fileService.getById(baseEmployeeResultVO.getAvatarId()));
            }
            baseEmployeeResultVO.setMobile(resultVO.getMobile());
        }
        resultVO.setBaseEmployee(baseEmployeeResultVO);

        //查 会员表
        MemberInfo memberInfo = memberInfoService.getByUserId(defUser.getId());
        if (ObjectUtil.isNotNull(memberInfo)) {
            //头像信息
            MemberInfoResultVO memberInfoResultVO = BeanUtil.copyProperties(memberInfo, MemberInfoResultVO.class);
            if (ObjectUtil.isNotNull(memberInfoResultVO.getAvatarId())) {
                memberInfoResultVO.setAvatarFile(fileService.getById(memberInfoResultVO.getAvatarId()));
            }
            resultVO.setMemberInfo(memberInfoResultVO);
        }
        DefApplication defApplication = defApplicationService.getDefApp(id);
        resultVO.setDefApplication(BeanUtil.toBean(defApplication, DefApplicationResultVO.class));
        // 经销商信息
        resultVO.setDistributorResultVO(baseDistributorService.getByUserId(defUser.getId()));
        return resultVO;
    }
}
