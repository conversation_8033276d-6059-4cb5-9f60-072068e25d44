/*
 * Copyright 2002-2011 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.kx.kxss.oauth.granter;

import org.springframework.stereotype.Component;
import top.kx.basic.base.R;
import top.kx.basic.jwt.model.AuthInfo;
import top.kx.basic.utils.StrHelper;
import top.kx.kxss.oauth.vo.param.LoginParamVO;
import top.kx.kxss.oauth.vo.result.LoginResultVO;
import top.kx.kxss.system.entity.tenant.DefUser;

import static top.kx.kxss.oauth.granter.RefreshTokenGranter.GRANT_TYPE;

/**
 * RefreshTokenGranter
 *
 * <AUTHOR>
 * <AUTHOR>
 * @date 2020年03月31日10:23:53
 */
@Component(GRANT_TYPE)
public class RefreshTokenGranter extends AbstractTokenGranter implements TokenGranter {

    public static final String GRANT_TYPE = "REFRESH_TOKEN";

    @Override
    public R<LoginResultVO> checkParam(LoginParamVO loginParam) {
        if (StrHelper.isAnyBlank(loginParam.getRefreshToken())) {
            return R.fail("请输入刷新token");
        }

        return R.success(null);
    }

    @Override
    protected DefUser getUser(LoginParamVO loginParam) {
        String refreshTokenStr = loginParam.getRefreshToken();
        AuthInfo authInfo = tokenUtil.parseRefreshToken(refreshTokenStr);
        return defUserService.getByIdCache(authInfo.getUserId());
    }


}
