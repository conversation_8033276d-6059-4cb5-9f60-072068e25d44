package top.kx.kxss.oauth.vo.result;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 登录参数
 *
 * <AUTHOR>
 * @date 2020年01月05日22:18:12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "LoginResultVO", description = "登录结果")
public class LoginResultVO {

    @ApiModelProperty(value = "随机数")
    private String uuid;

    /**
     * token
     */
    @ApiModelProperty(value = "token")
    private String token;
    @ApiModelProperty(value = "刷新token")
    private String refreshToken;
    /**
     * Token 有效时间：单位：秒
     */
    @ApiModelProperty(value = "有效期")
    private Long expire;
    /**
     * Token 到期时间
     */
    @ApiModelProperty(value = "到期时间")
    private LocalDateTime expiration;
}
