package top.kx.kxss.oauth.vo.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 登录参数
 *
 * <AUTHOR>
 * @date 2020年01月05日22:18:12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "AppPhoneLoginVO", description = "登录手机号授权参数")
public class AppPhoneLoginVO implements Serializable {
    /**
     * 微信返回的code
     */
    @NotBlank(message = "请填写临时凭证code")
    @ApiModelProperty(value = "请填写临时凭证code")
    private String code;
    /**
     * 微信返回的jsCode
     */
    @NotBlank(message = "请填写临时凭证jsCode")
    @ApiModelProperty(value = "请填写临时凭证jsCode")
    private String jsCode;

    @ApiModelProperty(value = "登录类型 0 用户小程序 1 员工小程序")
    private Integer type;
}
