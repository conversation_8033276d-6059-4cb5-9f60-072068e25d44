package top.kx.kxss.oauth.vo.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 登录参数
 *
 * <AUTHOR>
 * @date 2020年01月05日22:18:12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "AppAccountPhoneLoginVO", description = "登录手机号授权参数")
public class AppAccountPhoneLoginVO implements Serializable {

    @ApiModelProperty(value = "登录类型 0 用户小程序 1 员工小程序 2 平台小程序", hidden = true)
    private Integer type;

    @ApiModelProperty(value = "扫码登录参数")
    private String scene;

    @ApiModelProperty(value = "用户名")
    private String username;

    @ApiModelProperty(value = "密码")
    private String password;


}
