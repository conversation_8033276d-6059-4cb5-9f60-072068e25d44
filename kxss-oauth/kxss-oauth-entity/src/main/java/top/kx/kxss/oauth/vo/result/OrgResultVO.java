package top.kx.kxss.oauth.vo.result;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import top.kx.kxss.base.entity.user.BaseOrg;

import java.util.List;

/**
 * 登录参数
 *
 * <AUTHOR>
 * @date 2020年01月05日22:18:12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "OrgResultVO", description = "部门信息结果")
public class OrgResultVO {
    @ApiModelProperty(value = "当前租户下，所属单位")
    private List<BaseOrg> companyList;
    @ApiModelProperty(value = "当前租户下和单位下，所属部门")
    private List<BaseOrg> deptList;
    /**
     * 当前单位ID
     */
    @ApiModelProperty(value = "当前单位ID")
    private Long currentCompanyId;
    /**
     * 当前部门ID
     */
    @ApiModelProperty(value = "当前部门ID")
    private Long currentDeptId;

}
