package top.kx.kxss.oauth.vo.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 登录参数
 *
 * <AUTHOR>
 * @date 2020年01月05日22:18:12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "AppLoginVO", description = "登录授权参数")
public class AppLoginVO implements Serializable {
    /**
     * 微信返回的code
     */
    @ApiModelProperty(value = "发送渠道")
    @NotBlank(message = "请填写code")
    private String code;
    /**
     * 非敏感的用户信息
     */
    @NotBlank(message = "请填写非敏感的用户信息")
    private String rawData;
    /**
     * 签名信息
     */
    private String signature;
    /**
     * 加密的数据
     */
    @NotBlank(message = "请填写加密数据")
    private String encryptedData;
    /**
     * 加密密钥
     */
    @NotBlank(message = "请填写加密密钥")
    private String iv;
}
