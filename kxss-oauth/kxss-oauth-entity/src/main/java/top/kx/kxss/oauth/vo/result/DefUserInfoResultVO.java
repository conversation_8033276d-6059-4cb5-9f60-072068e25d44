package top.kx.kxss.oauth.vo.result;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.hutool.core.map.MapUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.annotation.echo.Echo;
import top.kx.basic.base.entity.Entity;
import top.kx.basic.interfaces.echo.EchoVO;
import top.kx.kxss.base.vo.result.dealers.BaseDealersResultVO;
import top.kx.kxss.base.vo.result.distributor.BaseDistributorResultVO;
import top.kx.kxss.base.vo.result.member.MemberInfoResultVO;
import top.kx.kxss.base.vo.result.user.BaseEmployeeResultVO;
import top.kx.kxss.model.constant.EchoApi;
import top.kx.kxss.model.constant.EchoDictType;
import top.kx.kxss.system.vo.result.application.DefApplicationResultVO;

import java.io.Serializable;
import java.util.Map;


/**
 * <p>
 * 实体类
 * 用户
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-09
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "DefUserInfoResultVO", description = "用户")
public class DefUserInfoResultVO extends Entity<Long> implements Serializable, EchoVO {

    private static final long serialVersionUID = 1L;
    private Map<String, Object> echoMap = MapUtil.newHashMap();

    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 用户名;大小写数字下划线
     */
    @ApiModelProperty(value = "用户名")
    @Excel(name = "用户名")
    private String username;
    /**
     * 昵称
     */
    @ApiModelProperty(value = "昵称")
    @Excel(name = "昵称")
    private String nickName;
    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    @Excel(name = "邮箱")
    private String email;
    /**
     * 手机;1开头11位纯数字
     */
    @ApiModelProperty(value = "手机")
    @Excel(name = "手机")
    private String mobile;
    /**
     * 身份证;15或18位
     */
    @ApiModelProperty(value = "身份证")
    @Excel(name = "身份证")
    private String idCard;
    /**
     * 微信OpenId
     */
    @ApiModelProperty(value = "微信OpenId")
    @Excel(name = "微信OpenId")
    private String wxOpenId;
    /**
     * 钉钉OpenId
     */
    @ApiModelProperty(value = "钉钉OpenId")
    @Excel(name = "钉钉OpenId")
    private String ddOpenId;
    /**
     * 内置;[0-否 1-是]
     */
    @ApiModelProperty(value = "内置")
    @Excel(name = "内置", replace = {"是_true", "否_false", "_null"})
    private Boolean readonly;
    /**
     * 性别;
     * #Sex{W:女;M:男;N:未知}
     */
    @ApiModelProperty(value = "性别")
    @Excel(name = "性别", replace = {"女_1", "男_2", "未知_3", "_null"})
    @Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Global.SEX)
    private String sex;
    /**
     * 民族;[01-汉族 99-其他]	@Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Global.NATION)
     */
    @ApiModelProperty(value = "民族")
    @Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Global.NATION)
    @Excel(name = "民族")
    private String nation;
    /**
     * 学历;[01-小学 02-中学 03-高中 04-专科 05-本科 06-硕士 07-博士 08-博士后 99-其他]	@Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Global.EDUCATION)
     */
    @ApiModelProperty(value = "学历")
    @Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Global.EDUCATION)
    @Excel(name = "学历")
    private String education;
    /**
     * 状态;[0-禁用 1-启用]
     */
    @ApiModelProperty(value = "状态")
    @Excel(name = "状态", replace = {"是_true", "否_false", "_null"})
    private Boolean state;

    @ApiModelProperty(value = "头像id")
    private Long avatarId;

    /**
     * 工作描述
     */
    @ApiModelProperty(value = "工作描述")
    @Excel(name = "工作描述")
    private String workDescribe;

    @ApiModelProperty(value = "员工ID")
    private Long employeeId;

    @ApiModelProperty(value = "当前员工信息")
    private BaseEmployeeResultVO baseEmployee;

    @ApiModelProperty(value = "当前应用信息")
    private DefApplicationResultVO defApplication;

    /** 为空时，默认页面由前端控制 */
    @ApiModelProperty(value = "登录成功后，跳转的页面")
    private String homePath;

    @ApiModelProperty(value = "当前会员信息")
    private MemberInfoResultVO memberInfo;

    /**
     * 经销商信息
     */
    @ApiModelProperty(value = "当前经销商信息")
    private BaseDistributorResultVO distributorResultVO;
}
