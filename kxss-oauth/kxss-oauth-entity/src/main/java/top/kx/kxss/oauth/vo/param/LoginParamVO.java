package top.kx.kxss.oauth.vo.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.kxss.oauth.enumeration.GrantType;

import javax.validation.constraints.NotNull;

/**
 * 登录参数
 *
 * <AUTHOR>
 * @date 2020年01月05日22:18:12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "LoginParamVO", description = "登录参数")
public class LoginParamVO {
    @ApiModelProperty(value = "验证码KEY")
    private String key;
    @ApiModelProperty(value = "验证码")
    private String code;

    @ApiModelProperty(value = "用户名")
    private String username;
    @ApiModelProperty(value = "密码")
    private String password;

    @ApiModelProperty(value = "手机号")
    private String mobile;

    /**
     * password: 账号密码
     * refresh_token: 刷新token
     * captcha: 验证码
     */
    @ApiModelProperty(value = "授权类型", example = "CAPTCHA", allowableValues = "CAPTCHA,REFRESH_TOKEN,PASSWORD,MOBILE")
    @NotNull(message = "授权类型不能为空")
    private GrantType grantType;

    /**
     * 前端界面点击清空缓存时调用
     */
    @ApiModelProperty(value = "刷新token")
    private String refreshToken;

    @ApiModelProperty(value = "openId", hidden = true)
    private String openId;

    @ApiModelProperty(value = "客户端ID", hidden = true)
    private String clientId;

    @ApiModelProperty(value = "客户端密码", hidden = true)
    private String clientSecret;
}
