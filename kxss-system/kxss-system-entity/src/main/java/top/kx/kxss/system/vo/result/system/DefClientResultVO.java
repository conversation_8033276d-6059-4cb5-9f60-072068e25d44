package top.kx.kxss.system.vo.result.system;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.hutool.core.map.MapUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import top.kx.basic.annotation.echo.Echo;
import top.kx.basic.base.entity.Entity;
import top.kx.basic.interfaces.echo.EchoVO;
import top.kx.kxss.model.constant.EchoApi;
import top.kx.kxss.model.constant.EchoDictType;

import java.io.Serializable;
import java.util.Map;


/**
 * <p>
 * 实体类
 * 客户端
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-13
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "DefClientResultVO", description = "客户端")
public class DefClientResultVO extends Entity<Long> implements Serializable, EchoVO {

    private static final long serialVersionUID = 1L;
    private Map<String, Object> echoMap = MapUtil.newHashMap();

    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 客户端ID
     */
    @ApiModelProperty(value = "客户端ID")
    @Excel(name = "客户端ID")
    private String clientId;
    /**
     * 客户端密码
     */
    @ApiModelProperty(value = "客户端密码")
    @Excel(name = "客户端密码")
    private String clientSecret;
    /**
     * 客户端名称
     */
    @ApiModelProperty(value = "客户端名称")
    @Excel(name = "客户端名称")
    private String name;
    /**
     * 类型;[10-WEB网站;15-移动端应用;20-手机H5网页;25-内部服务; 30-第三方应用]	@Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Tenant.CLIENT_TYPE)
     */
    @ApiModelProperty(value = "类型")
    @Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.System.CLIENT_TYPE)
    @Excel(name = "类型")
    private String type;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Excel(name = "备注")
    private String remarks;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    @Excel(name = "状态", replace = {"是_true", "否_false", "_null"})
    private Boolean state;

    /**
     * token有效期(秒) -1为不失效
     */
    @ApiModelProperty(value = "token有效期(秒) -1为不失效")
    @Excel(name = "token有效期(秒) -1为不失效")
    private Long expireMillis;
}
