package top.kx.kxss.system.vo.result.system;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.hutool.core.map.MapUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import top.kx.basic.annotation.echo.Echo;
import top.kx.basic.base.entity.Entity;
import top.kx.basic.interfaces.echo.EchoVO;
import top.kx.kxss.model.constant.EchoApi;
import top.kx.kxss.model.constant.EchoDictType;

import java.io.Serializable;
import java.util.Map;


/**
 * <p>
 * 实体类
 * 登录日志
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "DefLoginLogResultVO", description = "登录日志")
public class DefLoginLogResultVO extends Entity<Long> implements Serializable, EchoVO {

    private static final long serialVersionUID = 1L;
    private Map<String, Object> echoMap = MapUtil.newHashMap();

    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 所属企业
     */
    @ApiModelProperty(value = "所属企业")
    @Excel(name = "所属企业")
    private Long tenantId;
    /**
     * 登录员工
     */
    @ApiModelProperty(value = "登录员工")
    @Excel(name = "登录员工")
    private Long employeeId;
    /**
     * 登录用户
     */
    @ApiModelProperty(value = "登录用户")
    @Excel(name = "登录用户")
    private Long userId;
    /**
     * 登录IP
     */
    @ApiModelProperty(value = "登录IP")
    @Excel(name = "登录IP")
    private String requestIp;
    /**
     * 登录人姓名
     */
    @ApiModelProperty(value = "登录人姓名")
    @Excel(name = "登录人姓名")
    private String nickName;
    /**
     * 登录人账号
     */
    @ApiModelProperty(value = "登录人账号")
    @Excel(name = "登录人账号")
    private String username;
    /**
     * 登录描述
     */
    @ApiModelProperty(value = "登录描述")
    @Excel(name = "登录描述")
    private String description;
    /**
     * 登录时间
     */
    @ApiModelProperty(value = "登录时间")
    @Excel(name = "登录时间")
    private String loginDate;
    /**
     * 浏览器请求头
     */
    @ApiModelProperty(value = "浏览器请求头")
    @Excel(name = "浏览器请求头")
    private String ua;
    /**
     * 浏览器名称
     */
    @ApiModelProperty(value = "浏览器名称")
    @Excel(name = "浏览器名称")
    private String browser;
    /**
     * 浏览器版本
     */
    @ApiModelProperty(value = "浏览器版本")
    @Excel(name = "浏览器版本")
    private String browserVersion;
    /**
     * 操作系统
     */
    @ApiModelProperty(value = "操作系统")
    @Excel(name = "操作系统")
    private String operatingSystem;
    /**
     * 登录地点
     */
    @ApiModelProperty(value = "登录地点")
    @Excel(name = "登录地点")
    private String location;

    /**
     * '登录状态;[01-登录成功 02-验证码错误 03-密码错误 04-账号锁定 05-切换租户 06-短信验证码错误]
     *
     * @Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Tenant.LOGIN_STATUS)
     */
    @Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.System.LOGIN_STATUS)
    @ApiModelProperty(value = "登录状态")
    private String status;
}
