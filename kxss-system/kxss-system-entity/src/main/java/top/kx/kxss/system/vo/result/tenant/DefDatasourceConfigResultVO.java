package top.kx.kxss.system.vo.result.tenant;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.Entity;
import top.kx.basic.interfaces.echo.EchoVO;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * 实体类
 * 数据源
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "DefDatasourceConfigResultVO", description = "数据源")
public class DefDatasourceConfigResultVO extends Entity<Long> implements Serializable, EchoVO {

    private static final long serialVersionUID = 1L;
    private Map<String, Object> echoMap = new HashMap<>();

    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @Excel(name = "名称")
    private String name;
    @ApiModelProperty(value = "数据源前缀")
    private String dbPrefix;
    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名")
    @Excel(name = "用户名")
    private String username;
    /**
     * 链接
     */
    @ApiModelProperty(value = "链接")
    @Excel(name = "链接")
    private String url;
    /**
     * 驱动
     */
    @ApiModelProperty(value = "驱动")
    @Excel(name = "驱动")
    private String driverClassName;

    private Long tenantId;
}
