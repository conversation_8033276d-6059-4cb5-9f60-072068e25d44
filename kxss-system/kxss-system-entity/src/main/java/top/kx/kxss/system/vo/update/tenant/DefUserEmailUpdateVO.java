package top.kx.kxss.system.vo.update.tenant;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import top.kx.basic.annotation.constraints.NotEmptyPattern;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;

import static top.kx.basic.utils.ValidatorUtil.REGEX_EMAIL;

/**
 * <p>
 * 用户密码修改VO
 * </p>
 *
 * <AUTHOR>
 * @since 2019-11-04
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "DefUserEmailUpdateVO", description = "用户邮箱修改VO")
public class DefUserEmailUpdateVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "验证码")
    @Size(max = 10, message = "验证码长度不能超过{max}")
    @NotEmpty(message = "请填写验证码")
    private String code;

    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    @Size(max = 255, message = "邮箱长度不能超过{max}")
    @NotEmpty(message = "请填写邮箱")
    @NotEmptyPattern(regexp = REGEX_EMAIL, message = "请输入正确的邮箱地址")
    private String email;


    @ApiModelProperty(value = "消息模板")
    @NotEmpty(message = "请填写消息模板")
    private String templateCode;
}
