package top.kx.kxss.system.vo.save.application;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <p>
 * 实体类
 * 资源接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "DefResourceApiSaveVO", description = "资源接口")
public class DefResourceApiSaveVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 控制器类名
     */
    @ApiModelProperty(value = "控制器类名")
    @Size(max = 255, message = "控制器类名长度不能超过255")
    private String controller;
    /**
     * 所属服务;取配置文件中 spring.application.name
     */
    @ApiModelProperty(value = "所属服务")
    @Size(max = 255, message = "所属服务长度不能超过255")
    private String springApplicationName;
    /**
     * 请求类型
     */
    @ApiModelProperty(value = "请求类型")
    @Size(max = 255, message = "请求类型长度不能超过255")
    @NotEmpty(message = "请求类型不能为空")
    private String requestMethod;
    /**
     * 接口名;接口上的注释
     */
    @ApiModelProperty(value = "接口名")
    @Size(max = 255, message = "接口名长度不能超过255")
    private String name;
    /**
     * 接口路径;lamp-cloud版：uri需要拼接上gateway中路径前缀
     * lamp-boot版: uri需要不需要拼接前缀
     */
    @ApiModelProperty(value = "接口地址")
    @Size(max = 255, message = "接口地址长度不能超过255")
    @NotEmpty(message = "接口地址不能为空")
    private String uri;

    @ApiModelProperty(value = "是否手动录入")
    private Boolean isInput;
}
