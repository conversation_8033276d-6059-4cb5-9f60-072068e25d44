package top.kx.kxss.system.vo.result.system;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.Entity;
import top.kx.basic.interfaces.echo.EchoVO;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * 实体类
 * 字典项
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-04
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "DefDictItemResultVO", description = "字典项")
public class DefDictItemResultVO extends Entity<Long> implements Serializable, EchoVO {

    private static final long serialVersionUID = 1L;
    private Map<String, Object> echoMap = new HashMap<>();

    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 字典ID
     */
    @ApiModelProperty(value = "字典ID")
    @Excel(name = "字典ID")
    private Long parentId;
    /**
     * 父字典标识
     */
    @ApiModelProperty(value = "父字典标识")
    @Excel(name = "父字典标识")
    private String parentKey;
    /**
     * 分类;[10-系统字典 20-业务字典]@Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Tenant.DICT_CLASSIFY)
     */
    @ApiModelProperty(value = "分类")
    @Excel(name = "分类")
    private String classify;
    /**
     * 标识
     */
    @ApiModelProperty(value = "标识")
    @Excel(name = "标识")
    private String key;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @Excel(name = "名称")
    private String name;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    @Excel(name = "状态", replace = {"是_true", "否_false", "_null"})
    private Boolean state;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Excel(name = "备注")
    private String remark;
    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    @Excel(name = "排序")
    private Integer sortValue;
    /**
     * 图标
     */
    @ApiModelProperty(value = "图标")
    @Excel(name = "图标")
    private String icon;
    /**
     * css样式
     */
    @ApiModelProperty(value = "css样式")
    @Excel(name = "css样式")
    private String cssStyle;
    /**
     * css类元素
     */
    @ApiModelProperty(value = "css类元素")
    @Excel(name = "css类元素")
    private String cssClass;
}
