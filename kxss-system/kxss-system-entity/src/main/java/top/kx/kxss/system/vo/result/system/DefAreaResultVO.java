package top.kx.kxss.system.vo.result.system;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.hutool.core.map.MapUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import top.kx.basic.annotation.echo.Echo;
import top.kx.basic.base.entity.TreeEntity;
import top.kx.basic.interfaces.echo.EchoVO;
import top.kx.kxss.model.constant.EchoApi;
import top.kx.kxss.model.constant.EchoDictType;

import java.io.Serializable;
import java.util.Map;


/**
 * <p>
 * 实体类
 * 地区表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "DefAreaResultVO", description = "地区表")
public class DefAreaResultVO extends TreeEntity<DefAreaResultVO, Long> implements Serializable, EchoVO {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "名称")
    protected String name;
    @ApiModelProperty(value = "父ID")
    protected Long parentId;
    @ApiModelProperty(value = "排序号")
    protected Integer sortValue;
    private Map<String, Object> echoMap = MapUtil.newHashMap();
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @Excel(name = "编码")
    private String code;
    /**
     * 全名
     */
    @ApiModelProperty(value = "全名")
    @Excel(name = "全名")
    private String fullName;
    /**
     * 经度
     */
    @ApiModelProperty(value = "经度")
    @Excel(name = "经度")
    private String longitude;
    /**
     * 维度
     */
    @ApiModelProperty(value = "维度")
    @Excel(name = "维度")
    private String latitude;
    /**
     * 行政级别;[10-国家 20-省份/直辖市 30-地市 40-区县 50-乡镇]@Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Global.AREA_LEVEL)
     */
    @ApiModelProperty(value = "行政级别")
    @Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Global.AREA_LEVEL)
    @Excel(name = "行政级别")
    private String level;
    /**
     * 数据来源;[10-爬取 20-新增]	@Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Tenant.AREA_SOURCE)
     */
    @ApiModelProperty(value = "数据来源")
    @Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.System.AREA_SOURCE)
    @Excel(name = "数据来源")
    private String source;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    @Excel(name = "状态", replace = {"是_true", "否_false", "_null"})
    private Boolean state;
    /**
     * 树层级
     */
    @ApiModelProperty(value = "树层级")
    @Excel(name = "树层级")
    private Integer treeGrade;
    /**
     * 路径
     */
    @ApiModelProperty(value = "路径")
    @Excel(name = "路径")
    private String treePath;
}
