package top.kx.kxss.system.vo.result.application;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import top.kx.basic.annotation.echo.Echo;
import top.kx.basic.base.entity.TreeEntity;
import top.kx.basic.interfaces.echo.EchoVO;
import top.kx.kxss.model.constant.EchoApi;
import top.kx.kxss.model.constant.EchoDictType;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 实体类
 * 资源
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "DefResourceResultVO", description = "资源")
public class DefResourceResultVO extends TreeEntity<DefResourceResultVO, Long> implements Serializable, EchoVO {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "名称")
    protected String name;
    @ApiModelProperty(value = "父ID")
    protected Long parentId;
    @ApiModelProperty(value = "排序号")
    protected Integer sortValue;
    private Map<String, Object> echoMap = new HashMap<>();
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * 应用ID;#def_application
     */
    @ApiModelProperty(value = "应用ID")
    @Excel(name = "应用ID")
    private Long applicationId;
    /**
     * 编码;唯一编码，用于区分资源
     */
    @ApiModelProperty(value = "编码")
    @Excel(name = "编码")
    private String code;
    /**
     * 类型;[20-菜单 30-视图 40-按钮 50-字段 06-数据]@Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Tenant.RESOURCE_TYPE)
     * 菜单即左侧显示的菜单，视图即隐藏的菜单(需要配置在路由中)
     */
    @ApiModelProperty(value = "类型")
    @Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.System.RESOURCE_TYPE)
    @Excel(name = "类型")
    private String resourceType;
    /**
     * 打开方式 [01-组件 02-内链 03-外链]
     */
    @ApiModelProperty(value = "打开方式")
    @Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.System.RESOURCE_OPEN_WITH)
    private String openWith;
    /**
     * 描述;resource_type=接口时表示接口说明
     */
    @ApiModelProperty(value = "描述")
    @Excel(name = "描述")
    private String describe;
    /**
     * 地址栏路径;用于resource_type=菜单和视图和接口.resource_type=菜单和视图，表示地址栏地址, http开头表示外链, is_frame_src 为true表示在框架类打开.resource_type=接口，表示后端接口请求地址.
     */
    @ApiModelProperty(value = "地址栏路径")
    @Excel(name = "地址栏路径")
    private String path;
    /**
     * 页面路径;用于resource_type=菜单和视图.
     * 前端页面在src/views目录下的相对地址.
     */
    @ApiModelProperty(value = "页面路径")
    @Excel(name = "页面路径")
    private String component;
    /**
     * 重定向;用于resource_type=菜单和视图
     */
    @ApiModelProperty(value = "重定向")
    @Excel(name = "重定向")
    private String redirect;
    /**
     * 图标
     */
    @ApiModelProperty(value = "图标")
    @Excel(name = "图标")
    private String icon;
    /**
     * 是否公共资源;1-无需分配所有人就可以访问的
     */
    @ApiModelProperty(value = "是否公共资源")
    @Excel(name = "是否公共资源", replace = {"是_true", "否_false", "_null"})
    private Boolean isGeneral;
    /**
     * 状态;[0-禁用 1-启用]
     */
    @ApiModelProperty(value = "状态")
    @Excel(name = "状态", replace = {"是_true", "否_false", "_null"})
    private Boolean state;
    /**
     * 分组
     */
    @ApiModelProperty(value = "分组")
    @Excel(name = "分组")
    private String subGroup;
    /**
     * 是否脱敏;显示时是否需要脱敏实现 (用于resource_type=字段)
     */
    @ApiModelProperty(value = "是否脱敏")
    @Excel(name = "是否脱敏", replace = {"是_true", "否_false", "_null"})
    private Boolean fieldIsSecret;
    /**
     * 是否可以编辑;是否可以编辑(用于resource_type=字段)
     */
    @ApiModelProperty(value = "是否可以编辑")
    @Excel(name = "是否可以编辑", replace = {"是_true", "否_false", "_null"})
    private Boolean fieldIsEdit;
    /**
     * 数据范围;[01-全部 02-本单位及子级 03-本单位 04-本部门 05-本部门及子级 06-个人 07-自定义]
     */
    @ApiModelProperty(value = "数据范围")
    @Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.System.RESOURCE_DATA_SCOPE)
    private String dataScope;
    /**
     * 实现类;自定义实现类全类名
     */
    @ApiModelProperty(value = "实现类")
    private String customClass;
    /**
     * 是否默认
     */
    @ApiModelProperty(value = "是否默认")
    private Boolean isDef;
    /**
     * 元数据;菜单视图的元数据
     */
    @ApiModelProperty(value = "元数据")
    @Excel(name = "元数据")
    private String metaJson;
    /**
     * 树层级
     */
    @ApiModelProperty(value = "树层级")
    private Integer treeGrade;
    /**
     * 树路径;用id拼接树结构
     */
    @ApiModelProperty(value = "树路径")
    private String treePath;


    @ApiModelProperty(value = "资源接口")
    private List<DefResourceApiResultVO> resourceApiList;
}
