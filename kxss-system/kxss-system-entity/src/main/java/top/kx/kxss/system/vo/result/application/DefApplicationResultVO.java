package top.kx.kxss.system.vo.result.application;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import top.kx.basic.annotation.echo.Echo;
import top.kx.basic.base.entity.Entity;
import top.kx.basic.interfaces.echo.EchoVO;
import top.kx.kxss.model.constant.EchoApi;
import top.kx.kxss.model.constant.EchoDictType;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * 实体类
 * 应用
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "DefApplicationResultVO", description = "应用")
public class DefApplicationResultVO extends Entity<Long> implements Serializable, EchoVO {

    private static final long serialVersionUID = 1L;
    private Map<String, Object> echoMap = new HashMap<>();

    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 应用标识
     */
    @ApiModelProperty(value = "应用标识")
    @Excel(name = "应用标识")
    private String appKey;
    /**
     * 应用秘钥
     */
    @ApiModelProperty(value = "应用秘钥")
    @Excel(name = "应用秘钥")
    private String appSecret;
    /**
     * 应用名称
     */
    @ApiModelProperty(value = "应用名称")
    @Excel(name = "应用名称")
    private String name;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    @Excel(name = "版本")
    private String version;
    /**
     * 应用类型;[10-自建应用 20-第三方应用]
     *
     * @Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Tenant.APPLICATION_TYPE)
     */
    @ApiModelProperty(value = "应用类型")
    @Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.System.APPLICATION_TYPE)
    @Excel(name = "应用类型")
    private String type;
    /**
     * 简介
     */
    @ApiModelProperty(value = "简介")
    @Excel(name = "简介")
    private String introduce;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Excel(name = "备注")
    private String remark;
    /**
     * 应用地址
     */
    @ApiModelProperty(value = "应用地址")
    @Excel(name = "应用地址")
    private String url;
    /**
     * 是否可见;0-否 1-是
     */
    @ApiModelProperty(value = "是否可见")
    @Excel(name = "是否可见", replace = {"是_true", "否_false", "_null"})
    private Boolean isVisible;
    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    @Excel(name = "排序")
    private Integer sortValue;
    /**
     * 是否公共应用;0-否 1-是
     */
    @ApiModelProperty(value = "是否公共应用")
    private Boolean isGeneral;

    /**
     * 过期时间
     */
    @ApiModelProperty(value = "过期时间")
    private LocalDateTime expirationTime;

    /**
     * 过期状态 0-过期 1-有效
     */
    @ApiModelProperty("过期状态 0-过期 1-有效")
    private String state;

}
