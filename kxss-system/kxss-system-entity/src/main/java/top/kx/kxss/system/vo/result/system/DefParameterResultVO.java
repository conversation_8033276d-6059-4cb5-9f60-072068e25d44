package top.kx.kxss.system.vo.result.system;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.hutool.core.map.MapUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.Entity;
import top.kx.basic.interfaces.echo.EchoVO;

import java.io.Serializable;
import java.util.Map;

/**
 * <p>
 * 实体类
 * 参数配置
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-13
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "DefParameterResultVO", description = "参数配置")
public class DefParameterResultVO extends Entity<Long> implements Serializable, EchoVO {

    private static final long serialVersionUID = 1L;
    private Map<String, Object> echoMap = MapUtil.newHashMap();

    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 参数键
     */
    @ApiModelProperty(value = "参数键")
    @Excel(name = "参数键")
    private String key;
    /**
     * 参数值
     */
    @ApiModelProperty(value = "参数值")
    @Excel(name = "参数值")
    private String value;
    /**
     * 参数名称
     */
    @ApiModelProperty(value = "参数名称")
    @Excel(name = "参数名称")
    private String name;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Excel(name = "备注")
    private String remarks;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    @Excel(name = "状态", replace = {"是_true", "否_false", "_null"})
    private Boolean state;
    /**
     * 类型;[10-系统参数 20-业务参数]@Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Tenant.PARAMETER_TYPE)
     */
    @ApiModelProperty(value = "类型")
    @Excel(name = "类型")
    private String paramType;
}
