package top.kx.kxss.system.vo.query.system;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 实体类
 * 客户端
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-13
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "DefClientPageQuery", description = "客户端")
public class DefClientPageQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 客户端ID
     */
    @ApiModelProperty(value = "客户端ID")
    private String clientId;
    /**
     * 客户端密码
     */
    @ApiModelProperty(value = "客户端密码")
    private String clientSecret;
    /**
     * 客户端名称
     */
    @ApiModelProperty(value = "客户端名称")
    private String name;
    /**
     * 类型;[10-WEB网站;15-移动端应用;20-手机H5网页;25-内部服务; 30-第三方应用]	@Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Tenant.CLIENT_TYPE)
     */
    @ApiModelProperty(value = "类型")
    private String type;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remarks;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Boolean state;

    /**
     * token有效期(秒) -1为不失效
     */
    @ApiModelProperty(value = "token有效期(秒) -1为不失效")
    private Long expireMillis;

}
