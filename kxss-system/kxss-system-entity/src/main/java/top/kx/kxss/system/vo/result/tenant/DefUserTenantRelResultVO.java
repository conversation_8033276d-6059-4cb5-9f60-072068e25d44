package top.kx.kxss.system.vo.result.tenant;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.hutool.core.map.MapUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.Entity;
import top.kx.basic.interfaces.echo.EchoVO;

import java.io.Serializable;
import java.util.Map;

/**
 * <p>
 * 实体类
 * 员工
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "DefUserTenantRelResultVO", description = "员工")
public class DefUserTenantRelResultVO extends Entity<Long> implements Serializable, EchoVO {

    private static final long serialVersionUID = 1L;
    private Map<String, Object> echoMap = MapUtil.newHashMap();

    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 是否默认员工;[0-否 1-是]
     */
    @ApiModelProperty(value = "是否默认员工")
    @Excel(name = "是否默认员工", replace = {"是_true", "否_false", "_null"})
    private Boolean isDefault;
    /**
     * 用户
     */
    @ApiModelProperty(value = "用户")
    @Excel(name = "用户")
    private Long userId;
    /**
     * 状态;[0-禁用 1-启用]
     */
    @ApiModelProperty(value = "状态")
    @Excel(name = "状态", replace = {"是_true", "否_false", "_null"})
    private Boolean state;
    /**
     * 所属企业
     */
    @ApiModelProperty(value = "所属企业")
    @Excel(name = "所属企业")
    private Long tenantId;

    @ApiModelProperty(value = "企业状态")
    private Boolean tenantState;
    @ApiModelProperty(value = "企业编码")
    private String tenantCode;

}
