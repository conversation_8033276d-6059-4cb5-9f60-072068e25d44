package top.kx.kxss.system.vo.update.tenant;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <p>
 * 实体类
 * 用户
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "DefUserBaseInfoUpdateVO", description = "用户修改自己的基础信息实体")
public class DefUserBaseInfoSelfUpdateVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 昵称
     */
    @ApiModelProperty(value = "昵称")
    @NotEmpty(message = "请填写昵称")
    @Size(max = 255, message = "昵称长度不能超过{max}")
    private String nickName;


    /**
     * 性别;
     */
    @ApiModelProperty(value = "性别")
    @Size(max = 1, message = "性别长度不能超过{max}")
    private String sex;

}
