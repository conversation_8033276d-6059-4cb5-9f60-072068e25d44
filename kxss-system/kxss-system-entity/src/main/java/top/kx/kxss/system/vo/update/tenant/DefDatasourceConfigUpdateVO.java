package top.kx.kxss.system.vo.update.tenant;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.SuperEntity;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <p>
 * 实体类
 * 数据源
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "DefDatasourceConfigUpdateVO", description = "数据源")
public class DefDatasourceConfigUpdateVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotNull(message = "请填写主键", groups = SuperEntity.Update.class)
    private Long id;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @NotEmpty(message = "请填写名称")
    @Size(max = 255, message = "名称长度不能超过255")
    private String name;


    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名")
    @NotEmpty(message = "请填写用户名")
    @Size(max = 255, message = "用户名长度不能超过255")
    private String username;
    /**
     * 密码
     */
    @ApiModelProperty(value = "密码")
    @NotEmpty(message = "请填写密码")
    @Size(max = 255, message = "密码长度不能超过255")
    private String password;
    /**
     * 链接
     */
    @ApiModelProperty(value = "链接")
    @NotEmpty(message = "请填写链接")
    @Size(max = 255, message = "链接长度不能超过255")
    private String url;
    /**
     * 驱动
     */
    @ApiModelProperty(value = "驱动")
    @NotEmpty(message = "请填写驱动")
    @Size(max = 255, message = "驱动长度不能超过255")
    private String driverClassName;
}
