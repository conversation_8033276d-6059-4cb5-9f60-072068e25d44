package top.kx.kxss.system.vo.update.tenant;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import top.kx.kxss.model.vo.save.AppendixSaveVO;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <p>
 * 用户头像
 * </p>
 *
 * <AUTHOR>
 * @since 2019-11-04
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "DefUserAvatarUpdateVO", description = "用户头像")
public class DefUserAvatarUpdateVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotNull(message = "id不能为空")
    private Long id;

    /**
     * 上传的头像
     */
    @ApiModelProperty(value = "上传的头像")
    @Valid
    private AppendixSaveVO appendixAvatar;

}
