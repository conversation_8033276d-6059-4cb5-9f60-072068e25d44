package top.kx.kxss.msg.vo.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;


/**
 * <p>
 * 表单查询条件VO
 * 接口属性
 * </p>
 *
 * <AUTHOR>
 * @date 2022-07-04 15:51:37
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode
@Builder
@ApiModel(value = "DefInterfacePropertyPageQuery", description = "接口属性")
public class DefInterfacePropertyPageQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    private Long id;

    /**
     * 接口ID
     */
    @ApiModelProperty(value = "接口ID")
    private Long interfaceId;
    /**
     * 参数名称
     */
    @ApiModelProperty(value = "参数名称")
    private String name;
    /**
     * 参数键
     */
    @ApiModelProperty(value = "参数键")
    private String key;
    /**
     * 参数值
     */
    @ApiModelProperty(value = "参数值")
    private String value;
    /**
     * 顺序号
     */
    @ApiModelProperty(value = "顺序号")
    private Integer sortValue;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remarks;


}
