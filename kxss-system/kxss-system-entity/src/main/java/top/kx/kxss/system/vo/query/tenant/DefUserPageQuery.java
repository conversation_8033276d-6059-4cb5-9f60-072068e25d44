package top.kx.kxss.system.vo.query.tenant;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 实体类
 * 用户
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-09
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "DefUserPageQuery", description = "用户")
public class DefUserPageQuery implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 用户名;大小写数字下划线
     */
    @ApiModelProperty(value = "用户名")
    private String username;
    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String nickName;
    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    private String email;
    /**
     * 手机;1开头11位纯数字
     */
    @ApiModelProperty(value = "手机")
    private String mobile;
    /**
     * 身份证;15或18位
     */
    @ApiModelProperty(value = "身份证")
    private String idCard;
    /**
     * 微信OpenId
     */
    @ApiModelProperty(value = "微信OpenId")
    private String wxOpenId;
    /**
     * 员工微信OpenId
     */
    @ApiModelProperty(value = "员工微信OpenId")
    private String empOpenId;
    /**
     * 钉钉OpenId
     */
    @ApiModelProperty(value = "钉钉OpenId")
    private String ddOpenId;
    /**
     * 内置;[0-否 1-是]
     */
    @ApiModelProperty(value = "内置")
    private Boolean readonly;
    /**
     * 性别;
     */
    @ApiModelProperty(value = "性别")
    private String sex;
    /**
     * 民族;[01-汉族 99-其他]	@Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Global.NATION)
     */
    @ApiModelProperty(value = "民族")
    private List<String> nation;
    /**
     * 学历;[01-小学 02-中学 03-高中 04-专科 05-本科 06-硕士 07-博士 08-博士后 99-其他]	@Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Global.EDUCATION)
     */
    @ApiModelProperty(value = "学历")
    private List<String> education;
    /**
     * 状态;[0-禁用 1-启用]
     */
    @ApiModelProperty(value = "状态")
    private Boolean state;
    /**
     * 工作描述
     */
    @ApiModelProperty(value = "工作描述")
    private String workDescribe;
    /**
     * 最后一次输错密码时间
     */
    @ApiModelProperty(value = "最后一次输错密码时间")
    private LocalDateTime passwordErrorLastTime;
    /**
     * 密码错误次数
     */
    @ApiModelProperty(value = "密码错误次数")
    private Integer passwordErrorNum;
    /**
     * 密码过期时间
     */
    @ApiModelProperty(value = "密码过期时间")
    private LocalDateTime passwordExpireTime;
    /**
     * 密码
     */
    @ApiModelProperty(value = "密码")
    private String password;
    /**
     * 密码盐
     */
    @ApiModelProperty(value = "密码盐")
    private String salt;
    /**
     * 最后登录时间
     */
    @ApiModelProperty(value = "最后登录时间")
    private LocalDateTime lastLoginTime;


    @ApiModelProperty(value = "企业ID")
    private Long tenantId;


}
