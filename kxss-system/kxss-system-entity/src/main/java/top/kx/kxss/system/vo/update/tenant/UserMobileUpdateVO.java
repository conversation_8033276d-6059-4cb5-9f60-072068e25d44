package top.kx.kxss.system.vo.update.tenant;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.annotation.constraints.NotEmptyPattern;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

import static top.kx.basic.utils.ValidatorUtil.REGEX_MOBILE;

/**
 * <p>
 * 用户手机修改VO
 * </p>
 *
 * <AUTHOR>
 * @since 2019-11-04
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "UserMobileUpdateVO", description = "用户手机修改VO")
public class UserMobileUpdateVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 手机;1开头11位纯数字
     */
    @ApiModelProperty(value = "手机")
    @Size(max = 11, message = "手机长度不能超过{max}")
    @NotEmptyPattern(regexp = REGEX_MOBILE, message = "请输入11位的手机号")
    @NotEmpty(message = "请填写手机")
    private String mobile;


    @ApiModelProperty(value = "验证码")
    @NotNull(message = "请选择用户ID")
    private Long id;

}
