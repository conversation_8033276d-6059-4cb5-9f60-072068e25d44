package top.kx.kxss.system.vo.result.application;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.Entity;
import top.kx.basic.interfaces.echo.EchoVO;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * 实体类
 * 资源接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "DefResourceApiResultVO", description = "资源接口")
public class DefResourceApiResultVO extends Entity<Long> implements Serializable, EchoVO {

    private static final long serialVersionUID = 1L;
    private Map<String, Object> echoMap = new HashMap<>();

    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 资源ID
     */
    @ApiModelProperty(value = "资源ID")
    @Excel(name = "资源ID")
    private Long resourceId;
    /**
     * 控制器类名
     */
    @ApiModelProperty(value = "控制器类名")
    @Excel(name = "控制器类名")
    private String controller;
    /**
     * 所属服务;取配置文件中 spring.application.name
     */
    @ApiModelProperty(value = "所属服务")
    @Excel(name = "所属服务")
    private String springApplicationName;
    /**
     * 请求类型
     */
    @ApiModelProperty(value = "请求类型")
    @Excel(name = "请求类型")
    private String requestMethod;
    /**
     * 接口名;接口上的注释
     */
    @ApiModelProperty(value = "接口名")
    @Excel(name = "接口名")
    private String name;
    /**
     * 接口路径;lamp-cloud版：uri需要拼接上gateway中路径前缀
     * lamp-boot版: uri需要不需要拼接前缀
     */
    @ApiModelProperty(value = "接口路径")
    @Excel(name = "接口路径")
    private String uri;

    private Boolean isInput;
}
