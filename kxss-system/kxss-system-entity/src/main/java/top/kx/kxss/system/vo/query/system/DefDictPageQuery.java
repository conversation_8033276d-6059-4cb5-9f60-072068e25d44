package top.kx.kxss.system.vo.query.system;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 实体类
 * 字典
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-04
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "DefDictPageQuery", description = "字典")
public class DefDictPageQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分类;[10-系统字典 20-业务字典]@Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Tenant.DICT_CLASSIFY)
     */
    @ApiModelProperty(value = "分类")
    private List<String> classify;
    /**
     * 标识
     */
    @ApiModelProperty(value = "标识")
    private String key;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private List<Boolean> state;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
}
