package top.kx.kxss.system.service.application.impl;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.kxss.system.service.application.DefUserApplicationService;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.kxss.system.entity.application.DefUserApplication;
import top.kx.kxss.system.manager.application.DefUserApplicationManager;
import top.kx.kxss.system.vo.query.application.DefUserApplicationPageQuery;
import top.kx.kxss.system.vo.result.application.DefUserApplicationResultVO;
import top.kx.kxss.system.vo.save.application.DefUserApplicationSaveVO;
import top.kx.kxss.system.vo.update.application.DefUserApplicationUpdateVO;

/**
 * <p>
 * 业务实现类
 * 用户的默认应用
 * </p>
 *
 * <AUTHOR>
 * @date 2022-03-06
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)

public class DefUserApplicationServiceImpl extends SuperServiceImpl<DefUserApplicationManager, Long, DefUserApplication, DefUserApplicationSaveVO, DefUserApplicationUpdateVO, DefUserApplicationPageQuery, DefUserApplicationResultVO> implements DefUserApplicationService {
    @Override
    public Long getMyDefAppByUserId(Long userId) {
        DefUserApplication userApplication = superManager.getOne(Wraps.<DefUserApplication>lbQ().eq(DefUserApplication::getUserId, userId), false);
        return userApplication != null ? userApplication.getApplicationId() : null;
    }
}
