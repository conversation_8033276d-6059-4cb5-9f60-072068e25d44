package top.kx.kxss.system.manager.application.impl;

import cn.hutool.core.convert.Convert;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.manager.impl.SuperCacheManagerImpl;
import top.kx.basic.cache.redis2.CacheResult;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.model.cache.CacheKey;
import top.kx.basic.model.cache.CacheKeyBuilder;
import top.kx.kxss.common.cache.tenant.application.ResourceApiCacheKeyBuilder;
import top.kx.kxss.common.cache.tenant.application.ResourceResourceApiCacheKeyBuilder;
import top.kx.kxss.system.entity.application.DefResourceApi;
import top.kx.kxss.system.manager.application.DefResourceApiManager;
import top.kx.kxss.system.mapper.application.DefResourceApiMapper;

import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 应用管理
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2021/9/29 1:26 下午
 * @create [2021/9/29 1:26 下午 ] [tangyh] [初始创建]
 */
@RequiredArgsConstructor
@Service
public class DefResourceApiManagerImpl extends SuperCacheManagerImpl<DefResourceApiMapper, DefResourceApi> implements DefResourceApiManager {
    @Override
    protected CacheKeyBuilder cacheKeyBuilder() {
        return new ResourceApiCacheKeyBuilder();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeByResourceId(List<Long> resourceIdList) {
        LbQueryWrap<DefResourceApi> wrap = Wraps.<DefResourceApi>lbQ().select(DefResourceApi::getId).in(DefResourceApi::getResourceId, resourceIdList);
        List<Long> apiIds = listObjs(wrap, Convert::toLong);
        remove(wrap);

        CacheKey[] keys = apiIds.stream().map(ResourceApiCacheKeyBuilder::builder).toArray(CacheKey[]::new);
        cacheOps.del(keys);

        CacheKey[] resourceResourceApiKeys = resourceIdList.stream().map(ResourceResourceApiCacheKeyBuilder::builder).toArray(CacheKey[]::new);
        cacheOps.del(resourceResourceApiKeys);
    }

    @Override
    public List<DefResourceApi> findByResourceId(Long resourceId) {
        return list(Wraps.<DefResourceApi>lbQ().eq(DefResourceApi::getResourceId, resourceId));
    }

    @Override
    public List<DefResourceApi> findApiByResourceId(List<Long> resourceIdList) {
        Set<Long> apiIds = new HashSet<>();
        for (Long resourceId : resourceIdList) {
            CacheKey key = ResourceResourceApiCacheKeyBuilder.builder(resourceId);
            CacheResult<List<Long>> result = cacheOps.get(key, k -> super.listObjs(
                    Wraps.<DefResourceApi>lbQ().select(DefResourceApi::getId).eq(DefResourceApi::getResourceId, resourceId),
                    Convert::toLong
            ));
            apiIds.addAll(result.asList());
        }
        List<DefResourceApi> apiList = findByIds(apiIds, null);
        return apiList.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }
}
