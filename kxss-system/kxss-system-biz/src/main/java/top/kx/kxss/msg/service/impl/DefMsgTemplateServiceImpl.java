package top.kx.kxss.msg.service.impl;

import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.msg.entity.DefMsgTemplate;
import top.kx.kxss.msg.manager.DefMsgTemplateManager;
import top.kx.kxss.msg.service.DefMsgTemplateService;
import top.kx.kxss.msg.vo.query.DefMsgTemplatePageQuery;
import top.kx.kxss.msg.vo.result.DefMsgTemplateResultVO;
import top.kx.kxss.msg.vo.save.DefMsgTemplateSaveVO;
import top.kx.kxss.msg.vo.update.DefMsgTemplateUpdateVO;

/**
 * <p>
 * 业务实现类
 * 消息模板
 * </p>
 *
 * <AUTHOR>
 * @date 2022-07-04 15:51:37
 * @create [2022-07-04 15:51:37] [zuihou] [代码生成器生成]
 */

@Slf4j
@RequiredArgsConstructor
@Service
@Transactional(readOnly = true)
public class DefMsgTemplateServiceImpl extends SuperServiceImpl<DefMsgTemplateManager, Long, DefMsgTemplate, DefMsgTemplateSaveVO,
        DefMsgTemplateUpdateVO, DefMsgTemplatePageQuery, DefMsgTemplateResultVO> implements DefMsgTemplateService {
    @Override
    public DefMsgTemplate getByCode(String code) {
        return superManager.getByCode(code);
    }

    @Override
    public Boolean check(String code, Long id) {
        ArgumentAssert.notEmpty(code, "请填写模板标识");
        return superManager.count(Wraps.<DefMsgTemplate>lbQ().eq(DefMsgTemplate::getCode, code)
                .ne(DefMsgTemplate::getId, id)) > 0;
    }

    @Override
    protected DefMsgTemplate saveBefore(DefMsgTemplateSaveVO extendMsgTemplateSaveVO) {
        ArgumentAssert.isFalse(StrUtil.isNotBlank(extendMsgTemplateSaveVO.getCode()) &&
                check(extendMsgTemplateSaveVO.getCode(), null), "模板标识{}已存在", extendMsgTemplateSaveVO.getCode());

        return super.saveBefore(extendMsgTemplateSaveVO);
    }

    @Override
    protected DefMsgTemplate updateBefore(DefMsgTemplateUpdateVO extendMsgTemplateUpdateVO) {
        ArgumentAssert.isFalse(StrUtil.isNotBlank(extendMsgTemplateUpdateVO.getCode()) &&
                        check(extendMsgTemplateUpdateVO.getCode(), extendMsgTemplateUpdateVO.getId()),
                "模板标识{}已存在", extendMsgTemplateUpdateVO.getCode());

        return super.updateBefore(extendMsgTemplateUpdateVO);
    }
}


