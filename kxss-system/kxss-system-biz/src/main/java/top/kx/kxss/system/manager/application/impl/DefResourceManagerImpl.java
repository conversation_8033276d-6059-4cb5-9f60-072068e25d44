package top.kx.kxss.system.manager.application.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import top.kx.basic.base.manager.impl.SuperCacheManagerImpl;
import top.kx.basic.cache.redis2.CacheResult;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.model.cache.CacheKey;
import top.kx.basic.model.cache.CacheKeyBuilder;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.TreeUtil;
import top.kx.kxss.common.cache.tenant.application.ApplicationResourceCacheKeyBuilder;
import top.kx.kxss.common.cache.tenant.application.ResourceCacheKeyBuilder;
import top.kx.kxss.system.entity.application.DefResource;
import top.kx.kxss.system.manager.application.DefResourceManager;
import top.kx.kxss.system.mapper.application.DefResourceMapper;

import java.io.Serializable;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 应用管理
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2021/9/29 1:26 下午
 * @create [2021/9/29 1:26 下午 ] [tangyh] [初始创建]
 */
@RequiredArgsConstructor
@Service
public class DefResourceManagerImpl extends SuperCacheManagerImpl<DefResourceMapper, DefResource> implements DefResourceManager {


    @Override
    protected CacheKeyBuilder cacheKeyBuilder() {
        return new ResourceCacheKeyBuilder();
    }

    @Override
    public List<DefResource> findResourceListByApplicationId(List<Long> applicationIdList, final Collection<String> resourceTypeList) {
        Set<Long> resourceIdSet = new HashSet<>();
        if (CollUtil.isEmpty(applicationIdList)) {
            CacheKey key = ApplicationResourceCacheKeyBuilder.build(null);
            LbQueryWrap<DefResource> wrap = Wraps.<DefResource>lbQ().select(DefResource::getId).eq(DefResource::getState, true);
            CacheResult<List<Long>> resourceResults = cacheOps.get(key, k -> super.listObjs(wrap, Convert::toLong));
            resourceIdSet.addAll(resourceResults.asList());
        } else {
            for (Long applicationId : applicationIdList) {
                CacheKey key = ApplicationResourceCacheKeyBuilder.build(applicationId);
                LbQueryWrap<DefResource> wrap = Wraps.<DefResource>lbQ().select(DefResource::getId).eq(DefResource::getApplicationId, applicationId).eq(DefResource::getState, true);
                CacheResult<List<Long>> resourceResults = cacheOps.get(key, k -> super.listObjs(wrap, Convert::toLong));
                resourceIdSet.addAll(resourceResults.asList());
            }
        }
        return findByIdsAndType(resourceIdSet, resourceTypeList);
    }

    @Override
    public List<DefResource> findByIdsAndType(Collection<? extends Serializable> idList, Collection<String> types) {
        List<DefResource> list = findByIds(idList, null);
        return list.stream()
                // 过滤数据状态
                .filter(Objects::nonNull).filter(DefResource::getState).filter(item -> !CollUtil.isNotEmpty(types) || (CollUtil.contains(types, item.getResourceType())))
                // 按sortValue排序，null排在最后
                .sorted(Comparator.comparing(DefResource::getSortValue, Comparator.nullsLast(Integer::compareTo))).collect(Collectors.toList());
    }

    @Override
    public List<DefResource> findByApplicationId(List<Long> applicationIds) {
        ArgumentAssert.notEmpty(applicationIds, "applicationIds 不能为空");
        return list(Wraps.<DefResource>lbQ().in(DefResource::getApplicationId, applicationIds).orderByAsc(DefResource::getSortValue));
    }

    @Override
    public List<DefResource> findChildrenByParentId(Long parentId) {
        ArgumentAssert.notNull(parentId, "parentId 不能为空");
        return list(Wraps.<DefResource>lbQ().in(DefResource::getParentId, parentId).orderByAsc(DefResource::getSortValue));
    }

    @Override
    public List<DefResource> findAllChildrenByParentId(Long parentId) {
        ArgumentAssert.notNull(parentId, "parentId 不能为空");
        return list(Wraps.<DefResource>lbQ().like(DefResource::getTreePath, TreeUtil.buildTreePath(parentId)).orderByAsc(DefResource::getSortValue));
    }


}
