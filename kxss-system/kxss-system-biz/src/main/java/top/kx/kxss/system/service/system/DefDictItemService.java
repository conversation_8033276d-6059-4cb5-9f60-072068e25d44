package top.kx.kxss.system.service.system;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.system.entity.system.DefDict;
import top.kx.kxss.system.vo.query.system.DefDictItemPageQuery;
import top.kx.kxss.system.vo.result.system.DefDictItemResultVO;
import top.kx.kxss.system.vo.save.system.DefDictItemSaveVO;
import top.kx.kxss.system.vo.update.system.DefDictItemUpdateVO;

import java.util.List;

/**
 * <p>
 * 业务接口
 * 字典
 * </p>
 *
 * <AUTHOR>
 * @date 2021-10-04
 */
public interface DefDictItemService extends SuperService<Long, DefDict, DefDictItemSaveVO, DefDictItemUpdateVO, DefDictItemPageQuery, DefDictItemResultVO> {

    /**
     * 检查字典项标识是否可用
     *
     * @param key    标识
     * @param dictId 所属字典id
     * @param id     当前id
     * @return
     */
    boolean checkItemByKey(String key, Long dictId, Long id);

    /**
     * 根据
     * @param key
     * @return
     */
    List<DefDictItemResultVO> findByDictKey(String key);
}
