package top.kx.kxss.msg.service.impl;

import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.msg.entity.DefInterface;
import top.kx.kxss.msg.entity.DefInterfaceProperty;
import top.kx.kxss.msg.enumeration.InterfaceExecModeEnum;
import top.kx.kxss.msg.manager.DefInterfaceManager;
import top.kx.kxss.msg.manager.DefInterfacePropertyManager;
import top.kx.kxss.msg.service.DefInterfaceService;
import top.kx.kxss.msg.vo.query.DefInterfacePageQuery;
import top.kx.kxss.msg.vo.result.DefInterfaceResultVO;
import top.kx.kxss.msg.vo.save.DefInterfaceSaveVO;
import top.kx.kxss.msg.vo.update.DefInterfaceUpdateVO;

import java.util.Collection;

/**
 * <p>
 * 业务实现类
 * 接口
 * </p>
 *
 * <AUTHOR>
 * @date 2022-07-04 16:45:45
 * @create [2022-07-04 16:45:45] [zuihou] [代码生成器生成]
 */

@Slf4j
@RequiredArgsConstructor
@Service
@Transactional(readOnly = true)
public class DefInterfaceServiceImpl extends SuperServiceImpl<DefInterfaceManager, Long, DefInterface, DefInterfaceSaveVO,
        DefInterfaceUpdateVO, DefInterfacePageQuery, DefInterfaceResultVO> implements DefInterfaceService {
    private final DefInterfacePropertyManager defInterfacePropertyManager;

    @Override
    public Boolean check(String code, Long id) {
        ArgumentAssert.notEmpty(code, "请填写接口编码");
        return superManager.count(Wraps.<DefInterface>lbQ().eq(DefInterface::getCode, code)
                .ne(DefInterface::getId, id)) > 0;
    }

    @Override
    protected DefInterface saveBefore(DefInterfaceSaveVO saveVO) {
        ArgumentAssert.isFalse(StrUtil.isNotBlank(saveVO.getCode()) &&
                check(saveVO.getCode(), null), "接口编码{}已存在", saveVO.getCode());
        if (InterfaceExecModeEnum.IMPL_CLASS.eq(saveVO.getExecMode())) {
            ArgumentAssert.notEmpty(saveVO.getImplClass(), "请填写实现类");
        } else {
            ArgumentAssert.notEmpty(saveVO.getScript(), "请填写实现脚本");
        }
        return super.saveBefore(saveVO);
    }

    @Override
    protected DefInterface updateBefore(DefInterfaceUpdateVO updateVO) {
        ArgumentAssert.isFalse(StrUtil.isNotBlank(updateVO.getCode()) &&
                        check(updateVO.getCode(), updateVO.getId()),
                "接口编码{}已存在", updateVO.getCode());
        if (InterfaceExecModeEnum.IMPL_CLASS.eq(updateVO.getExecMode())) {
            ArgumentAssert.notEmpty(updateVO.getImplClass(), "请填写实现类");
        } else {
            ArgumentAssert.notEmpty(updateVO.getScript(), "请填写实现脚本");
        }
        return super.updateBefore(updateVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByIds(Collection<Long> idList) {
        defInterfacePropertyManager.remove(Wraps.<DefInterfaceProperty>lbQ().in(DefInterfaceProperty::getInterfaceId, idList));
        return super.removeByIds(idList);
    }
}


