package top.kx.kxss.system.service.tenant.impl;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.kxss.system.service.tenant.DefDatasourceConfigService;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.kxss.system.entity.tenant.DefDatasourceConfig;
import top.kx.kxss.system.manager.tenant.DefDatasourceConfigManager;
import top.kx.kxss.system.vo.query.tenant.DefDatasourceConfigPageQuery;
import top.kx.kxss.system.vo.result.tenant.DefDatasourceConfigResultVO;
import top.kx.kxss.system.vo.save.tenant.DefDatasourceConfigSaveVO;
import top.kx.kxss.system.vo.update.tenant.DefDatasourceConfigUpdateVO;

/**
 * <p>
 * 业务实现类
 * 数据源
 * </p>
 *
 * <AUTHOR>
 * @date 2021-09-13
 */
@Slf4j
@Service

@RequiredArgsConstructor
@Transactional(readOnly = true)
public class DefDatasourceConfigServiceImpl extends SuperServiceImpl<DefDatasourceConfigManager, Long, DefDatasourceConfig, DefDatasourceConfigSaveVO, DefDatasourceConfigUpdateVO, DefDatasourceConfigPageQuery, DefDatasourceConfigResultVO>
        implements DefDatasourceConfigService {

    @Override
    public Boolean testConnection(Long id) {
        return true;
    }
}
