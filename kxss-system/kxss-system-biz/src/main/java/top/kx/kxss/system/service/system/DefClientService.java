package top.kx.kxss.system.service.system;

import top.kx.basic.base.service.SuperCacheService;
import top.kx.kxss.system.entity.system.DefClient;
import top.kx.kxss.system.vo.query.system.DefClientPageQuery;
import top.kx.kxss.system.vo.result.system.DefClientResultVO;
import top.kx.kxss.system.vo.save.system.DefClientSaveVO;
import top.kx.kxss.system.vo.update.system.DefClientUpdateVO;

/**
 * <p>
 * 业务接口
 * 客户端
 * </p>
 *
 * <AUTHOR>
 * @date 2021-10-13
 */
public interface DefClientService extends SuperCacheService<Long, DefClient, DefClientSaveVO, DefClientUpdateVO, DefClientPageQuery, DefClientResultVO> {

    /**
     * 根据 客户端id 和 客户端秘钥查询应用
     *
     * @param clientId
     * @param clientSecret
     * @return
     */
    DefClient getClient(String clientId, String clientSecret);

    DefClient getByClientKey(String clientId);
}
