package top.kx.kxss.system.service.system.impl;

import cn.hutool.core.util.RandomUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.service.impl.SuperCacheServiceImpl;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.kxss.system.entity.system.DefClient;
import top.kx.kxss.system.manager.system.DefClientManager;
import top.kx.kxss.system.service.system.DefClientService;
import top.kx.kxss.system.vo.query.system.DefClientPageQuery;
import top.kx.kxss.system.vo.result.system.DefClientResultVO;
import top.kx.kxss.system.vo.save.system.DefClientSaveVO;
import top.kx.kxss.system.vo.update.system.DefClientUpdateVO;

/**
 * <p>
 * 业务实现类
 * 客户端
 * </p>
 *
 * <AUTHOR>
 * @date 2021-10-13
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)

public class DefClientServiceImpl extends SuperCacheServiceImpl<DefClientManager, Long, DefClient, DefClientSaveVO, DefClientUpdateVO, DefClientPageQuery, DefClientResultVO>
        implements DefClientService {

    @Override
    protected DefClient saveBefore(DefClientSaveVO defClientSaveVO) {
        DefClient defClient = super.saveBefore(defClientSaveVO);
        defClient.setClientId(RandomUtil.randomString(24));
        defClient.setClientSecret(RandomUtil.randomString(32));
        return defClient;
    }

    @Override
    public DefClient getClient(String clientId, String clientSecret) {
        return superManager.getClient(clientId, clientSecret);
    }

    @Override
    public DefClient getByClientKey(String clientId) {
        return superManager.getOne(Wraps.<DefClient>lbQ()
                .eq(DefClient::getClientId, clientId)
                .eq(DefClient::getState, 1)
                .eq(DefClient::getDeleteFlag, 0).last("limit 1"));
    }
}
