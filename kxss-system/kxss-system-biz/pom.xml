<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>kxss-system</artifactId>
        <groupId>top.kx.kxss</groupId>
        <version>4.13.1</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>kxss-system-biz</artifactId>
    <name>${project.artifactId}</name>
    <description>系统服务-业务模块</description>
    <dependencies>
        <dependency>
            <groupId>top.kx.kxss</groupId>
            <artifactId>kxss-system-entity</artifactId>
            <version>${kxss-project.version}</version>
        </dependency>
        <dependency>
            <groupId>top.kx.kxss</groupId>
            <artifactId>kxss-file-sdk</artifactId>
            <version>${kxss-project.version}</version>
        </dependency>
        <dependency>
            <groupId>top.kx.basic</groupId>
            <artifactId>kxss-echo-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>top.kx.basic</groupId>
            <artifactId>kxss-mq-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>top.kx.basic</groupId>
            <artifactId>kxss-databases</artifactId>
        </dependency>

        <dependency>
            <groupId>top.kx.basic</groupId>
            <artifactId>kxss-mvc</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus</artifactId>
        </dependency>

        <!-- @RefreshScope 需要使用 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-context</artifactId>
        </dependency>

    </dependencies>

</project>
