package top.kx.kxss.common.annotation;

import top.kx.kxss.model.enumeration.base.PasswordBusinessEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 密码验证注解
 * 用于标记需要密码验证的接口方法
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface PasswordValidation {

    /**
     * 业务模块代码
     * 用于标识不同的业务模块，对应数据库中的business_code字段
     */
    PasswordBusinessEnum businessCode();

    /**
     * 业务模块描述
     * 用于日志记录和错误提示
     */
    String description() default "";
}
