package top.kx.kxss.common.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;

/**
 * 获取微信小程序配置
 * <AUTHOR>
 */
@Data
@ConfigurationProperties(prefix = WxMaProperties.PREFIX)
public class WxMaProperties {
    public static final String PREFIX = "kxss.miniapp";
    private List<Config> configs;

    @Data
    public static class Config {
        /**
         * 设置微信小程序的appid
         */
        private String appid;

        /**
         * 设置微信小程序的Secret
         */
        private String secret;

        /**
         * 设置微信小程序消息服务器配置的token
         */
        private String token;

        /**
         * 设置微信小程序消息服务器配置的EncodingAESKey
         */
        private String aesKey;

        /**
         * 消息格式，XML或者JSON
         */
        private String msgDataFormat;

        /**
         * 小程序客户端id
         */
        private String clientId;

        /**
         *小程序客户端密码
         */
        private String clientSecret;
    }
}
