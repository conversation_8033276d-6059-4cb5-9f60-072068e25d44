package top.kx.kxss.common.cache.tenant.application;

import top.kx.basic.base.entity.SuperEntity;
import top.kx.basic.model.cache.CacheKey;
import top.kx.basic.model.cache.CacheKeyBuilder;
import top.kx.kxss.common.cache.CacheKeyModular;
import top.kx.kxss.common.cache.CacheKeyTable;

import java.time.Duration;

/**
 * 资源 KEY
 * [服务模块名:]业务类型[:业务字段][:value类型][:资源id] -> obj
 * tenant:def_resource:id:obj:1 -> {}
 * <p>
 * #def_resource
 *
 * <AUTHOR>
 * @date 2020/9/20 6:45 下午
 */
public class ResourceCacheKeyBuilder implements CacheKeyBuilder {
    public static CacheKey builder(Long id) {
        return new ResourceCacheKeyBuilder().key(id);
    }

    @Override
    public String getTenant() {
        return null;
    }

    @Override
    public String getPrefix() {
        return CacheKeyModular.PREFIX;
    }

    @Override
    public String getModular() {
        return CacheKeyModular.SYSTEM;
    }

    @Override
    public String getTable() {
        return CacheKeyTable.System.RESOURCE;
    }

    @Override
    public String getField() {
        return SuperEntity.ID_FIELD;
    }

    @Override
    public ValueType getValueType() {
        return ValueType.obj;
    }

    @Override
    public Duration getExpire() {
        return Duration.ofHours(24);
    }
}
