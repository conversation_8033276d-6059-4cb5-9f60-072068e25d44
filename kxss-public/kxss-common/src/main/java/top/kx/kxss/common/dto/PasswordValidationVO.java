package top.kx.kxss.common.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 密码验证通用基类
 * 需要密码验证的请求参数可以继承此类
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@ApiModel(value = "PasswordValidationDTO", description = "密码验证通用基类")
public class PasswordValidationVO {

    /**
     * 验证密码
     */
    @ApiModelProperty(value = "验证密码", required = true)
   // @NotBlank(message = "验证密码不能为空")
    private String password;
}
