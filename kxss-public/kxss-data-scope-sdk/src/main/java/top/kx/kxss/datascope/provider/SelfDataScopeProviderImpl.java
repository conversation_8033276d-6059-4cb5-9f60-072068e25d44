package top.kx.kxss.datascope.provider;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.kx.kxss.datascope.model.DataFieldProperty;
import top.kx.basic.base.entity.SuperEntity;
import top.kx.basic.context.ContextUtil;

import java.util.Collections;
import java.util.List;

/**
 * 个人的数据
 *
 * <AUTHOR>
 * @date 2022/1/9 23:29
 */
@Slf4j
@RequiredArgsConstructor
@Component("DATA_SCOPE_06")
public class SelfDataScopeProviderImpl implements DataScopeProvider {

    @Override
    public List<DataFieldProperty> findDataFieldProperty(List<DataFieldProperty> fsp) {
        List<Long> employeeIdList = Collections.singletonList(ContextUtil.getUserId());
        fsp.forEach(item -> {
            item.setField(SuperEntity.CREATED_BY_FIELD);
            item.setValues(employeeIdList);
        });
        return fsp;
    }
}
