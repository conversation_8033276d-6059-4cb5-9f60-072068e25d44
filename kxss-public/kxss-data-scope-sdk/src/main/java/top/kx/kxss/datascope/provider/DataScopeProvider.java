package top.kx.kxss.datascope.provider;

import top.kx.kxss.datascope.model.DataFieldProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/1/9 23:28
 */
public interface DataScopeProvider {
    /**
     * @param fsp fsp
     * @return java.util.List<top.kx.kxss.datascope.model.DataFieldProperty>
     * <AUTHOR>
     * @date 2022/10/28 4:41 PM
     * @create [2022/10/28 4:41 PM ] [tangyh] [初始创建]
     */
    List<DataFieldProperty> findDataFieldProperty(List<DataFieldProperty> fsp);
}
