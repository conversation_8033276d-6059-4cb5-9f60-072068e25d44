package top.kx.kxss.model.entity.base;

import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import top.kx.kxss.model.constant.Condition;
import top.kx.kxss.model.constant.EchoApi;
import top.kx.basic.annotation.echo.Echo;
import top.kx.basic.base.entity.Entity;
import top.kx.basic.interfaces.echo.EchoVO;

import java.io.Serializable;
import java.util.Map;

/**
 * <p>
 * 实体类
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2019-07-03
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode
@ApiModel(value = "SysPosition", description = "岗位")
@TableName("base_position")
public class SysPosition extends Entity<Long> implements Serializable, EchoVO {

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = MapUtil.newHashMap();

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @TableField(value = "name", condition = Condition.LIKE)
    private String name;
    /**
     * 所属组织;#base_org@Echo(api = EchoApi.ORG_ID_CLASS)
     */
    @ApiModelProperty(value = "组织")
    @Echo(api = EchoApi.ORG_ID_CLASS)
    @TableField(value = "org_id")
    private Long orgId;
    /**
     * 状态;0-禁用 1-启用
     */
    @ApiModelProperty(value = "状态")
    @TableField(value = "state")
    private Boolean state;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @TableField(value = "remarks", condition = Condition.LIKE)
    private String remarks;
    /**
     * 创建者机构
     */
    @ApiModelProperty(value = "创建者机构")
    @TableField(value = "created_org_id")
    private Long createdOrgId;

}
