package top.kx.kxss.model.entity.system;

import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import top.kx.basic.annotation.echo.Echo;
import top.kx.basic.base.entity.Entity;
import top.kx.basic.interfaces.echo.EchoVO;
import top.kx.kxss.model.constant.EchoApi;
import top.kx.kxss.model.constant.EchoDictType;
import top.kx.kxss.model.entity.base.SysEmployee;
import top.kx.kxss.model.entity.base.SysOrg;
import top.kx.kxss.model.entity.base.SysPosition;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import static top.kx.kxss.model.constant.Condition.LIKE;

/**
 * 用户实体
 * 字段基于 def_user 表, 可能还会扩展其他字段
 *
 * <AUTHOR>
 * @date 2022年04月13日12:30:28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Accessors(chain = true)
@ToString(callSuper = true)
@Builder
@ApiModel(value = "SysUser", description = "系统用户")
@TableName("def_user")
public class SysUser extends Entity<Long> implements Serializable, EchoVO {
    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = MapUtil.newHashMap();

    /**
     * 用户名;大小写数字下划线
     */
    @ApiModelProperty(value = "用户名")
    @TableField(value = "username", condition = LIKE)
    private String username;
    /**
     * 昵称
     */
    @ApiModelProperty(value = "昵称")
    @TableField(value = "nick_name", condition = LIKE)
    private String nickName;
    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    @TableField(value = "email", condition = LIKE)
    private String email;
    /**
     * 手机;1开头11位纯数字
     */
    @ApiModelProperty(value = "手机")
    @TableField(value = "mobile", condition = LIKE)
    private String mobile;
    /**
     * 身份证;15或18位
     */
    @ApiModelProperty(value = "身份证")
    @TableField(value = "id_card", condition = LIKE)
    private String idCard;
    /**
     * 微信OpenId
     */
    @ApiModelProperty(value = "微信OpenId")
    @TableField(value = "wx_open_id", condition = LIKE)
    private String wxOpenId;
    /**
     * 钉钉OpenId
     */
    @ApiModelProperty(value = "钉钉OpenId")
    @TableField(value = "dd_open_id", condition = LIKE)
    private String ddOpenId;
    /**
     * 内置;[0-否 1-是]
     */
    @ApiModelProperty(value = "内置")
    @TableField(value = "readonly")
    private Boolean readonly;
    /**
     * 性别;
     * #Sex{W:女;M:男;N:未知}
     */
    @ApiModelProperty(value = "性别")
    @Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Global.SEX)
    @TableField(value = "sex")
    private String sex;
    /**
     * 民族;[01-汉族 99-其他]	@Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Global.NATION)
     */
    @ApiModelProperty(value = "民族")
    @Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Global.NATION)
    @TableField(value = "nation", condition = LIKE)
    private String nation;
    /**
     * 学历;[01-小学 02-中学 03-高中 04-专科 05-本科 06-硕士 07-博士 08-博士后 99-其他]	@Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Global.EDUCATION)
     */
    @ApiModelProperty(value = "学历")
    @Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Global.EDUCATION)
    @TableField(value = "education", condition = LIKE)
    private String education;
    /**
     * 状态;[0-禁用 1-启用]
     */
    @ApiModelProperty(value = "状态")
    @TableField(value = "state")
    private Boolean state;

    /**
     * 工作描述
     */
    @ApiModelProperty(value = "工作描述")
    @TableField(value = "work_describe", condition = LIKE)
    private String workDescribe;
    /**
     * 最后一次输错密码时间
     */
    @ApiModelProperty(value = "最后一次输错密码时间")
    @TableField(value = "password_error_last_time")
    private LocalDateTime passwordErrorLastTime;
    /**
     * 密码错误次数
     */
    @ApiModelProperty(value = "密码错误次数")
    @TableField(value = "password_error_num")
    private Integer passwordErrorNum;
    /**
     * 密码过期时间
     */
    @ApiModelProperty(value = "密码过期时间")
    @TableField(value = "password_expire_time")
    private LocalDateTime passwordExpireTime;

    /**
     * 最后登录时间
     */
    @ApiModelProperty(value = "最后登录时间")
    @TableField(value = "last_login_time")
    private LocalDateTime lastLoginTime;

    @ApiModelProperty(value = "员工ID")
    @TableField(exist = false)
    private Long employeeId;

    /**
     * 当前用户的角色编码
     * 启用条件： LoginUser.isFull = true || LoginUser.isRole = true
     */
    @TableField(exist = false)
    private List<String> roleCodeList;
    /**
     * 当前用户的资源编码
     * 启用条件： LoginUser.isFull = true || LoginUser.isResource = true
     */
    @TableField(exist = false)
    private List<String> resourceCodeList;
    /**
     * 当前用户所属部门
     * 启用条件： LoginUser.isFull = true || LoginUser.isOrg = true
     */
    @TableField(exist = false)
    private SysOrg dept;
    /**
     * 当前用户所属 单位
     * 启用条件： LoginUser.isFull = true || LoginUser.isOrg = true
     */
    @TableField(exist = false)
    private SysOrg company;
    /**
     * 当前用户的所属的 公司列表
     */
    @TableField(exist = false)
    private List<SysOrg> companyList;
    /**
     * 当前用户的所属的 部门列表
     */
    @TableField(exist = false)
    private List<SysOrg> deptList;
    /**
     * 当前用户的 岗位
     * 启用条件： LoginUser.isFull = true || LoginUser.isStation = true
     */
    @TableField(exist = false)
    private SysPosition position;

    @TableField(exist = false)
    private SysEmployee employee;

}
