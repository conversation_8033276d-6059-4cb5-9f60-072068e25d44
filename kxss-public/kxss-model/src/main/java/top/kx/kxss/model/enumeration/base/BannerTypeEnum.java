package top.kx.kxss.model.enumeration.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import top.kx.basic.interfaces.BaseEnum;

import java.util.stream.Stream;

/**
 * 流水编码前缀标识
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@ApiModel(value = "BannerTypeEnum", description = "轮播类型-枚举")
public enum BannerTypeEnum implements BaseEnum {
    /**
     * 首页
     */
    HOME("1", "首页"),
    ;

    @ApiModelProperty(value = "前缀标识")
    private String code;
    @ApiModelProperty(value = "描述")
    private String desc;

    public static BannerTypeEnum match(String val, BannerTypeEnum def) {
        return Stream.of(values()).parallel().filter((item) -> item.getCode().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static BannerTypeEnum get(String val) {
        return match(val, null);
    }

    public boolean eq(BannerTypeEnum val) {
        return val != null && eq(val.name());
    }

    @Override
    @ApiModelProperty(value = "编码", allowableValues = "MONTH,WEEK,DAY,NUL", example = "NUL")
    public String getCode() {
        return code;
    }
}
