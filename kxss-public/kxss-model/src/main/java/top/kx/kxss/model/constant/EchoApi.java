package top.kx.kxss.model.constant;

/**
 * Echo 注解中api的常量
 * <p>
 * 切记，该类下的接口和方法，一定要自己手动创建，否则会注入失败
 * <p>
 * 本类中的 @lamp.generator auto insert 请勿删除
 *
 * <AUTHOR>
 * @date 2020年01月20日11:16:37
 */
public interface EchoApi {
    // @lamp.generator auto insert EchoApi

    /**
     * lamp-cloud 数据字典项 feign查询类 全类名
     */
    String DICTIONARY_ITEM_FEIGN_CLASS = "dictServiceImpl";
    /**
     * 组织 service查询类
     */
    String ORG_ID_CLASS = "baseOrgManagerImpl";
    /**
     * 岗位 service查询类
     */
    String POSITION_ID_CLASS = "basePositionManagerImpl";
    String DEF_TENANT_SERVICE_IMPL_CLASS = "defTenantManagerImpl";
    String DEF_APPLICATION_SERVICE_IMPL_CLASS = "defApplicationManagerImpl";

    /**
     * 员工 service查询类
     */
    String EMP_ID_CLASS = "baseEmployeeManagerImpl";
    /**
     * 企业 service查询类 baseEnterpriseManagerImpl
     */
    String ENTERPRISE_ID_CLASS = "baseEnterpriseManagerImpl";
    /**
     * 商品 service查询类
     */
    String PRODUCT_ID_CLASS = "baseProductManagerImpl";

    String ATTRIBUTE_ID_CLASS = "baseAttributeManagerImpl";
    /**
     * 经销商 service查询类
     */
    String DEALERS_ID_CLASS = "baseDealersManagerImpl";

    /**
     * 经销商 service查询类
     */
    String DISTRIBUTOR_ID_CLASS = "baseDistributorManagerImpl";

    /**
     * 应用 service查询类
     */
    String DEF_CLIENT_IMPL_CLASS = "defClientManagerImpl";

    /**
     * 用户 service查询类
     */
    String USER_ID_CLASS = "defUserManagerImpl";

    /**
     * 用户 service查询类
     */
    String MEMBER_ID_CLASS = "memberInfoManagerImpl";

}
