package top.kx.kxss.model.entity.base;

import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import top.kx.kxss.model.constant.Condition;
import top.kx.kxss.model.constant.EchoApi;
import top.kx.kxss.model.constant.EchoDictType;
import top.kx.kxss.model.entity.system.SysUser;
import top.kx.basic.annotation.echo.Echo;
import top.kx.basic.base.entity.Entity;
import top.kx.basic.interfaces.echo.EchoVO;

import java.io.Serializable;
import java.util.Map;


/**
 * <p>
 * 实体类
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2019-07-03
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode
@ApiModel(value = "SysEmployee", description = "员工")
@TableName("base_employee")
public class SysEmployee extends Entity<Long> implements Serializable, EchoVO {

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = MapUtil.newHashMap();

    /**
     * 是否默认员工;[0-否 1-是]
     */
    @ApiModelProperty(value = "是否默认员工")
    @TableField(value = "is_default")
    private Boolean isDefault;
    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    @TableField(value = "user_id")
    private Long userId;
    /**
     * 岗位Id
     */
    @ApiModelProperty(value = "岗位Id")
    @Echo(api = EchoApi.POSITION_ID_CLASS)
    @TableField(value = "position_id")
    private Long positionId;

    /**
     * 上一次登录单位ID
     */
    @ApiModelProperty(value = "所属主机构")
    @Echo(api = EchoApi.POSITION_ID_CLASS)
    @TableField(value = "last_company_id")
    private Long lastCompanyId;
    /**
     * 上一次登录部门ID
     */
    @TableField(value = "last_dept_id")
    private Long lastDeptId;

    /**
     * 真实姓名
     */
    @ApiModelProperty(value = "真实姓名")
    @TableField(value = "real_name", condition = Condition.LIKE)
    private String realName;

    /**
     * 职位状态;[10-在职 20-离职]@Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Base.POSITION_STATUS)
     */
    @ApiModelProperty(value = "职位状态")
    @Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Base.POSITION_STATUS)
    @TableField(value = "position_status", condition = Condition.LIKE)
    private String positionStatus;
    /**
     * 激活状态;[10-未激活 20-已激活]
     */
    @ApiModelProperty(value = "激活状态")
    @TableField(value = "active_status", condition = Condition.LIKE)
    private String activeStatus;

    /**
     * 状态;[0-禁用 1-启用]
     */
    @ApiModelProperty(value = "状态")
    @TableField(value = "state")
    private Boolean state;

    /**
     * 创建机构Id
     */
    @TableField(value = "created_org_id")
    private Long createdOrgId;

    @ApiModelProperty(value = "用户信息")
    @TableField(exist = false)
    private SysUser defUser;
}
