package top.kx.kxss.model.enumeration.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import top.kx.basic.interfaces.BaseEnum;

import java.util.stream.Stream;

/**
 * 流水编码前缀标识
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@ApiModel(value = "SecurityCodeBatchImportEnum", description = "-枚举")
public enum SecurityCodeBatchImportEnum implements BaseEnum {
    /**
     * 1-未导入,2-导入中,3-已经导入
     */
    NOT_IMPORT("1", "未导入"),
    IMPORTING("2", "导入中"),
    IMPORTED("3", "已导入"),
    IMPORT_FAIL("4", "导入失败"),

    ;

    @ApiModelProperty(value = "前缀标识")
    private String code;
    @ApiModelProperty(value = "描述")
    private String desc;

    public static SecurityCodeBatchImportEnum match(String val, SecurityCodeBatchImportEnum def) {
        return Stream.of(values()).parallel().filter((item) -> item.getCode().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static SecurityCodeBatchImportEnum get(String val) {
        return match(val, null);
    }

    public boolean eq(SecurityCodeBatchImportEnum val) {
        return val != null && eq(val.name());
    }

    @Override
    @ApiModelProperty(value = "编码", allowableValues = "MONTH,WEEK,DAY,NUL", example = "NUL")
    public String getCode() {
        return code;
    }
}
