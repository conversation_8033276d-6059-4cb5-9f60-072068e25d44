package top.kx.kxss.model.enumeration.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import top.kx.basic.interfaces.BaseEnum;

import java.util.stream.Stream;

/**
 * 流水编码前缀标识
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@ApiModel(value = "DownloadSourceEnum", description = "-枚举")
public enum DownloadSourceEnum implements BaseEnum {

    BATCH("BATCH", "防伪码批次导出"),

    DISTRIBUTOR("DISTRIBUTOR", "经销商导出"),

    ;

    @ApiModelProperty(value = "前缀标识")
    private String code;
    @ApiModelProperty(value = "描述")
    private String desc;

    public static DownloadSourceEnum match(String val, DownloadSourceEnum def) {
        return Stream.of(values()).parallel().filter((item) -> item.getCode().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static DownloadSourceEnum get(String val) {
        return match(val, null);
    }

    public boolean eq(DownloadSourceEnum val) {
        return val != null && eq(val.name());
    }

    @Override
    @ApiModelProperty(value = "编码", allowableValues = "MONTH,WEEK,DAY,NUL", example = "NUL")
    public String getCode() {
        return code;
    }
}
