package top.kx.kxss.model.enumeration.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import top.kx.basic.interfaces.BaseEnum;

import java.util.stream.Stream;

/**
 * 流水编码前缀标识
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@ApiModel(value = "CodeIdentifyEnum", description = "流水编码前缀标识-枚举")
public enum CodeIdentifyEnum implements BaseEnum {
    /**
     * 进行中
     */
    PRODUCT("SP", "商品"),
    PRODUCT_OUTIN("DH", "商品出入库单据号"),
    MEMBER_CODE("KX", "会员编码"),
    PRIZE_CODE("PE", "奖品"),
    ;

    @ApiModelProperty(value = "前缀标识")
    private String code;
    @ApiModelProperty(value = "描述")
    private String desc;

    public static CodeIdentifyEnum match(String val, CodeIdentifyEnum def) {
        return Stream.of(values()).parallel().filter((item) -> item.getCode().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static CodeIdentifyEnum get(String val) {
        return match(val, null);
    }

    public boolean eq(CodeIdentifyEnum val) {
        return val != null && eq(val.name());
    }

    @Override
    @ApiModelProperty(value = "编码", allowableValues = "MONTH,WEEK,DAY,NUL", example = "NUL")
    public String getCode() {
        return code;
    }
}
