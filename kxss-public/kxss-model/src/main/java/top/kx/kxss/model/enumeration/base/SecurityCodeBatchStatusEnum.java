package top.kx.kxss.model.enumeration.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import top.kx.basic.interfaces.BaseEnum;

import java.util.stream.Stream;

/**
 * 流水编码前缀标识
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@ApiModel(value = "SecurityCodeBatchStatusEnum", description = "-枚举")
public enum SecurityCodeBatchStatusEnum implements BaseEnum {
    /**
     * 1-生成中。2-已生成
     */
    GENERATING("1", "生成中"),
    GENERATED("2", "已生成"),
    GENERATE_FAIL("3", "生成失败"),

    ;

    @ApiModelProperty(value = "前缀标识")
    private String code;
    @ApiModelProperty(value = "描述")
    private String desc;

    public static SecurityCodeBatchStatusEnum match(String val, SecurityCodeBatchStatusEnum def) {
        return Stream.of(values()).parallel().filter((item) -> item.getCode().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static SecurityCodeBatchStatusEnum get(String val) {
        return match(val, null);
    }

    public boolean eq(SecurityCodeBatchStatusEnum val) {
        return val != null && eq(val.name());
    }

    @Override
    @ApiModelProperty(value = "编码", allowableValues = "MONTH,WEEK,DAY,NUL", example = "NUL")
    public String getCode() {
        return code;
    }
}
