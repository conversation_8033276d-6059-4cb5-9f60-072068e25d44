package top.kx.kxss.model.entity.base;

import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import top.kx.kxss.model.constant.Condition;
import top.kx.kxss.model.constant.EchoApi;
import top.kx.kxss.model.constant.EchoDictType;
import top.kx.basic.annotation.echo.Echo;
import top.kx.basic.base.entity.Entity;
import top.kx.basic.interfaces.echo.EchoVO;

import java.io.Serializable;
import java.util.Map;


/**
 * <p>
 * 实体类
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2019-07-03
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode
@Accessors(chain = true)
@ApiModel(value = "SysOrg", description = "组织")
@TableName("base_org")
public class SysOrg extends Entity<Long> implements Serializable, EchoVO {

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = MapUtil.newHashMap();

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @TableField(value = "name", condition = Condition.LIKE)
    private String name;
    /**
     * 类型;[10-单位 20-部门]@Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Base.ORG_TYPE)
     */
    @ApiModelProperty(value = "类型")
    @Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Base.ORG_TYPE)
    @TableField(value = "type_", condition = Condition.LIKE)
    private String type;
    /**
     * 简称
     */
    @ApiModelProperty(value = "简称")
    @TableField(value = "short_name", condition = Condition.LIKE)
    private String shortName;
    /**
     * 父ID
     */
    @ApiModelProperty(value = "父ID")
    @TableField(value = "parent_id")
    private Long parentId;
    /**
     * 树层级
     */
    @ApiModelProperty(value = "树层级")
    @TableField(value = "tree_grade")
    private Integer treeGrade;
    /**
     * 树路径;用id拼接树结构
     */
    @ApiModelProperty(value = "树路径")
    @TableField(value = "tree_path", condition = Condition.LIKE)
    private String treePath;
    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    @TableField(value = "sort_value")
    private Integer sortValue;
    /**
     * 状态;[0-禁用 1-启用]
     */
    @ApiModelProperty(value = "状态")
    @TableField(value = "state")
    private Boolean state;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @TableField(value = "remarks", condition = Condition.LIKE)
    private String remarks;

}
