.tgBtn {
    height: 12px;
    border: 1px solid #dcdfe6;
    padding: 12px 20px;
    font-size: 14px;
    border-radius: 4px;
    text-align: center;
    line-height: 12px;
}

.tgAlertDiv {
    z-index: 9999999;
    left: 50%;
    border-radius: 8px;
    font-size: 14px;
    text-align: center;
    padding: 12px 20px;
}

.tgtool {
    -webkit-touch-callout: none;
    /* iOS Safari
       -webkit-user-select: none; /* Chrome/Safari/Opera */
    -khtml-user-select: none; /* Konqueror */
    -moz-user-select: none; /* Firefox */
    -ms-user-select: none; /* Internet Explorer/Edge */
    user-select: none; /* Non-prefixed version, currently*/
}

.animated {
    animation-duration: 1s;
    animation-fill-mode: both;
}

@keyframes bounceInDown {
    from,
    60%,
    75%,
    90%,
    to {
        animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
    }
    0% {
        opacity: 0;
        transform: translate3d(0, -300px, 0);
    }
    50% {
        transform: translate3d(0, -300px, 0);
    }
    60% {
        opacity: 1;
        transform: translate3d(0, 25px, 0);
    }
    75% {
        transform: translate3d(0, -10px, 0);
    }
    90% {
        transform: translate3d(0, 5px, 0);
    }
    to {
        transform: none;
    }
}

.bounceInDown {
    animation-name: bounceInDown;
}

@keyframes remove {
    from {
        top: 0px;
    }
    to {
        top: -2000px;
    }
}

.remove {
    animation: remove 4s;
    animation-fill-mode: forwards;
}

@keyframes removeTop {
    from {
        top: 0px;
    }
    to {
        top: -50px;
    }
}

.removeTop {
    animation: removeTop 1s;
    animation-fill-mode: forwards;
}

.button_container {
    float: left;
    width: 15%;
    padding-top: 20px;
    background-color: #f5f5f5;
    box-shadow: 5px 0px 5px #888888;
}

.btnTg {
    border: none;
    display: block;
    text-align: center;
    cursor: pointer;
    text-transform: uppercase;
    outline: none;
    overflow: hidden;
    position: relative;
    color: #fff;
    font-weight: 700;
    font-size: 15px;
    background-color: #3b7b8a;
    padding: 17px 60px;
    margin: 0 auto;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.20);
    margin-bottom: 10px;
    width: 100%;
}

.btnTg span {
    position: relative;
    z-index: 1;
}

.btnTg:after {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    height: 490%;
    width: 140%;
    background: #78c7d2;
    -webkit-transition: all .5s ease-in-out;
    transition: all .5s ease-in-out;
    -webkit-transform: translateX(-98%) translateY(-25%) rotate(45deg);
    transform: translateX(-98%) translateY(-25%) rotate(45deg);
}

.btnTg:hover:after {
    -webkit-transform: translateX(-9%) translateY(-25%) rotate(45deg);
    transform: translateX(-9%) translateY(-25%) rotate(45deg);
}