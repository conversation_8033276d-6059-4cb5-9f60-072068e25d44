package top.kx.kxss.gateway.controller;

import cn.hutool.core.map.MapUtil;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import top.kx.basic.base.R;
import top.kx.kxss.model.vo.result.Option;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;


/**
 * 常用Controller
 *
 * <AUTHOR>
 * @date 2019-06-21 18:22
 */
@Controller
public class GeneratorController {

    @Value("${spring.application.name:}")
    private String application;

    @ResponseBody
    @ApiOperation(value = "查询在线服务的前缀")
    @GetMapping("/gateway/findOnlineServicePrefix")
    public R<Map<String, String>> findOnlineServicePrefix() {

        Map<String, String> map = MapUtil.newHashMap();
        map.put(application, "base");
        return R.success(map);
    }

    @ResponseBody
    @ApiOperation(value = "查询在线服务")
    @GetMapping("/gateway/findOnlineService")
    public R<List<Option>> findOnlineService() {
        List<Option> list = new ArrayList();

        list.add(Option.builder()
                .value("base").text(application).label(application)
                .build());

        return R.success(list);
    }
    @ResponseBody
    @ApiOperation(value = "查询在线服务")
    @GetMapping("/findOnlineService")
    public R<List<Option>> findOnlineService1() {
        List<Option> list = new ArrayList();

        list.add(Option.builder()
                .value("base").text(application).label(application)
                .build());

        return R.success(list);
    }
}
