package top.kx.kxss.base.interceptor;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.AsyncHandlerInterceptor;
import top.kx.basic.cache.redis2.CacheResult;
import top.kx.basic.cache.repository.CacheOps;
import top.kx.basic.context.ContextConstants;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.exception.UnauthorizedException;
import top.kx.basic.jwt.TokenUtil;
import top.kx.basic.jwt.model.Token;
import top.kx.basic.jwt.utils.JwtUtil;
import top.kx.basic.model.cache.CacheKey;
import top.kx.basic.utils.StrPool;
import top.kx.kxss.common.cache.common.TokenUserIdCacheKeyBuilder;
import top.kx.kxss.common.constant.BizConstant;
import top.kx.kxss.common.properties.IgnoreProperties;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import static top.kx.basic.context.ContextConstants.*;
import static top.kx.basic.exception.code.ExceptionCode.JWT_NOT_LOGIN;
import static top.kx.basic.exception.code.ExceptionCode.JWT_OFFLINE;

/**
 * 用户信息解析器 一定要在AuthenticationFilter之前执行
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2021/12/28 2:36 下午
 * @create [2021/12/28 2:36 下午 ] [tangyh] [初始创建]
 */
@Slf4j
@RequiredArgsConstructor
public class TokenContextFilter implements AsyncHandlerInterceptor {
    private final String profiles;
    private final IgnoreProperties ignoreProperties;
    private final TokenUtil tokenUtil;
    private final CacheOps cacheOps;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        if (!(handler instanceof HandlerMethod)) {
            log.debug("not exec!!! url={}", request.getRequestURL());
            return true;
        }
        ContextUtil.setBoot(true);
        ContextUtil.setPath(getHeader(ContextConstants.PATH_HEADER, request));
        String traceId = IdUtil.fastSimpleUUID();
        MDC.put(ContextConstants.TRACE_ID_HEADER, traceId);
        try {
            // 1,解码 Authorization
            parseAuthorization(request);

            // 2, 获取 应用id
            parseApplication(request);

            // 3，解析 Token
            parseToken(request);

            if (StrUtil.isBlank(ContextUtil.getClientId())) {
                ContextUtil.setClientId(getHeader(ContextConstants.CLIENT_ID_HEADER, request));
            }
        } catch (Exception e) {
            log.error("request={}", request.getRequestURL(), e);
            throw e;
        }

        return true;
    }

    private boolean parseToken(HttpServletRequest request) {
        // 忽略 token 认证的接口
        if (isIgnoreToken(request)) {
            log.debug("access filter not execute");
            return true;
        }

        //3, 获取token
        String token = getHeader(TOKEN_KEY, request);


        Token tokenObj;
        //添加测试环境的特殊token
        if (isDev(token)) {
            tokenObj = new Token();
            tokenObj.setUserId(1L).setEmployeeId(1L).setCurrentTopCompanyId(1L).setCurrentCompanyId(1L).setCurrentDeptId(1L);
        } else {
            tokenObj = tokenUtil.parseToken(token);

            // 验证 是否在其他设备登录或被挤下线
            // TOKEN_USER_ID:{token} === T
            CacheKey cacheKey = TokenUserIdCacheKeyBuilder.builder(tokenObj.getUuid());
            CacheResult<String> tokenCache = cacheOps.get(cacheKey);

            if (StrUtil.isEmpty(tokenCache.getValue())) {
                log.error("token is empty");
                throw UnauthorizedException.wrap(JWT_NOT_LOGIN.getMsg());
            } else if (StrUtil.equals(BizConstant.LOGIN_STATUS, tokenCache.getValue())) {
                log.error("您被踢了");
                throw UnauthorizedException.wrap(JWT_OFFLINE.getMsg());
            }
        }

        //6, 转换，将 token 解析出来的用户身份 和 解码后的tenant、Authorization 重新封装到请求头
        ContextUtil.setUserId(tokenObj.getUserId());
        ContextUtil.setEmployeeId(tokenObj.getEmployeeId());
        ContextUtil.setCurrentCompanyId(tokenObj.getCurrentCompanyId());
        ContextUtil.setCurrentTopCompanyId(tokenObj.getCurrentTopCompanyId());
        ContextUtil.setCurrentDeptId(tokenObj.getCurrentDeptId());
        MDC.put(ContextConstants.USER_ID_HEADER, String.valueOf(tokenObj.getUserId()));
        MDC.put(ContextConstants.EMPLOYEE_ID_HEADER, String.valueOf(tokenObj.getEmployeeId()));

        return false;
    }

    private void parseAuthorization(HttpServletRequest request) {
        String base64Authorization = getHeader(CLIENT_KEY, request);
        if (StrUtil.isNotEmpty(base64Authorization)) {
            String[] client = JwtUtil.getClient(base64Authorization);
            ContextUtil.setApplicationId(client[0]);
        }
    }

    private void parseApplication(HttpServletRequest request) {
        String applicationIdStr = getHeader(APPLICATION_ID_KEY, request);
        if (StrUtil.isNotEmpty(applicationIdStr)) {
            ContextUtil.setApplicationId(applicationIdStr);
            MDC.put(APPLICATION_ID_HEADER, applicationIdStr);
        }
    }

    private String getHeader(String name, HttpServletRequest request) {
        String value = request.getHeader(name);
        if (StrUtil.isEmpty(value)) {
            value = request.getParameter(name);
        }
        if (StrUtil.isEmpty(value)) {
            return null;
        }
        return URLUtil.decode(value);
    }


    protected boolean isDev(String token) {
        return !StrPool.PROD.equalsIgnoreCase(profiles) && (StrPool.TEST_TOKEN.equalsIgnoreCase(token) || StrPool.TEST.equalsIgnoreCase(token));
    }

    /**
     * 忽略应用级token
     *
     * @return
     */
    protected boolean isIgnoreToken(HttpServletRequest request) {
        return ignoreProperties.isIgnoreUser(request.getMethod(), request.getRequestURI());
    }

    /**
     * 忽略 租户编码
     *
     * @return
     */
    protected boolean isIgnoreTenant(HttpServletRequest request) {
        return ignoreProperties.isIgnoreTenant(request.getMethod(), request.getRequestURI());
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        ContextUtil.remove();
    }
}
