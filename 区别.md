# boot 和 cloud 区别

1. EchoApi类中所有的feign改成Impl
2. AuthenticationFilter、TokenContextFilter实现不同
3. kxss-boot-server模块 application.yml = kxss-cloud的 nacos 中所有配置合并
4. kxss-boot-server = kxss-system-server + kxss-oauth-server + kxss-base-server + kxss-generator-server合并
   uri权限校验时，判断的uri地址不同： ResourceBiz#checkUri

> 若您确定只使用kxss-boot，而非kxss-cloud，请将def_resource_api表中uri的代理的前缀(/base、/system、/oauth)去除，即可 删除删除删除
> if里面的代码！
> 因为脚本数据是基于kxss-cloud配置的，所以uri地址会多一段gateway代理前缀。如
> kxss-cloud 中地址为：/base/baseEmployee/page
> 对应kxss-boot的地址为：/baseEmployee/page
> 其中/base是因为使用了gateway增加的！

5. 新建租户时，初始化kxss_base库表结构的脚本存放路径不同。boot位于 kxss-boot-server/src/main/resources/ ，cloud位于
   kxss-system-server/src/main/resources/
6. 租户管理初始化数据源实现不同： GeneratorController
7. 代码生成器生成代码需要的模板文件存放位置不同
8. boot项目配置不同：

```yml
kxss:
  webmvc:
    undertow: true
    header: false
```
