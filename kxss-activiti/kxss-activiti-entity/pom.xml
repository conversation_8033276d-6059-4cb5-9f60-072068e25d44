<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>top.kx.kxss</groupId>
        <artifactId>kxss-activiti</artifactId>
        <version>4.13.1</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>kxss-activiti-entity</artifactId>
    <description>工作流实体模块</description>

    <dependencies>
        <dependency>
            <groupId>top.kx.kxss</groupId>
            <artifactId>kxss-common</artifactId>
            <version>${kxss-project.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-annotation</artifactId>
        </dependency>

        <dependency>
            <groupId>org.activiti</groupId>
            <artifactId>activiti-spring-boot-starter-basic</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>de.odysseus.juel</groupId>
                    <artifactId>juel-spi</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>de.odysseus.juel</groupId>
                    <artifactId>juel-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-security</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>activation</artifactId>
                    <groupId>javax.activation</groupId>
                </exclusion>
            </exclusions>
        </dependency>

    </dependencies>

</project>
