package top.kx.kxss.activiti.dto.activiti;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import top.kx.basic.annotation.echo.Echo;
import top.kx.basic.interfaces.echo.EchoVO;

import java.util.HashMap;
import java.util.Map;

/**
 * 活动任务返回实体
 *
 * <AUTHOR>
 * @date 2020-08-07
 */
@Data
@NoArgsConstructor
@ApiModel(value = "TaskResDTO", description = "活动任务返回实体")
public class TaskResDTO implements EchoVO {

    /**
     * 任务id
     */
    @ApiModelProperty(value = "任务id")
    protected String id;
    /**
     * 任务名称
     */
    @ApiModelProperty(value = "任务名称")
    protected String name;
    /**
     * 审批人id
     */
    @ApiModelProperty(value = "审批人id")
    protected String assignee;
    /**
     * 审批人
     */
    @ApiModelProperty(value = "审批人")
    protected String cuser;
    /**
     * 租户id
     */
    @ApiModelProperty(value = "租户id")
    protected String tenantId;
    /**
     * 是否挂起
     */
    @ApiModelProperty(value = "是否挂起")
    protected Boolean isSuspended;
    /**
     * 对应定义key
     */
    @ApiModelProperty(value = "对应定义key")
    protected String taskDefKey;
    /**
     * 对应流程实例
     */
    @ApiModelProperty(value = "对应流程实例")
    @Echo(api = "myProcessInstantService")
    protected String inst;
    private Map<String, Object> echoMap = new HashMap<>();

}
