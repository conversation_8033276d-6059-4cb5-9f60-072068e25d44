package top.kx.kxss.activiti.controller.activiti;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.kxss.activiti.service.activiti.LampProcessDefinitionService;
import top.kx.kxss.activiti.vo.ProcessDefinitionVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

import static top.kx.basic.base.R.fail;
import static top.kx.basic.base.R.success;

/**
 * 流程定义管理
 *
 * <AUTHOR>
 * @date 2020-08-07
 */
@Slf4j
@RestController
@RequestMapping("/definition")
@AllArgsConstructor
public class DefinitionController {

    private final LampProcessDefinitionService myProcessDefinitionService;

    /**
     * 模型分页查询
     *
     * @param params 分页查询实体
     */
    @PostMapping(value = "/page")
    public R<IPage<ProcessDefinitionVO>> page(@RequestBody PageParams<ProcessDefinitionVO> params) {
        return success(myProcessDefinitionService.page(params));
    }

    /**
     * 通过流程定义映射模型
     *
     * @param processDefinitionId 流程id
     */
    @PostMapping(value = "/saveModelByProcessDefinition")
    public R<Boolean> saveModelByProcessDefinition(@RequestParam(value = "processDefinitionId") String processDefinitionId) {
        return success(myProcessDefinitionService.saveModelByProcessDefinition(processDefinitionId));
    }

    /**
     * 修改流程状态
     *
     * @param processDefinitionId 流程定义id
     * @param suspensionState     修改状态
     */
    @PutMapping(value = "/updateState")
    public R<Boolean> updateState(@RequestParam(value = "id") String processDefinitionId,
                                  @RequestParam(value = "suspensionState") Integer suspensionState) {
        myProcessDefinitionService.updateState(processDefinitionId, suspensionState);
        return success(true);
    }


    /**
     * 删除流程实例及模型
     */
    @DeleteMapping(value = "/delete")
    public R<Boolean> delete(@RequestBody List<String> deploymentIds) {
        return R.success(myProcessDefinitionService.deleteProcessDeploymentByIds(deploymentIds));
    }


    /**
     * 导入流程模型
     *
     * @param file 上传文件
     */
    @SneakyThrows
    @PostMapping(value = "/upload")
    public R<String> upload(@RequestParam(value = "file", required = false) MultipartFile file,
                            @RequestParam(value = "name", required = false) String name) {
        if (file == null || file.isEmpty()) {
            return fail("请上传文件!");
        }
        if (!file.getOriginalFilename().endsWith(".zip")) {
            return fail("仅支持zip文件!");
        }

        if (StrUtil.isEmpty(name)) {
            name = file.getOriginalFilename();
        }

        return success(myProcessDefinitionService.deploymentDefinitionByZip(name, file.getInputStream()));
    }

    /**
     * 预览流程定义XML
     *
     * @param processDefinitionId 流程定义id
     */
    @GetMapping(value = "/getXml")
    public R<String> getXml(@RequestParam("processDefinitionId") String processDefinitionId) {
        return success(myProcessDefinitionService.getXml(processDefinitionId));
    }

    /**
     * 读取流程资源
     *
     * @param processDefinitionId 流程定义ID
     */
    @GetMapping(value = "/getDiagram", produces = "image/png")
    public void getDiagram(@RequestParam("processDefinitionId") String processDefinitionId, HttpServletResponse response) {
        myProcessDefinitionService.getDiagram(processDefinitionId, response);
    }

}
