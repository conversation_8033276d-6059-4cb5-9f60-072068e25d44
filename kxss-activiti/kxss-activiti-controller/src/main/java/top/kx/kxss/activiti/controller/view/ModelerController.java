package top.kx.kxss.activiti.controller.view;

import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.RepositoryService;
import org.activiti.engine.repository.Model;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import top.kx.basic.annotation.response.IgnoreResponseBodyAdvice;

import java.util.HashMap;
import java.util.Map;

/**
 * 视图管理
 *
 * <AUTHOR>
 * @date 2020-08-07
 */
@Controller
@Slf4j
@RequestMapping({"/static"})
@RequiredArgsConstructor
@IgnoreResponseBodyAdvice
public class ModelerController {
    private final RepositoryService repositoryService;

    /**
     * 跳转首页
     *
     * @param Token   token,该token多用于跨域情况
     * @param modelId 模型id
     */
    @RequestMapping("index")
    public ModelAndView index(ModelAndView modelAndView, String Token, String modelId) {
        modelAndView.addObject("Token", Token);
        if (StrUtil.isNotEmpty(modelId)) {
            modelAndView.setViewName("static/index");
            return modelAndView;
        }
        return modelAndView;
    }

    /**
     * 跳转编辑器页面
     */
    @GetMapping("editor")
    public String editor() {
        return "static/index";
    }

    /**
     * 撤销流程定义
     *
     * @param modelId 模型ID
     */
    @ResponseBody
    @RequestMapping("/revokePublish")
    public Object revokePublish(String modelId) {
        Map<String, String> map = new HashMap<>(3);
        Model modelData = repositoryService.getModel(modelId);
        if (null != modelData) {
            repositoryService.deleteDeployment(modelData.getDeploymentId(), true);
            map.put("code", "SUCCESS");
        } else {
            map.put("code", "FAILURE");
        }
        return map;
    }

}
