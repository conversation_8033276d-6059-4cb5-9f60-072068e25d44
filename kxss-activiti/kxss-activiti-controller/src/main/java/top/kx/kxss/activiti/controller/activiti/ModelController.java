package top.kx.kxss.activiti.controller.activiti;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.repository.Model;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.activiti.dto.activiti.ModelSelectReqDTO;
import top.kx.kxss.activiti.service.activiti.LampModelService;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 模型管理
 * ACT_RE_MODEL
 *
 * <AUTHOR>
 * @date 2020-08-07
 */
@Slf4j
@RestController
@RequestMapping("model")
@AllArgsConstructor
public class ModelController {
    private final LampModelService myModelService;

    /**
     * 模型分页查询
     *
     * @param params 查询实体
     */
    @PostMapping(value = "/page")
    public R<IPage<Model>> page(@RequestBody PageParams<ModelSelectReqDTO> params) {
        IPage<Model> page = myModelService.page(params);
        return R.success(page);
    }

    /**
     * 创建模型
     *
     * @param name 模型名称
     * @param key  模型key
     */
    @PostMapping(value = "/save")
    public R<String> save(String name, String key, String description) {
        ArgumentAssert.notEmpty(name, "模型名称不能为空");
        ArgumentAssert.notEmpty(key, "模型KEY不能为空");

        Model model = myModelService.create(name, key, description);
        return R.success(model.getId());
    }


    /**
     * 删除流程实例及模型
     *
     * @param modelId 模型ID
     */
    @DeleteMapping(value = "/delete")
    public R<Boolean> deleteProcessInstance(String modelId) {
        return R.success(myModelService.deleteModel(modelId));
    }

    /**
     * 发布流程
     *
     * @param modelId 模型
     */
    @PostMapping(value = "/publish")
    public R<Map<String, String>> publish(@RequestParam String modelId) {
        return R.success(myModelService.publish(modelId));
    }

    /**
     * 根据模型Id导出XML
     */
    @GetMapping(value = "/download")
    public void exportXmlByModelId(@RequestParam("modelId") String modelId, HttpServletResponse response) {
        myModelService.exportXmlByModelId(response, modelId);
    }
}
