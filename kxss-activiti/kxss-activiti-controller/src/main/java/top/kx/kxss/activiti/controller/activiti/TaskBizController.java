package top.kx.kxss.activiti.controller.activiti;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.kxss.activiti.service.activiti.MyTaskService;

/**
 * 任务管理
 *
 * <AUTHOR>
 * @date 2020-08-07
 */
@Slf4j
@RestController
@RequestMapping("task")
@RequiredArgsConstructor
public class TaskBizController {
    private final MyTaskService myTaskService;

    /**
     * 任务转办
     *
     * @param taskId 任务id
     * @param userId 用户id
     */
    @RequestMapping(value = "/updateAssignee", method = RequestMethod.GET)
    public R<Boolean> updateAssignee(@RequestParam(value = "taskId") String taskId, @RequestParam(value = "userId") String userId) {
        Boolean bool = myTaskService.setAssignee(taskId, userId);

        return R.success(bool);
    }

}
