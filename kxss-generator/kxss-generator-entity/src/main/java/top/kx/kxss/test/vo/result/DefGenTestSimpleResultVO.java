package top.kx.kxss.test.vo.result;

import cn.hutool.core.map.MapUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import top.kx.kxss.test.enumeration.DefGenTestSimpleType2Enum;
import top.kx.kxss.test.enumeration.ProductType;
import top.kx.basic.annotation.echo.Echo;
import top.kx.basic.base.entity.Entity;
import top.kx.basic.interfaces.echo.EchoVO;
import top.kx.kxss.model.constant.EchoApi;
import top.kx.kxss.model.constant.EchoDictType;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * <p>
 * 表单查询方法返回值VO
 * 测试单表
 * </p>
 *
 * <AUTHOR>
 * @date 2022-04-15 15:36:45
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@ApiModel(value = "DefGenTestSimpleResultVO", description = "测试单表")
public class DefGenTestSimpleResultVO extends Entity<Long> implements Serializable, EchoVO {

    private static final long serialVersionUID = 1L;

    private Map<String, Object> echoMap = MapUtil.newHashMap();

    @ApiModelProperty(value = "ID")
    private Long id;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;
    /**
     * 库存
     */
    @ApiModelProperty(value = "库存")
    private Integer stock;
    /**
     * 商品类型;
     * #ProductType{ordinary:普通;gift:赠品}
     */
    @ApiModelProperty(value = "商品类型")
    @Echo(api = Echo.ENUM_API)
    private ProductType type;
    /**
     * 商品类型2 ;
     * <p>
     * #{ordinary:01,普通;gift:02,赠品;}
     */
    @ApiModelProperty(value = "商品类型2 ")
    @Echo(api = Echo.ENUM_API)
    private DefGenTestSimpleType2Enum type2;
    /**
     * 学历;
     *
     * @Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS,  dictType = DictionaryType.Global.EDUCATION)
     */
    @ApiModelProperty(value = "学历")
    private String type3;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Boolean state;
    /**
     * 测试
     */
    @ApiModelProperty(value = "测试")
    private Integer test4;
    /**
     * 时间
     */
    @ApiModelProperty(value = "时间")
    private LocalDate test5;
    /**
     * 日期
     */
    @ApiModelProperty(value = "日期")
    private LocalDateTime test6;
    /**
     * 父id
     */
    @ApiModelProperty(value = "父id")
    private Long parentId;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String label;
    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sortValue;
    /**
     * 字符字典;
     *
     * @Echo(api = "top.kx.kxss.oauth.api.DictionaryApi", dictType="GLOBAL_SEX")
     */
    @ApiModelProperty(value = "字符字典")
    @Echo(api = "top.kx.kxss.oauth.api.DictionaryApi", dictType = "GLOBAL_SEX")
    private String test7;
    /**
     * 整形字典;
     *
     * @Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = DictionaryType.Global.DATA_TYPE)
     */
    @ApiModelProperty(value = "整形字典")
    @Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Global.DATA_TYPE)
    private Integer test12;
    /**
     * 用户;
     *
     * @Echo(api = EchoApi.POSITION_ID_CLASS)
     */
    @ApiModelProperty(value = "用户")
    @Echo(api = EchoApi.POSITION_ID_CLASS)
    private Long userId;
    /**
     * 组织;
     *
     * @Echo(api = EchoApi.ORG_ID_CLASS)
     */
    @ApiModelProperty(value = "组织")
    @Echo(api = EchoApi.ORG_ID_CLASS)
    private Long orgId;
    /**
     * 小数
     */
    @ApiModelProperty(value = "小数")
    private BigDecimal test8;
    /**
     * 浮点2
     */
    @ApiModelProperty(value = "浮点2")
    private Float test9;
    /**
     * 浮点
     */
    @ApiModelProperty(value = "浮点")
    private Double test10;
    /**
     * xiao树
     */
    @ApiModelProperty(value = "xiao树")
    private BigDecimal test11;


}
