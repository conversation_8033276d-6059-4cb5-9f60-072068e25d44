package top.kx.kxss.test.enumeration;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import top.kx.basic.interfaces.BaseEnum;

import java.util.stream.Stream;

/**
 * <p>
 * 实体注释中生成的类型枚举
 * 测试树结构
 * </p>
 *
 * <AUTHOR>
 * @date 2022-04-20 00:28:30
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "DefGenTestTreeType2Enum", description = "商品类型2 -枚举")
public enum DefGenTestTreeType2Enum implements BaseEnum {

    /**
     * ORDINARY
     */
    ORDINARY("01", "普通"),
    /**
     * GIFT
     */
    GIFT("02", "赠品"),
    ;

    @ApiModelProperty(value = "数据库存储值")
    private String value;
    @ApiModelProperty(value = "描述")
    private String desc;

    /**
     * 根据当前枚举的name匹配
     */
    public static DefGenTestTreeType2Enum match(String val, DefGenTestTreeType2Enum def) {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static DefGenTestTreeType2Enum get(String val) {
        return match(val, null);
    }

    public boolean eq(DefGenTestTreeType2Enum val) {
        return val != null && eq(val.name());
    }

    @Override
    @ApiModelProperty(value = "name", allowableValues = "ORDINARY,GIFT", example = "ORDINARY")
    public String getCode() {
        return this.name();
    }

    @Override
    @ApiModelProperty(value = "数据库中的值")
    public String getValue() {
        return this.value;
    }

}
