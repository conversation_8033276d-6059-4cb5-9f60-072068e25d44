package top.kx.kxss.test.vo.update;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import top.kx.kxss.test.enumeration.DefGenTestTreeType2Enum;
import top.kx.kxss.test.enumeration.ProductType;
import top.kx.basic.base.entity.SuperEntity;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 表单修改方法VO
 * 测试树结构
 * </p>
 *
 * <AUTHOR>
 * @date 2022-04-20 00:28:30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode
@Builder
@ApiModel(value = "DefGenTestTreeUpdateVO", description = "测试树结构")
public class DefGenTestTreeUpdateVO implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "父节点")
    protected Long parentId;
    @ApiModelProperty(value = "排序号")
    protected Integer sortValue;
    @ApiModelProperty(value = "ID")
    @NotNull(message = "请填写ID", groups = SuperEntity.Update.class)
    private Long id;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @NotEmpty(message = "请填写名称")
    @Size(max = 24, message = "名称长度不能超过{max}")
    private String name;
    /**
     * 库存
     */
    @ApiModelProperty(value = "库存")
    @NotNull(message = "请填写库存")
    private Integer stock;
    /**
     * 商品类型;
     * #ProductType{ordinary:普通;gift:赠品}
     */
    @ApiModelProperty(value = "商品类型")
    private ProductType type;
    /**
     * 商品类型2 ;
     * <p>
     * #{ordinary:01,普通;gift:02,赠品;}
     */
    @ApiModelProperty(value = "商品类型2 ")
    private DefGenTestTreeType2Enum type2;
    /**
     * 学历;
     *
     * @Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS,  dictType = EchoDictType.Global.EDUCATION)
     */
    @ApiModelProperty(value = "学历")
    @Size(max = 255, message = "学历长度不能超过{max}")
    private String type3;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Boolean state;
    /**
     * 测试
     */
    @ApiModelProperty(value = "测试")
    private Integer test4;
    /**
     * 时间
     */
    @ApiModelProperty(value = "时间")
    private LocalDate test5;
    /**
     * 日期
     */
    @ApiModelProperty(value = "日期")
    private LocalDateTime test6;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @Size(max = 255, message = "名称长度不能超过{max}")
    private String label;
    /**
     * 字符字典;
     *
     * @Echo(api = "top.kx.kxss.common.api.DictApi", dictType="GLOBAL_SEX")
     */
    @ApiModelProperty(value = "字符字典")
    @Size(max = 10, message = "字符字典长度不能超过{max}")
    private String test7;
    /**
     * 整形字典;
     *
     * @Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Global.DATA_TYPE)
     */
    @ApiModelProperty(value = "整形字典")
    private Integer test12;
    /**
     * 用户;
     *
     * @Echo(api = EchoApi.POSITION_ID_CLASS)
     */
    @ApiModelProperty(value = "用户")
    private Long userId;
    /**
     * 组织;
     *
     * @Echo(api = EchoApi.ORG_ID_CLASS)
     */
    @ApiModelProperty(value = "组织")
    private Long orgId;
    /**
     * 小数
     */
    @ApiModelProperty(value = "小数")
    private BigDecimal test8;
    /**
     * 浮点2
     */
    @ApiModelProperty(value = "浮点2")
    private Float test9;
    /**
     * 浮点
     */
    @ApiModelProperty(value = "浮点")
    private Double test10;
    /**
     * xiao树
     */
    @ApiModelProperty(value = "xiao树")
    private BigDecimal test11;

}
