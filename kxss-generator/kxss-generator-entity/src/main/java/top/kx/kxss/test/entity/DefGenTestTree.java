package top.kx.kxss.test.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import top.kx.kxss.test.enumeration.DefGenTestTreeType2Enum;
import top.kx.kxss.test.enumeration.ProductType;
import top.kx.basic.base.entity.TreeEntity;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.baomidou.mybatisplus.annotation.SqlCondition.EQUAL;
import static top.kx.kxss.model.constant.Condition.LIKE;


/**
 * <p>
 * 实体类
 * 测试树结构
 * </p>
 *
 * <AUTHOR>
 * @date 2022-04-20 00:28:30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@TableName("def_gen_test_tree")
public class DefGenTestTree extends TreeEntity<DefGenTestTree, Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 名称
     */
    @TableField(value = "name", condition = LIKE)
    private String name;
    /**
     * 库存
     */
    @TableField(value = "stock", condition = EQUAL)
    private Integer stock;
    /**
     * 商品类型;
     * #ProductType{ordinary:普通;gift:赠品}
     */
    @TableField(value = "type_", condition = EQUAL)
    private ProductType type;
    /**
     * 商品类型2 ;
     * <p>
     * #{ordinary:01,普通;gift:02,赠品;}
     */
    @TableField(value = "type2", condition = EQUAL)
    private DefGenTestTreeType2Enum type2;
    /**
     * 学历;
     *
     * @Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS,  dictType = EchoDictType.Global.EDUCATION)
     */
    @TableField(value = "type3", condition = LIKE)
    private String type3;
    /**
     * 状态
     */
    @TableField(value = "state", condition = EQUAL)
    private Boolean state;
    /**
     * 测试
     */
    @TableField(value = "test4", condition = EQUAL)
    private Integer test4;
    /**
     * 时间
     */
    @TableField(value = "test5", condition = EQUAL)
    private LocalDate test5;
    /**
     * 日期
     */
    @TableField(value = "test6", condition = EQUAL)
    private LocalDateTime test6;
    /**
     * 名称
     */
    @TableField(value = "label", condition = LIKE)
    private String label;
    /**
     * 字符字典;
     *
     * @Echo(api = "top.kx.kxss.common.api.DictApi", dictType="GLOBAL_SEX")
     */
    @TableField(value = "test7", condition = LIKE)
    private String test7;
    /**
     * 整形字典;
     *
     * @Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Global.DATA_TYPE)
     */
    @TableField(value = "test12", condition = EQUAL)
    private Integer test12;
    /**
     * 用户;
     *
     * @Echo(api = EchoApi.POSITION_ID_CLASS)
     */
    @TableField(value = "user_id", condition = EQUAL)
    private Long userId;
    /**
     * 组织;
     *
     * @Echo(api = EchoApi.ORG_ID_CLASS)
     */
    @TableField(value = "org_id", condition = EQUAL)
    private Long orgId;
    /**
     * 小数
     */
    @TableField(value = "test8", condition = EQUAL)
    private BigDecimal test8;
    /**
     * 浮点2
     */
    @TableField(value = "test9", condition = EQUAL)
    private Float test9;
    /**
     * 浮点
     */
    @TableField(value = "test10", condition = EQUAL)
    private Double test10;
    /**
     * xiao树
     */
    @TableField(value = "test11", condition = EQUAL)
    private BigDecimal test11;


}
