package top.kx.kxss.generator.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.DownloadController;
import top.kx.basic.base.request.DownloadVO;
import top.kx.basic.base.service.SuperService;
import top.kx.kxss.generator.entity.DefGenTable;
import top.kx.kxss.generator.service.DefGenTableService;
import top.kx.kxss.generator.vo.query.DefGenTablePageQuery;
import top.kx.kxss.generator.vo.result.DefGenTableResultVO;
import top.kx.kxss.generator.vo.save.DefGenTableSaveVO;
import top.kx.kxss.generator.vo.save.ProjectGeneratorVO;
import top.kx.kxss.generator.vo.update.DefGenTableUpdateVO;

import javax.servlet.http.HttpServletResponse;


/**
 * <p>
 * 前端控制器
 * 项目生成
 * </p>
 *
 * <AUTHOR>
 * @date 2022-03-01
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/defGenProject")
@Api(value = "DefGenProject", tags = "项目生成")
public class DefGenProjectController implements DownloadController<Long, DefGenTable, DefGenTableSaveVO, DefGenTableUpdateVO, DefGenTablePageQuery, DefGenTableResultVO> {
    private final DefGenTableService defGenTableService;

    @Override
    public SuperService<Long, DefGenTable, DefGenTableSaveVO, DefGenTableUpdateVO, DefGenTablePageQuery, DefGenTableResultVO> getSuperService() {
        return defGenTableService;
    }

    @Override
    public Class<DefGenTable> getEntityClass() {
        return DefGenTable.class;
    }

    @Override
    public Class<DefGenTableResultVO> getResultVOClass() {
        return DefGenTableResultVO.class;
    }

    @ApiOperation(value = "获取默认配置", notes = "获取默认配置")
    @PostMapping("/getDef")
    @WebLog(value = "获取默认配置")
    public R<ProjectGeneratorVO> getDef() {

        return R.success(defGenTableService.getDef());
    }

    @PostMapping("/anno/getProperties")
    public R<Object> getProperties() {
        return R.success(System.getProperties());
    }


    @ApiOperation(value = "下载项目", notes = "下载项目")
    @PostMapping(value = "/download", produces = "application/octet-stream")
    @WebLog(value = "'下载项目")
    public void download(@RequestBody @Validated ProjectGeneratorVO projectGenerator, HttpServletResponse response) {
        DownloadVO download = defGenTableService.download(projectGenerator);
        write(download.getData(), download.getFileName(), response);
    }

    @ApiOperation(value = "生成项目", notes = "生成项目")
    @PostMapping("/generator")
    @WebLog(value = "'生成项目")
    public R<Boolean> generator(@RequestBody @Validated ProjectGeneratorVO projectGenerator) {
        defGenTableService.generator(projectGenerator);
        return R.success(true);
    }
}
