package top.kx.kxss.generator.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.kxss.generator.entity.DefGenTableColumn;
import top.kx.kxss.generator.service.DefGenTableColumnService;
import top.kx.kxss.generator.vo.query.DefGenTableColumnPageQuery;
import top.kx.kxss.generator.vo.result.DefGenTableColumnResultVO;
import top.kx.kxss.generator.vo.save.DefGenTableColumnSaveVO;
import top.kx.kxss.generator.vo.update.DefGenTableColumnUpdateVO;

/**
 * <p>
 * 前端控制器
 * 代码生成字段
 * </p>
 *
 * <AUTHOR>
 * @date 2022-03-24 14:13:43
 * @create [2022-03-24 14:13:43] [zuihou] [代码生成器生成]
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/defGenTableColumn")
@Api(value = "DefGenTableColumn", tags = "代码生成字段")
public class DefGenTableColumnController extends SuperController<DefGenTableColumnService, Long, DefGenTableColumn, DefGenTableColumnSaveVO,
        DefGenTableColumnUpdateVO, DefGenTableColumnPageQuery, DefGenTableColumnResultVO> {
    private final EchoService echoService;

    @Override
    public EchoService getEchoService() {
        return echoService;
    }


    @ApiOperation(value = "同步字段结构", notes = "同步字段结构")
    @PostMapping(value = "/syncField")
    @WebLog(value = "'同步字段结构")
    public R<Boolean> syncField(@RequestParam Long tableId, @RequestParam Long id) {
        return R.success(superService.syncField(tableId, id));
    }
}


