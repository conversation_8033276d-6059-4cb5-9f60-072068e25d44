package top.kx.kxss.generator.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.DownloadController;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.base.request.DownloadVO;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.kxss.generator.entity.DefGenTable;
import top.kx.kxss.generator.enumeration.FileOverrideStrategyEnum;
import top.kx.kxss.generator.enumeration.TemplateEnum;
import top.kx.kxss.generator.service.DefGenTableService;
import top.kx.kxss.generator.vo.query.DefGenTablePageQuery;
import top.kx.kxss.generator.vo.result.DefGenTableResultVO;
import top.kx.kxss.generator.vo.save.DefGenTableImportVO;
import top.kx.kxss.generator.vo.save.DefGenTableSaveVO;
import top.kx.kxss.generator.vo.save.DefGenVO;
import top.kx.kxss.generator.vo.update.DefGenTableUpdateVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * <p>
 * 前端控制器
 * 代码生成
 * </p>
 *
 * <AUTHOR>
 * @date 2022-03-01
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/defGenTable")
@Api(value = "DefGenTable", tags = "代码生成")
public class DefGenTableController
        extends SuperController<DefGenTableService, Long, DefGenTable, DefGenTableSaveVO, DefGenTableUpdateVO, DefGenTablePageQuery, DefGenTableResultVO>
        implements DownloadController<Long, DefGenTable, DefGenTableSaveVO, DefGenTableUpdateVO, DefGenTablePageQuery, DefGenTableResultVO> {

    private final EchoService echoService;

    @Override
    public EchoService getEchoService() {
        return echoService;
    }

    @ApiOperation(value = "分页查询代码生成表", notes = "分页查询代码生成表")
    @PostMapping("/selectTableList")
    @WebLog(value = "'分页列表查询:第' + #params?.current + '页, 显示' + #params?.size + '行'", response = false)
    public R<List<DefGenTable>> selectTableList(@RequestBody @Validated PageParams<DefGenTablePageQuery> params) {
        return R.success(superService.selectTableList(params.getModel().getDsId()));
    }

    @ApiOperation(value = "导入检测", notes = "导入检测")
    @PostMapping("/importCheck")
    @WebLog(value = "'导入检测")
    public R<Boolean> importCheck(@RequestBody @Validated List<String> tableNames) {
        return R.success(superService.importCheck(tableNames));
    }

    @ApiOperation(value = "导入表结构", notes = "导入表结构")
    @PostMapping(value = "/importTable")
    @WebLog(value = "'导入表结构", response = false)
    public R<Boolean> importTable(@RequestBody @Validated DefGenTableImportVO importVO) {
        return R.success(superService.importTable(importVO));
    }

    @ApiOperation(value = "同步表的字段", notes = "同步表的字段,新增或删除，不修改原来就存在的字段")
    @PostMapping("/syncField")
    @WebLog(value = "'同步表的字段")
    public R<Boolean> syncField(@RequestParam Long id) {
        superService.syncField(id);
        return R.success(true);
    }

    @ApiOperation(value = "批量查询", notes = "批量查询")
    @PostMapping("/findTableList")
    @WebLog(value = "'批量查询")
    public R<List<DefGenTableResultVO>> findTableList(@RequestBody List<Long> idList) {
        return R.success(superService.findTableList(idList));
    }

    @ApiOperation(value = "预览", notes = "预览")
    @PostMapping("/previewCode")
    @WebLog(value = "'预览")
    public R<Map<String, String>> previewCode(@RequestParam Long id, @RequestParam TemplateEnum template) {
        return R.success(superService.previewCode(id, template));
    }

    @ApiOperation(value = "批量生成代码", notes = "批量生成代码")
    @PostMapping("/generatorCode")
    @WebLog(value = "'批量生成代码")
    public R<Boolean> generatorCode(@RequestBody @Validated DefGenVO defGenVO) {
        superService.generatorCode(defGenVO);
        return R.success(true);
    }

    @ApiOperation(value = "批量下载代码", notes = "批量下载代码")
    @GetMapping(value = "/downloadZip", produces = "application/octet-stream")
    @WebLog(value = "'批量下载代码")
    public void downloadZip(HttpServletResponse response, @RequestParam List<Long> ids, @RequestParam TemplateEnum template) {
        DownloadVO download = superService.downloadZip(ids, template);
        write(download.getData(), download.getFileName(), response);
    }


    @Override
    public R<DefGenTableResultVO> getDetail(@RequestParam("id") Long id) {
        DefGenTableResultVO detail = superService.getDetail(id);
        echoService.action(detail);
        return R.success(detail);
    }

    @ApiOperation(value = "获取字段模板映射", notes = "获取字段模板映射")
    @GetMapping("/getFieldTemplate")
    @WebLog(value = "'获取字段模板映射")
    public R<Map<String, String>> getFieldTemplate() {
        return R.success(superService.getFieldTemplate());
    }

    @ApiOperation(value = "获取生成代码是否覆盖的默认配置", notes = "获取生成代码是否覆盖的默认配置")
    @GetMapping("/getDefFileOverrideStrategy")
    @WebLog(value = "'获取生成代码是否覆盖的默认配置")
    public R<Map<String, FileOverrideStrategyEnum>> getDefFileOverrideStrategy() {
        return R.success(superService.getDefFileOverrideStrategy());
    }


}
