package top.kx.kxss.base.vo.result;

import cn.hutool.core.map.MapUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.annotation.echo.Echo;
import top.kx.basic.base.entity.Entity;
import top.kx.basic.interfaces.echo.EchoVO;
import top.kx.kxss.base.vo.result.attribute.BaseAttributeResultVO;
import top.kx.kxss.base.vo.result.purchase.BasePurchaseDetailsResultVO;
import top.kx.kxss.model.constant.EchoApi;
import top.kx.kxss.model.constant.EchoDictType;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 表单查询方法返回值VO
 * 防伪信息
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-25 13:40:18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@ApiModel(value = "BaseSecurityCodeResultVO", description = "防伪信息")
public class BaseSecurityCodeResultVO extends Entity<Long> implements Serializable, EchoVO {

    private static final long serialVersionUID = 1L;

    private Map<String, Object> echoMap = MapUtil.newHashMap();

    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 序列号
     */
    @ApiModelProperty(value = "序列号")
    private String code;
    /**
     * 防伪码
     */
    @ApiModelProperty(value = "防伪码")
    private String securityCode;
    /**
     * 防伪链接
     */
    @ApiModelProperty(value = "防伪链接")
    private String url;
    /**
     * 绑定日期
     */
    @ApiModelProperty(value = "绑定日期")
    private LocalDateTime billDate;
    /**
     * 绑定日期
     */
    @ApiModelProperty(value = "绑定人员")
    @Echo(api = EchoApi.EMP_ID_CLASS)
    private Long bindUser;
    /**
     * 报废人员
     */
    @ApiModelProperty(value = "报废人员", hidden = true)
    @JsonIgnore
    private Long scrapUser;
    /**
     * 注册时间
     */
    @ApiModelProperty(value = "注册时间")
    private LocalDateTime registerTime;
    /**
     * 商品ID
     */
    @ApiModelProperty(value = "商品ID")
    @Echo(api = EchoApi.PRODUCT_ID_CLASS)
    private Long productId;
    /**
     * 仓库(字典)
     */
    @ApiModelProperty(value = "仓库(字典)")
    @Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Base.WARE_HOUSE)
    private String wareHouse;
    /**
     * 供应商(字典)
     */
    @ApiModelProperty(value = "供应商(字典)")
    @Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Base.SUPPLIER)
    private String supplier;
    /**
     * 会员ID
     */
    @ApiModelProperty(value = "会员ID")
    @Echo(api = EchoApi.MEMBER_ID_CLASS)
    private Long memberId;

    /**
     * 查询次数
     */
    @ApiModelProperty(value = "查询次数")
    private Integer selectNum;
    /**
     * 状态 1 已导入 2 已绑定 3 已注册 4 已报废
     */
    @ApiModelProperty(value = "状态 1 已导入 2 已绑定 3 已注册 4 已报废")
    @Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Base.CODE_STATUS)
    private String status;
    /**
     * 报废原因
     */
    @ApiModelProperty(value = "报废原因")
    @Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Base.SCRAP_REASON)
    private String scrapReason;

    /**
     * 报废时间
     */
    @ApiModelProperty(value = "报废时间")
    private LocalDateTime scrapTime;

    /**
     * 经销商
     */
    @ApiModelProperty(value = "经销商")
    @Echo(api = EchoApi.DISTRIBUTOR_ID_CLASS)
    private Long distributorId;

    /**
     * 时间
     */
    @ApiModelProperty(value = "时间")
    private LocalDateTime purchaseControlTime;

    /**
     * 经销商
     */
    @ApiModelProperty(value = "俱乐部信息")
    @Echo(api = EchoApi.DEALERS_ID_CLASS)
    private Long dealersId;


    @ApiModelProperty(value = "创建人")
    @Echo(api = EchoApi.USER_ID_CLASS)
    private Long createdBy;

    /**
     * 大标
     */
    @ApiModelProperty(value = "大标")
    private String bigCode;

    @ApiModelProperty(value = "拿货备注")
    private String purchaseRemarks;

    /**
     * 批次ID
     */
    @ApiModelProperty(value = "批次ID")
    private Long batchId;

    /**
     * 批次编号
     */
    @ApiModelProperty(value = "批次编号")
    private String batchCode;

    @ApiModelProperty(value = "属性规格")
    private List<BaseSecurityCodeAttributeResultVO> attributeResultVOList;
    @ApiModelProperty(value = "防伪码验证历史")
    private List<BaseSecurityCodeVerifyResultVO> verifyResultVOList;


    /**
     * 基础属性
     */
    @ApiModelProperty(value = "基础属性")
    private List<BaseAttributeResultVO> baseAttributeResultVOList;


    @ApiModelProperty(value = "属性详情列表")
    private List<BaseSecurityCodeAttributeDetailsResultVO> attributeResultVO;


    @ApiModelProperty(value = "防伪码时间轴")
    private List<BaseSecurityCodeTimeResultVO> timeResultVOList;

    /**
     * 一级经销商拿货
     */
    @ApiModelProperty(value = "一级经销商拿货")
    private BasePurchaseDetailsResultVO firstLevelPurchaseVO;

    /**
     * 二级经销商拿货
     */
    @ApiModelProperty(value = "二级经销商拿货")
    private BasePurchaseDetailsResultVO secondLevelPurchaseVO;


}
