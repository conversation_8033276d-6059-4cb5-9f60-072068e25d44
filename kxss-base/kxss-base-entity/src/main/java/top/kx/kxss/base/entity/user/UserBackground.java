package top.kx.kxss.base.entity.user;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.Entity;

import static com.baomidou.mybatisplus.annotation.SqlCondition.EQUAL;
import static top.kx.kxss.model.constant.Condition.LIKE;


/**
 * <p>
 * 实体类
 * 用户背景图
 * </p>
 *
 * <AUTHOR>
 * @date 2025-04-19 16:25:46
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@TableName("def_user_background")
public class UserBackground extends Entity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 应用分类
     */
    @TableField(value = "app_key", condition = LIKE)
    private String appKey;
    /**
     * 用户ID
     */
    @TableField(value = "user_id", condition = EQUAL)
    private Long userId;
    /**
     * 文件id
     */
    @TableField(value = "file_id", condition = EQUAL)
    private Long fileId;
    /**
     * 文件地址
     */
    @TableField(value = "file_url", condition = LIKE)
    private String fileUrl;
    /**
     * 备注
     */
    @TableField(value = "remarks", condition = LIKE)
    private String remarks;
    /**
     * 创建人组织
     */
    @TableField(value = "created_org_id", condition = EQUAL)
    private Long createdOrgId;


}
