package top.kx.kxss.base.vo.result.purchase;

import cn.hutool.core.map.MapUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.annotation.echo.Echo;
import top.kx.basic.base.entity.Entity;
import top.kx.basic.interfaces.echo.EchoVO;
import top.kx.kxss.model.constant.EchoApi;
import top.kx.kxss.model.constant.EchoDictType;

import java.io.Serializable;
import java.util.Map;

/**
 * <p>
 * 表单查询方法返回值VO
 * 要货详情
 * </p>
 *
 * <AUTHOR>
 * @date 2025-04-10 14:47:18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@ApiModel(value = "BasePurchaseDetailsResultVO", description = "要货详情")
public class BasePurchaseDetailsResultVO extends Entity<Long> implements Serializable, EchoVO {

    private static final long serialVersionUID = 1L;

    private Map<String, Object> echoMap = MapUtil.newHashMap();

    @ApiModelProperty(value = "主键")
    private Long id;

    /**
    * 防伪码
    */
    @ApiModelProperty(value = "防伪码")
    private String securityCode;
    /**
    * 商品id
    */
    @ApiModelProperty(value = "商品id")
    private Long productId;
    /**
    * 商品名称
    */
    @ApiModelProperty(value = "商品名称")
    private String productName;
    /**
    * 经销商ID
    */
    @ApiModelProperty(value = "经销商ID")
    @Echo(api = EchoApi.DISTRIBUTOR_ID_CLASS)
    private Long distributorId;
    /**
    * 拿货id
    */
    @ApiModelProperty(value = "拿货id")
    private Long purchaseId;
    /**K
    * 操作员工
    */
    @ApiModelProperty(value = "操作员工")
    @Echo(api = EchoApi.EMP_ID_CLASS)
    private Long purchaseEmp;
    /**
    * 要货录入方式
    */
    @ApiModelProperty(value = "要货录入方式")
    @Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Base.ENTER_TYPE)
    private String purchaseEnterType;
    /**
    * 拿货备注
    */
    @ApiModelProperty(value = "拿货备注")
    private String remarks;
    /**
    * 创建人组织
    */
    @ApiModelProperty(value = "创建人组织")
    private Long createdOrgId;



}
