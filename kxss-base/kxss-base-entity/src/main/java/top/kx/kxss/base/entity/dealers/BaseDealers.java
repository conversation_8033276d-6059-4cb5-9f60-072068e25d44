package top.kx.kxss.base.entity.dealers;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import static com.baomidou.mybatisplus.annotation.SqlCondition.EQUAL;
import static top.kx.kxss.model.constant.Condition.LIKE;
import top.kx.basic.base.entity.Entity;


/**
 * <p>
 * 实体类
 * 俱乐部信息
 * </p>
 *
 * <AUTHOR>
 * @date 2024-12-20 10:47:35
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@TableName("base_dealers")
public class BaseDealers extends Entity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 类型 1 新球房 2 老球房升级
     */
    @TableField(value = "type", condition = LIKE)
    private String type;
    /**
     * 姓名
     */
    @TableField(value = "name", condition = LIKE)
    private String name;
    /**
     * 手机号
     */
    @TableField(value = "mobile", condition = LIKE)
    private String mobile;
    /**
     * 球房名称
     */
    @TableField(value = "ball_room_name", condition = LIKE)
    private String ballRoomName;
    /**
     * 性别
     */
    @TableField(value = "sex", condition = LIKE)
    private String sex;
    /**
     * 用户ID
     */
    @TableField(value = "user_id", condition = EQUAL)
    private Long userId;
    /**
     * 备注
     */
    @TableField(value = "remarks", condition = LIKE)
    private String remarks;
    /**
     * 商品注册数
     */
    @TableField(value = "product_num", condition = EQUAL)
    private Integer productNum;
    /**
     * 详细地址
     */
    @TableField(value = "address", condition = LIKE)
    private String address;
    /**
     * 区域-数组
     */
    @TableField(value = "regions", condition = LIKE)
    private String regions;
    /**
     * 创建人组织
     */
    @TableField(value = "created_org_id", condition = EQUAL)
    private Long createdOrgId;
    /**
     * 删除标识 0 未删除 1 已删除
     */
    @TableField(value = "delete_flag", condition = EQUAL)
    private Integer deleteFlag;
    /**
     * 头像ID
     */
    @TableField(value = "avatar_id", condition = EQUAL)
    private Long avatarId;



}
