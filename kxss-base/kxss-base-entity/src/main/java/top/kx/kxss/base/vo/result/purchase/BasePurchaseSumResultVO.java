package top.kx.kxss.base.vo.result.purchase;

import cn.hutool.core.map.MapUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.Entity;
import top.kx.basic.interfaces.echo.EchoVO;

import java.io.Serializable;
import java.util.Map;

/**
 * <p>
 * 表单查询方法返回值VO
 * 要货
 * </p>
 *
 * <AUTHOR>
 * @date 2025-03-10 14:57:47
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@ApiModel(value = "BasePurchaseResultVO", description = "要货")
public class BasePurchaseSumResultVO extends Entity<Long> implements Serializable, EchoVO {

    private static final long serialVersionUID = 1L;

    private Map<String, Object> echoMap = MapUtil.newHashMap();


    @ApiModelProperty(value = "商品数量")
    private Long productNum;

    @ApiModelProperty(value = "拿货数量")
    private Long purchaseNum;
    /**
     * 总数量
     */
    @ApiModelProperty(value = "总数量")
    private Integer totalNum;

    @ApiModelProperty(value = "拿货数量")
    private Integer num;

    @ApiModelProperty(value = "下级拿货数量")
    private Integer childNum;



}
