package top.kx.kxss.base.vo.update.security;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.SuperEntity;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * <p>
 * 表单修改方法VO
 * 防伪码批次
 * </p>
 *
 * <AUTHOR>
 * @date 2025-04-25 17:45:12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode
@Builder
@ApiModel(value = "BaseSecurityCodeBatchUpdateVO", description = "防伪码批次")
public class BaseSecurityCodeBatchUpdateVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotNull(message = "请填写主键", groups = SuperEntity.Update.class)
    private Long id;

    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
    @NotEmpty(message = "请填写批次号")
    @Size(max = 20, message = "批次号长度不能超过{max}")
    private String batchCode;
    /**
     * 生成数量
     */
    @ApiModelProperty(value = "生成数量")
    @NotNull(message = "请填写生成数量")
    private Long num;
    /**
     * 商品ID
     */
    @ApiModelProperty(value = "商品ID")
    private Long productId;
    /**
     * 大商品ID
     */
    @ApiModelProperty(value = "大商品ID")
    private Long bigProductId;
    /**
     * 跳转类型:1-小程序，2-H5
     */
    @ApiModelProperty(value = "跳转类型:1-小程序，2-H5")
    @Size(max = 1, message = "跳转类型:1-小程序，2-H5长度不能超过{max}")
    private String linkType;
    /**
     * 大跳转类型:1-小程序，2-H5
     */
    @ApiModelProperty(value = "大跳转类型:1-小程序，2-H5")
    @Size(max = 1, message = "大跳转类型:1-小程序，2-H5长度不能超过{max}")
    private String bigLinkType;
    /**
     * 链接地址
     */
    @ApiModelProperty(value = "链接地址")
    @Size(max = 255, message = "链接地址长度不能超过{max}")
    private String linkUrl;
    /**
     * 大链接地址
     */
    @ApiModelProperty(value = "大链接地址")
    @Size(max = 255, message = "大链接地址长度不能超过{max}")
    private String bigLinkUrl;
    /**
     * 是否子母码
     */
    @ApiModelProperty(value = "是否子母码")
    private Boolean isSeries;
    /**
     * 子母码比例
     */
    @ApiModelProperty(value = "子母码比例")
    private Integer seriesRatio;
    /**
     * 状态;[1-生成中。2-已生成]
     */
    @ApiModelProperty(value = "状态")
    @Size(max = 1, message = "状态长度不能超过{max}")
    private String state;
    /**
     * 导入状态;[1-未导入,2-导入中,3-已经导入]
     */
    @ApiModelProperty(value = "导入状态")
    @Size(max = 1, message = "导入状态长度不能超过{max}")
    private String importState;
    /**
     * 企业ID
     */
    @ApiModelProperty(value = "企业ID")
    private Long enterpriseId;
    /**
     * 物流方式
     */
    @ApiModelProperty(value = "物流方式")
    @Size(max = 255, message = "物流方式长度不能超过{max}")
    private String logisticsType;
    /**
     * 物流日期
     */
    @ApiModelProperty(value = "物流日期")
    private LocalDate logisticsDate;
    /**
     * 创建员工
     */
    @ApiModelProperty(value = "创建员工")
    private Long createdEmp;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Size(max = 255, message = "备注长度不能超过{max}")
    private String remarks;
    /**
     * 创建人组织
     */
    @ApiModelProperty(value = "创建人组织")
    private Long createdOrgId;


}
