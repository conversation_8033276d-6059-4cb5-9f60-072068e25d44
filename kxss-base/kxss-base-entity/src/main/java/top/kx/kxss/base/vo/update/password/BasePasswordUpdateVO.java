package top.kx.kxss.base.vo.update.password;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <p>
 * 密码验证配置更新VO
 * </p>
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Builder
@ApiModel(value = "BasePasswordConfigUpdateVO", description = "密码验证配置更新VO")
public class BasePasswordUpdateVO {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID", required = true)
    @NotNull(message = "主键ID不能为空")
    private Long id;

    /**
     * 验证密码（原始密码，会自动加密）
     */
    @ApiModelProperty(value = "验证密码", required = true)
    @NotBlank(message = "验证密码不能为空")
    private String password;

}