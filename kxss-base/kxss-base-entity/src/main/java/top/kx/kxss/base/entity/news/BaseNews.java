package top.kx.kxss.base.entity.news;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.Entity;

import static com.baomidou.mybatisplus.annotation.SqlCondition.EQUAL;
import static top.kx.kxss.model.constant.Condition.LIKE;


/**
 * <p>
 * 实体类
 * 新闻资讯
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-25 16:12:08
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@TableName("base_news")
public class BaseNews extends Entity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 标题
     */
    @TableField(value = "title", condition = LIKE)
    private String title;
    /**
     * 内容
     */
    @TableField(value = "content", condition = LIKE)
    private String content;
    /**
     * 封面图
     */
    @TableField(value = "cover_image", condition = LIKE, fill = FieldFill.UPDATE)
    private String coverImage;
    /**
     * 作者
     */
    @TableField(value = "author", condition = LIKE)
    private String author;
    /**
     * 浏览量
     */
    @TableField(value = "view_count", condition = EQUAL)
    private Integer viewCount;
    /**
     * 类型
     */
    @TableField(value = "type_", condition = LIKE)
    private String type;
    /**
     * 跳转地址
     */
    @TableField(value = "jump_url", condition = LIKE)
    private String jumpUrl;
    /**
     * 状态 10 已创建 20 已发布 30 已撤销
     */
    @TableField(value = "status", condition = LIKE)
    private String status;
    /**
     * 创建人组织
     */
    @TableField(value = "created_org_id", condition = EQUAL)
    private Long createdOrgId;


}
