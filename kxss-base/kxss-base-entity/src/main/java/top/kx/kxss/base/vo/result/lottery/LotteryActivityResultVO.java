package top.kx.kxss.base.vo.result.lottery;

import cn.hutool.core.map.MapUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.annotation.echo.Echo;
import top.kx.basic.base.entity.Entity;
import top.kx.basic.interfaces.echo.EchoVO;
import top.kx.kxss.model.constant.EchoApi;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 表单查询方法返回值VO
 * 抽奖活动表
 * </p>
 *
 * <AUTHOR>
 * @date 2025-09-15 17:23:11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@ApiModel(value = "LotteryActivityResultVO", description = "抽奖活动表")
public class LotteryActivityResultVO extends Entity<Long> implements Serializable, EchoVO {

    private static final long serialVersionUID = 1L;

    private Map<String, Object> echoMap = MapUtil.newHashMap();

    @ApiModelProperty(value = "活动ID")
    private Long id;

    /**
    * 活动名称
    */
    @ApiModelProperty(value = "活动名称")
    private String code;
    /**
    * 活动名称
    */
    @ApiModelProperty(value = "活动名称")
    private String name;
    /**
    * 玩法类型: 1=大转盘,2=九宫格,3=摇一摇
    */
    @ApiModelProperty(value = "玩法类型: 1=大转盘,2=九宫格,3=摇一摇")
    private String playType;
    /**
    * 活动类型: 1=注册抽奖
    */
    @ApiModelProperty(value = "活动类型: 1=注册抽奖")
    private String type;
    /**
    * 活动描述
    */
    @ApiModelProperty(value = "活动描述")
    private String description;
    /**
    * 开始时间
    */
    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;
    /**
    * 结束时间
    */
    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;
    /**
    * 状态: 0=未开始,1=进行中,2=已结束,3=已下架
    */
    @ApiModelProperty(value = "状态: 0=未开始,1=进行中,2=已结束,3=已下架")
    private String status;
    /**
    * 排序
    */
    @ApiModelProperty(value = "排序 ")
    private Integer sortValue;
    /**
    * 每用户总抽奖次数限制 (0=不限)
    */
    @ApiModelProperty(value = "每用户总抽奖次数限制 (0=不限)")
    private Integer totalLimit;
    /**
    * 系统赠送初始抽奖机会数(如注册即送)
    */
    @ApiModelProperty(value = "系统赠送初始抽奖机会数(如注册即送)")
    private Integer initialChances;
    /**
    * 备注
    */
    @ApiModelProperty(value = "备注")
    private String remarks;
    /**
     * 员工ID
     */
    @ApiModelProperty(value = "员工ID")
    @Echo(api = EchoApi.EMP_ID_CLASS)
    private Long employeeId;

    /**
     * 参与人次
     */
    @ApiModelProperty(value = "参与人次")
    private Long participateCount;

    /**
     * 中奖人数
     */
    @ApiModelProperty(value = "中奖人数")
    private Long winCount;

    /**
     * 抽奖次数（总抽奖次数）
     */
    @ApiModelProperty(value = "抽奖次数（总抽奖次数）")
    private Long lotteryCount;
    
    /**
     * 中奖次数（总中奖次数）
     */
    @ApiModelProperty(value = "中奖次数（总中奖次数）")
    private Long prizeCount;

    /**
     * 中奖率
     */
    @ApiModelProperty(value = "中奖率")
    private BigDecimal winRate;

    @ApiModelProperty(value = "奖品")
    private List<LotteryActivityPrizeResultVO> prizeList;



    @ApiModelProperty(value = "活动批次")
    private List<String> batchList;



}
