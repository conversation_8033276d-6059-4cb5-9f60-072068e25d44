package top.kx.kxss.base.entity.product;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.Entity;

import static com.baomidou.mybatisplus.annotation.SqlCondition.EQUAL;


/**
 * <p>
 * 实体类
 * 商品基础属性关联
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-16 18:44:16
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@TableName("base_product_attribute")
public class BaseProductAttribute extends Entity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 商品ID
     */
    @TableField(value = "product_id", condition = EQUAL)
    private Long productId;
    /**
     * 属性ID
     */
    @TableField(value = "attribute_id", condition = EQUAL)
    private Long attributeId;
    /**
     * 创建人组织
     */
    @TableField(value = "created_org_id", condition = EQUAL)
    private Long createdOrgId;



}
