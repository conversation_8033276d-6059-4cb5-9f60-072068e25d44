package top.kx.kxss.base.entity.question;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.Entity;

import static com.baomidou.mybatisplus.annotation.SqlCondition.EQUAL;
import static top.kx.kxss.model.constant.Condition.LIKE;


/**
 * <p>
 * 实体类
 * 常见问题
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-26 16:24:10
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@TableName("base_question")
public class BaseQuestion extends Entity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 问题名称
     */
    @TableField(value = "name", condition = LIKE)
    private String name;
    /**
     * 回答
     */
    @TableField(value = "answer", condition = LIKE)
    private String answer;
    /**
     * 是否启用 0 停用 1 启用
     */
    @TableField(value = "state", condition = EQUAL)
    private Boolean state;
    /**
     * 排序
     */
    @TableField(value = "sort_value", condition = EQUAL)
    private Integer sortValue;
    /**
     * 创建人组织
     */
    @TableField(value = "created_org_id", condition = EQUAL)
    private Long createdOrgId;



}
