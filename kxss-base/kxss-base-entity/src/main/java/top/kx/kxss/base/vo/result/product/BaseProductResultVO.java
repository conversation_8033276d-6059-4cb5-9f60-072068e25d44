package top.kx.kxss.base.vo.result.product;

import cn.hutool.core.map.MapUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.annotation.echo.Echo;
import top.kx.basic.base.entity.Entity;
import top.kx.basic.interfaces.echo.EchoVO;
import top.kx.kxss.base.vo.result.BaseSecurityCodeAttributeDetailsResultVO;
import top.kx.kxss.base.vo.result.BaseSecurityCodeTimeResultVO;
import top.kx.kxss.base.vo.result.BaseSecurityCodeVerifyResultVO;
import top.kx.kxss.file.entity.File;
import top.kx.kxss.model.constant.EchoApi;
import top.kx.kxss.model.constant.EchoDictType;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 表单查询方法返回值VO
 * 商品表
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-25 13:52:40
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@ApiModel(value = "BaseProductResultVO", description = "商品表")
public class BaseProductResultVO extends Entity<Long> implements Serializable, EchoVO {

    private static final long serialVersionUID = 1L;

    private Map<String, Object> echoMap = MapUtil.newHashMap();

    @ApiModelProperty(value = "ID")
    private Long id;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String name;
    /**
     * 商品分类
     */
    @ApiModelProperty(value = "商品分类(字典值：PRODUCT_CATEGORY)")
    @Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Base.PRODUCT_CATEGORY)
    private String category;
    /**
     * 状态;[引用字典id]
     */
    @ApiModelProperty(value = "状态")
    private Boolean state;

    @ApiModelProperty(value = "排序")
    private Integer sortValue;
    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String code;
    /**
     * 计量单位[字典]
     */
    @ApiModelProperty(value = "计量单位(字典值：MEASURING_UNIT)")
    @Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Base.MEASURING_UNIT)
    private String measuringUnit;
    /**
     * 规格
     */
    @ApiModelProperty(value = "规格")
    private String spec;
    /**
     * 详细信息
     */
    @ApiModelProperty(value = "详细信息")
    private String detail;
    /**
     * 商品图片
     */
    @ApiModelProperty(value = "商品图片")
    private Long productImage;

    @ApiModelProperty(value = "商品信息")
    private File productImageFile;
    /**
     * 创建人组织
     */
    @ApiModelProperty(value = "创建人组织", hidden = true)
    private Long createdOrgId;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remarks;
    /**
     * 品牌
     */
    @ApiModelProperty(value = "品牌")
    private String brand;

    /**
     * 是否注册 false 未注册 true 已注册
     */
    @ApiModelProperty(value = "是否注册 false 未注册 true 已注册")
    private Boolean isRegister;
    /**
     * 注册时间
     */
    @ApiModelProperty(value = "注册时间")
    private LocalDateTime registerTime;

    /**
     * 防伪码
     */
    @ApiModelProperty(value = "防伪码")
    private String securityCode;
    /**
     * 客户端应用ID
     */
    @ApiModelProperty(value = "客户端应用ID")
    @Echo(api = EchoApi.DEF_CLIENT_IMPL_CLASS)
    private Long clientId;

    /**
     * 查询次数
     */
    @ApiModelProperty(value = "查询次数")
    private Integer selectNum;

    @ApiModelProperty(value = "属性")
    @Echo(api = EchoApi.ATTRIBUTE_ID_CLASS)
    private List<Long> attributeIds;

    @ApiModelProperty(value = "属性详情列表")
    private List<BaseSecurityCodeAttributeDetailsResultVO> attributeResultVO;

    @ApiModelProperty(value = "防伪码时间")
    private List<BaseSecurityCodeTimeResultVO> timeResultVOList;

    @ApiModelProperty(value = "防伪码本次验证")
    private BaseSecurityCodeVerifyResultVO verifyResultVO;


}
