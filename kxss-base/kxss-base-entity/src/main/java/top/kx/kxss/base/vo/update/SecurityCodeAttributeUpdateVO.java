package top.kx.kxss.base.vo.update;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.SuperEntity;
import top.kx.kxss.base.vo.save.BaseSecurityCodeAttributeSaveVO;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 表单修改方法VO
 * 防伪信息规格属性
 * </p>
 *
 * <AUTHOR>
 * @date 2025-02-26 16:17:06
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode
@Builder
@ApiModel(value = "SecurityCodeAttributeUpdateVO", description = "防伪信息规格属性")
public class SecurityCodeAttributeUpdateVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 防伪码
     */
    @ApiModelProperty(value = "防伪码")
    @NotBlank(message = "请填写防伪码")
    @Size(max = 255, message = "防伪码长度不能超过{max}")
    private String securityCode;


    @NotEmpty(message = "请填写防伪码规格属性")
    @ApiModelProperty(value = "防伪码规格属性")
    private List<BaseSecurityCodeAttributeSaveVO> attributeSaveVOList;


}
