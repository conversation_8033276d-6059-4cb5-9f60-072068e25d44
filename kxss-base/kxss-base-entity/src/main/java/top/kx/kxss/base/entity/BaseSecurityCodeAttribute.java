package top.kx.kxss.base.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.Entity;

import static com.baomidou.mybatisplus.annotation.SqlCondition.EQUAL;
import static top.kx.kxss.model.constant.Condition.LIKE;


/**
 * <p>
 * 实体类
 * 防伪信息规格属性
 * </p>
 *
 * <AUTHOR>
 * @date 2025-02-26 16:17:06
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@TableName("base_security_code_attribute")
public class BaseSecurityCodeAttribute extends Entity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 商品ID
     */
    @TableField(value = "product_id", condition = EQUAL)
    private Long productId;
    /**
     * 防伪码
     */
    @TableField(value = "security_code", condition = LIKE)
    private String securityCode;
    /**
     * 防伪ID
     */
    @TableField(value = "security_code_id", condition = EQUAL)
    private Long securityCodeId;
    /**
     * 创建人组织
     */
    @TableField(value = "created_org_id", condition = EQUAL)
    private Long createdOrgId;



}
