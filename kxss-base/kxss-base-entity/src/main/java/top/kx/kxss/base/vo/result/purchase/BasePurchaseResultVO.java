package top.kx.kxss.base.vo.result.purchase;

import cn.hutool.core.map.MapUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.annotation.echo.Echo;
import top.kx.basic.base.entity.Entity;
import top.kx.basic.interfaces.echo.EchoVO;
import top.kx.kxss.model.constant.EchoApi;

import java.io.Serializable;
import java.util.Map;

/**
 * <p>
 * 表单查询方法返回值VO
 * 要货
 * </p>
 *
 * <AUTHOR>
 * @date 2025-03-10 14:57:47
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@ApiModel(value = "BasePurchaseResultVO", description = "要货")
public class BasePurchaseResultVO extends Entity<Long> implements Serializable, EchoVO {

    private static final long serialVersionUID = 1L;

    private Map<String, Object> echoMap = MapUtil.newHashMap();

    @ApiModelProperty(value = "主键")
    private Long id;

    /**
    * 编号
    */
    @ApiModelProperty(value = "编号")
    private String code;
    /**
    * 商品id
    */
    @ApiModelProperty(value = "商品id")
    @Echo(api = EchoApi.PRODUCT_ID_CLASS)
    private Long productId;
    /**
    * 经销商ID
    */
    @ApiModelProperty(value = "经销商ID")
    @Echo(api = EchoApi.DISTRIBUTOR_ID_CLASS)
    private Long distributorId;
    /**
    * 创建人
    */
    @ApiModelProperty(value = "操作员工")
    @Echo(api = EchoApi.EMP_ID_CLASS)
    private Long employeeId;

    @ApiModelProperty(value = "数量")
    private Integer num;


    @ApiModelProperty(value = "下级数量")
    private Integer childNum;
    /**
    * 备注
    */
    @ApiModelProperty(value = "备注")
    private String remarks;
    /**
    * 创建人组织
    */
    @ApiModelProperty(value = "创建人组织")
    private Long createdOrgId;



}
