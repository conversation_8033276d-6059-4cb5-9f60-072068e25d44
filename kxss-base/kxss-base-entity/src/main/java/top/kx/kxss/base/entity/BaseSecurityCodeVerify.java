package top.kx.kxss.base.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.Entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static com.baomidou.mybatisplus.annotation.SqlCondition.EQUAL;
import static top.kx.kxss.model.constant.Condition.LIKE;


/**
 * <p>
 * 实体类
 * 防伪验证信息
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-06 16:18:00
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@TableName("base_security_code_verify")
public class BaseSecurityCodeVerify extends Entity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 防伪码
     */
    @TableField(value = "security_code", condition = LIKE)
    private String securityCode;
    /**
     * 验证时间
     */
    @TableField(value = "verify_time", condition = EQUAL)
    private LocalDateTime verifyTime;
    /**
     * 第几次验证
     */
    @TableField(value = "verify_count", condition = EQUAL)
    private Integer verifyCount;
    /**
     * 验证地址
     */
    @TableField(value = "address", condition = LIKE)
    private String address;
    /**
     * 经度
     */
    @TableField(value = "longitude", condition = EQUAL)
    private BigDecimal longitude;
    /**
     * 纬度
     */
    @TableField(value = "latitude", condition = EQUAL)
    private BigDecimal latitude;
    /**
     * 验证用户ID
     */
    @TableField(value = "user_id", condition = EQUAL)
    private Long userId;
    /**
     * 创建人组织
     */
    @TableField(value = "created_org_id", condition = EQUAL)
    private Long createdOrgId;



}
