package top.kx.kxss.base.entity.password;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.Entity;

import static com.baomidou.mybatisplus.annotation.SqlCondition.EQUAL;
import static top.kx.kxss.model.constant.Condition.LIKE;

/**
 * <p>
 * 密码验证配置表
 * 用于存储不同业务模块的密码验证配置
 * </p>
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@TableName("base_password_config")
public class BasePasswordConfig extends Entity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 业务模块代码
     */
    @TableField(value = "business_code", condition = EQUAL)
    private String businessCode;

    /**
     * 业务模块名称
     */
    @TableField(value = "business_name", condition = LIKE)
    private String businessName;

    /**
     * 验证密码（加密后）
     */
    @TableField(value = "password", condition = EQUAL)
    private String password;

    /**
     * 是否启用密码验证
     */
    @TableField(value = "is_enabled", condition = EQUAL)
    private Boolean isEnabled;

    /**
     * 备注
     */
    @TableField(value = "remarks", condition = LIKE)
    private String remarks;

    @TableField(value = "created_org_id", condition = EQUAL)
    private Long createdOrgId;
}