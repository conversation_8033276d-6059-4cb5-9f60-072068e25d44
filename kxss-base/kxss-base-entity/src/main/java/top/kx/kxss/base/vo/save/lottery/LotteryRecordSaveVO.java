package top.kx.kxss.base.vo.save.lottery;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 表单保存方法VO
 * 用户抽奖记录表
 * </p>
 *
 * <AUTHOR>
 * @date 2025-09-15 17:23:11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode
@Builder
@ApiModel(value = "LotteryRecordSaveVO", description = "用户抽奖记录表")
public class LotteryRecordSaveVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 活动ID
     */
    @ApiModelProperty(value = "活动ID")
    @NotNull(message = "请填写活动ID")
    private Long activityId;
    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    @NotNull(message = "请填写用户ID")
    private Long userId;
    /**
     * 抽奖机会ID
     */
    @ApiModelProperty(value = "抽奖机会ID")
    @NotNull(message = "请填写抽奖机会ID")
    private Long chanceId;
    /**
     * 奖品库ID (引用原始奖品)
     */
    @ApiModelProperty(value = "奖品库ID (引用原始奖品)")
    @NotNull(message = "请填写奖品库ID (引用原始奖品)")
    private Long prizeId;
    /**
     * 奖品名称快照
     */
    @ApiModelProperty(value = "奖品名称快照")
    @Size(max = 100, message = "奖品名称快照长度不能超过{max}")
    private String prizeName;
    /**
     * 奖品类型快照: 1=谢谢参与,2=积分,3=优惠券,4=代金券,5=实物
     */
    @ApiModelProperty(value = "奖品类型快照: 1=谢谢参与,2=积分,3=优惠券,4=代金券,5=实物")
    private String prizeType;
    /**
     * 奖品面额/积分数快照
     */
    @ApiModelProperty(value = "奖品面额/积分数快照")
    private BigDecimal valueAmount;
    /**
     * 使用门槛(金额门槛快照)
     */
    @ApiModelProperty(value = "使用门槛(金额门槛快照)")
    private BigDecimal threshold;
    /**
     * 优惠券编码快照
     */
    @ApiModelProperty(value = "优惠券编码快照")
    @Size(max = 100, message = "优惠券编码快照长度不能超过{max}")
    private String couponCode;
    /**
     * 状态: 0=未中奖,1=已中奖
     */
    @ApiModelProperty(value = "状态: 0=未中奖,1=已中奖")
    @NotNull(message = "请填写状态: 0=未中奖,1=已中奖")
    private String status;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Size(max = 200, message = "备注长度不能超过{max}")
    private String remarks;




}
