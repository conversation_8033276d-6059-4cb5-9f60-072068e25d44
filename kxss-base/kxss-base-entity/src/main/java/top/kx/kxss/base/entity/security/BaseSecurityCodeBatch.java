package top.kx.kxss.base.entity.security;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.Entity;

import java.time.LocalDate;

import static com.baomidou.mybatisplus.annotation.SqlCondition.EQUAL;
import static top.kx.kxss.model.constant.Condition.LIKE;


/**
 * <p>
 * 实体类
 * 防伪码批次
 * </p>
 *
 * <AUTHOR>
 * @date 2025-04-25 17:45:12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@TableName("base_security_code_batch")
public class BaseSecurityCodeBatch extends Entity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 批次号
     */
    @TableField(value = "batch_code", condition = LIKE)
    private String batchCode;
    /**
     * 生成数量
     */
    @TableField(value = "num", condition = EQUAL)
    private Long num;
    /**
     * 商品ID
     */
    @TableField(value = "product_id", condition = EQUAL)
    private Long productId;

    /**
     * 大码商品ID
     */
    @TableField(value = "big_product_id", condition = EQUAL)
    private Long bigProductId;
    /**
     * 跳转类型:1-小程序，2-H5
     */
    @TableField(value = "link_type", condition = LIKE)
    private String linkType;

    @TableField(value = "big_link_type", condition = LIKE)
    private String bigLinkType;
    /**
     * 链接地址
     */
    @TableField(value = "link_url", condition = LIKE)
    private String linkUrl;

    @TableField(value = "big_link_url", condition = LIKE)
    private String bigLinkUrl;

    /**
     * 是否子母码
     */
    @TableField(value = "is_series", condition = EQUAL)
    private Boolean isSeries;
    /**
     * 子母码比例
     */
    @TableField(value = "series_ratio", condition = EQUAL)
    private Integer seriesRatio;
    /**
     * 状态;[1-生成中。2-已生成]
     */
    @TableField(value = "state", condition = LIKE)
    private String state;

    @TableField(value = "state_desc", condition = LIKE)
    private String stateDesc;
    /**
     * 导入状态;[1-未导入,2-导入中,3-已经导入]
     */
    @TableField(value = "import_state", condition = LIKE)
    private String importState;

    @TableField(value = "import_state_desc", condition = LIKE)
    private String importStateDesc;
    /**
     * 企业ID
     */
    @TableField(value = "enterprise_id", condition = EQUAL)
    private Long enterpriseId;
    /**
     * 物流方式
     */
    @TableField(value = "logistics_type", condition = LIKE)
    private String logisticsType;
    /**
     * 物流日期
     */
    @TableField(value = "logistics_date", condition = EQUAL)
    private LocalDate logisticsDate;
    /**
     * 创建员工
     */
    @TableField(value = "created_emp", condition = EQUAL)
    private Long createdEmp;
    /**
     * 备注
     */
    @TableField(value = "remarks", condition = LIKE)
    private String remarks;
    /**
     * 创建人组织
     */
    @TableField(value = "created_org_id", condition = EQUAL)
    private Long createdOrgId;



}
