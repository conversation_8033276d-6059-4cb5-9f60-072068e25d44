package top.kx.kxss.base.vo.query.security;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;


/**
 * <p>
 * 表单查询条件VO
 * 防伪码批次-详情
 * </p>
 *
 * <AUTHOR>
 * @date 2025-04-25 17:45:12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode
@Builder
@ApiModel(value = "BaseSecurityCodeBatchDetailsPageQuery", description = "防伪码批次-详情")
public class BaseSecurityCodeBatchDetailsPageQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private Long id;

    /**
    * 批次id
    */
    @ApiModelProperty(value = "批次id")
    private Long batchId;
    /**
    * 防伪码
    */
    @ApiModelProperty(value = "防伪码")
    private String securityCode;
    /**
    * 防伪链接
    */
    @ApiModelProperty(value = "防伪链接")
    private String url;
    /**
    * 大标
    */
    @ApiModelProperty(value = "大标")
    private String bigCode;
    /**
    * 创建人组织
    */
    @ApiModelProperty(value = "创建人组织")
    private Long createdOrgId;



}
