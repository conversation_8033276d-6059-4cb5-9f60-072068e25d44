package top.kx.kxss.base.vo.result;

import cn.hutool.core.map.MapUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.annotation.echo.Echo;
import top.kx.basic.base.entity.Entity;
import top.kx.basic.interfaces.echo.EchoVO;
import top.kx.kxss.model.constant.EchoApi;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 表单查询方法返回值VO
 * 防伪信息规格属性
 * </p>
 *
 * <AUTHOR>
 * @date 2025-02-26 16:17:06
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@ApiModel(value = "BaseSecurityCodeAttributeResultVO", description = "防伪信息规格属性")
public class BaseSecurityCodeAttributeResultVO extends Entity<Long> implements Serializable, EchoVO {

    private static final long serialVersionUID = 1L;

    private Map<String, Object> echoMap = MapUtil.newHashMap();

    @ApiModelProperty(value = "主键")
    private Long id;
    /**
    * 防伪码
    */
    @ApiModelProperty(value = "防伪码")
    private String securityCode;

    @Echo(api = EchoApi.PRODUCT_ID_CLASS)
    @ApiModelProperty(value = "商品ID")
    private Long productId;

    /**
    * 防伪ID
    */
    @ApiModelProperty(value = "防伪ID")
    private Long securityCodeId;

    /**
    * 创建人组织
    */
    @ApiModelProperty(value = "创建人组织")
    private Long createdOrgId;

    @ApiModelProperty(value = "创建人")
    @Echo(api = EchoApi.USER_ID_CLASS)
    private Long createdBy;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdTime;

    @ApiModelProperty(value = "更新人")
    @Echo(api = EchoApi.USER_ID_CLASS)
    private Long updatedBy;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedTime;

    @ApiModelProperty(value = "属性列表")
    private List<BaseSecurityCodeAttributeDetailsResultVO> attributeDetailsResultVOList;


}
