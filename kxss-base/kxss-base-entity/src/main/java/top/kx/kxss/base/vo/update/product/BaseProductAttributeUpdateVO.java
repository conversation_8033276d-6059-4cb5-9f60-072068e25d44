package top.kx.kxss.base.vo.update.product;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.SuperEntity;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <p>
 * 表单修改方法VO
 * 商品基础属性关联
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-16 18:44:16
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode
@Builder
@ApiModel(value = "BaseProductAttributeUpdateVO", description = "商品基础属性关联")
public class BaseProductAttributeUpdateVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotNull(message = "请填写主键", groups = SuperEntity.Update.class)
    private Long id;

    /**
     * 商品ID
     */
    @ApiModelProperty(value = "商品ID")
    @NotNull(message = "请填写商品ID")
    private Long productId;
    /**
     * 属性ID
     */
    @ApiModelProperty(value = "属性ID")
    @NotNull(message = "请填写属性ID")
    private Long attributeId;
    /**
     * 创建人组织
     */
    @ApiModelProperty(value = "创建人组织")
    private Long createdOrgId;


}
