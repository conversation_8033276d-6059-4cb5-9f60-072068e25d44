package top.kx.kxss.base.vo.update.enterprise;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.SuperEntity;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <p>
 * 表单修改方法VO
 * 企业
 * </p>
 *
 * <AUTHOR>
 * @date 2025-04-25 17:45:11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode
@Builder
@ApiModel(value = "BaseEnterpriseUpdateVO", description = "企业")
public class BaseEnterpriseUpdateVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotNull(message = "请填写主键", groups = SuperEntity.Update.class)
    private Long id;

    /**
     * 企业全称
     */
    @ApiModelProperty(value = "企业全称")
    @NotEmpty(message = "请填写企业全称")
    @Size(max = 100, message = "企业全称长度不能超过{max}")
    private String name;
    /**
     * 营业执照号码
     */
    @ApiModelProperty(value = "营业执照号码")
    @NotEmpty(message = "请填写营业执照号码")
    @Size(max = 50, message = "营业执照号码长度不能超过{max}")
    private String businessLicense;
    /**
     * 状态;[0-禁用 1-启用]
     */
    @ApiModelProperty(value = "状态")
    private Boolean state;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Size(max = 255, message = "备注长度不能超过{max}")
    private String remarks;
    /**
     * 创建人组织
     */
    @ApiModelProperty(value = "创建人组织")
    private Long createdOrgId;


}
