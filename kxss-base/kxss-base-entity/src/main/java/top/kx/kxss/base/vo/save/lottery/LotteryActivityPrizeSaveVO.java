package top.kx.kxss.base.vo.save.lottery;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 表单保存方法VO
 * 活动奖品配置表
 * </p>
 *
 * <AUTHOR>
 * @date 2025-09-15 17:23:11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode
@Builder
@ApiModel(value = "LotteryActivityPrizeSaveVO", description = "活动奖品配置表")
public class LotteryActivityPrizeSaveVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 活动ID
     */
    @ApiModelProperty(value = "活动ID", hidden = true)
    //@NotNull(message = "请填写活动ID")
    private Long activityId;
    /**
     * 奖品库ID
     */
    @ApiModelProperty(value = "奖品库ID")
    @NotNull(message = "请填写奖品库ID")
    private Long prizeId;
    /**
     * 中奖概率(%)
     */
    @ApiModelProperty(value = "中奖概率(%)")
    @NotNull(message = "请填写中奖概率(%)")
    private BigDecimal probability;
    /**
     * 活动内奖品数量限制 (0=不限)
     */
    @ApiModelProperty(value = "活动内奖品数量限制 (0=不限)")
    @NotNull(message = "请填写活动内奖品数量限制 (0=不限)")
    private Integer num;

    //@ApiModelProperty(value = "已领取数量", hidden = true)
    //@NotNull(message = "请填写已领取数量")
    //private Integer receivedNum;
    /**
     * 排序 (奖品位置)
     */
    @ApiModelProperty(value = "排序 (奖品位置)")
    @NotNull(message = "请填写排序 (奖品位置)")
    private Integer sortValue;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Size(max = 200, message = "备注长度不能超过{max}")
    private String remarks;



}
