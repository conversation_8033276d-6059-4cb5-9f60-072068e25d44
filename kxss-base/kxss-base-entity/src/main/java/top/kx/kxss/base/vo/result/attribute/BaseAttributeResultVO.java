package top.kx.kxss.base.vo.result.attribute;

import cn.hutool.core.map.MapUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.annotation.echo.Echo;
import top.kx.basic.base.entity.Entity;
import top.kx.basic.interfaces.echo.EchoVO;
import top.kx.kxss.model.constant.EchoApi;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 表单查询方法返回值VO
 * 商品基础属性
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-16 18:44:16
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@ApiModel(value = "BaseAttributeResultVO", description = "商品基础属性")
public class BaseAttributeResultVO extends Entity<Long> implements Serializable, EchoVO {

    private static final long serialVersionUID = 1L;

    private Map<String, Object> echoMap = MapUtil.newHashMap();

    @ApiModelProperty(value = "主键")
    private Long id;

    /**
    * 属性名称
    */
    @ApiModelProperty(value = "属性名称")
    private String name;
    /**
    * 单位
    */
    @ApiModelProperty(value = "单位")
    private String unit;
    /**
    * 状态
    */
    @ApiModelProperty(value = "状态")
    private Boolean state;
    /**
    * 排序数量
    */
    @ApiModelProperty(value = "排序数量")
    private Integer sortValue;
    /**
    * 创建人组织
    */
    @ApiModelProperty(value = "创建人组织")
    private Long createdOrgId;

    @ApiModelProperty(value = "已经选中属性")
    private String value;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @Echo(api = EchoApi.USER_ID_CLASS)
    private Long createdBy;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    @Echo(api = EchoApi.USER_ID_CLASS)
    private Long updatedBy;

    @ApiModelProperty(value = "基础属性值")
    private List<String> valueList;



}
