package top.kx.kxss.base.entity.lottery;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.Entity;

import static com.baomidou.mybatisplus.annotation.SqlCondition.EQUAL;
import static top.kx.kxss.model.constant.Condition.LIKE;


/**
 * <p>
 * 实体类
 * 奖品领取表(含收货地址快照+物流信息)
 * </p>
 *
 * <AUTHOR>
 * @date 2025-09-15 17:23:11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@TableName("lottery_prize_claim")
public class LotteryPrizeClaim extends Entity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 抽奖记录ID
     */
    @TableField(value = "record_id", condition = EQUAL)
    private Long recordId;
    /**
     * 活动ID
     */
    @TableField(value = "activity_id", condition = EQUAL)
    private Long activityId;
    /**
     * 用户ID
     */
    @TableField(value = "user_id", condition = EQUAL)
    private Long userId;

    /**
     * 收货地址快照(JSON，包含姓名/电话/省市区/详细地址)
     */
    @TableField(value = "address_snapshot", condition = LIKE)
    private String addressSnapshot;

    /**
     * 收件人姓名
     */
    @TableField(value = "receiver_name", condition = LIKE)
    private String receiverName;

    /**
     * 收件人手机号
     */
    @TableField(value = "phone", condition = LIKE)
    private String phone;

    /**
     * 省份
     */
    @TableField(value = "province", condition = LIKE)
    private String province;

    /**
     * 城市
     */
    @TableField(value = "city", condition = LIKE)
    private String city;

    /**
     * 区/县
     */
    @TableField(value = "district", condition = LIKE)
    private String district;

    /**
     * 详细地址
     */
    @TableField(value = "detail_address", condition = LIKE)
    private String detailAddress;

    /**
     * 领取状态: 0=未领取,1=已确认地址,2=已发货,3=已完成
     */
    @TableField(value = "claim_status", condition = EQUAL)
    private String claimStatus;

    /**
     * 快递公司
     */
    @TableField(value = "express_company", condition = LIKE)
    private String expressCompany;
    /**
     * 快递单号
     */
    @TableField(value = "express_no", condition = LIKE)
    private String expressNo;
    /**
     * 额外信息(扩展字段，如备注/发货人)
     */
    @TableField(value = "claim_info", condition = LIKE)
    private String claimInfo;
    /**
     * 备注
     */
    @TableField(value = "remarks", condition = LIKE)
    private String remarks;
}
