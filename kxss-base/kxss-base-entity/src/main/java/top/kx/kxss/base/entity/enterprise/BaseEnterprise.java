package top.kx.kxss.base.entity.enterprise;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.Entity;

import static com.baomidou.mybatisplus.annotation.SqlCondition.EQUAL;
import static top.kx.kxss.model.constant.Condition.LIKE;


/**
 * <p>
 * 实体类
 * 企业
 * </p>
 *
 * <AUTHOR>
 * @date 2025-04-25 17:45:11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@TableName("base_enterprise")
public class BaseEnterprise extends Entity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 企业全称
     */
    @TableField(value = "name", condition = LIKE)
    private String name;
    /**
     * 营业执照号码
     */
    @TableField(value = "business_license", condition = LIKE)
    private String businessLicense;
    /**
     * 状态;[0-禁用 1-启用]
     */
    @TableField(value = "state", condition = EQUAL)
    private Boolean state;
    /**
     * 备注
     */
    @TableField(value = "remarks", condition = LIKE)
    private String remarks;
    /**
     * 创建人组织
     */
    @TableField(value = "created_org_id", condition = EQUAL)
    private Long createdOrgId;



}
