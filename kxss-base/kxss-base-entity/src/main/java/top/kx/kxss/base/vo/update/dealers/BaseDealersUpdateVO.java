package top.kx.kxss.base.vo.update.dealers;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.annotation.constraints.NotEmptyPattern;
import top.kx.basic.base.entity.SuperEntity;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

import static top.kx.basic.utils.ValidatorUtil.REGEX_MOBILE;

/**
 * <p>
 * 表单修改方法VO
 * 俱乐部信息
 * </p>
 *
 * <AUTHOR>
 * @date 2024-12-20 10:47:35
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode
@Builder
@ApiModel(value = "BaseDealersUpdateVO", description = "俱乐部信息")
public class BaseDealersUpdateVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotNull(message = "请填写主键", groups = SuperEntity.Update.class)
    private Long id;

    /**
     * 类型 1 新球房 2 老球房升级
     */
    @ApiModelProperty(value = "类型 1 新球房 2 老球房升级")
    //@NotEmpty(message = "请填写类型 1 新球房 2 老球房升级")
    @Size(max = 1, message = "类型 1 新球房 2 老球房升级长度不能超过{max}")
    private String type;
    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    @NotEmpty(message = "请填写姓名")
    @Size(max = 50, message = "姓名长度不能超过{max}")
    private String name;
    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    @NotEmpty(message = "请填写手机号")
    @Size(max = 20, message = "手机号长度不能超过{max}")
    @NotEmptyPattern(regexp = REGEX_MOBILE, message = "请输入正确的手机号")
    private String mobile;
    /**
     * 球房名称
     */
    @ApiModelProperty(value = "球房名称")
    @NotEmpty(message = "请填写球房名称")
    @Size(max = 100, message = "球房名称长度不能超过{max}")
    private String ballRoomName;
    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    //@NotEmpty(message = "请填写性别")
    @Size(max = 5, message = "性别长度不能超过{max}")
    private String sex;
    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    //@NotNull(message = "请填写用户ID")
    private Long userId;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Size(max = 255, message = "备注长度不能超过{max}")
    private String remarks;
    /**
     * 商品注册数
     */
    @ApiModelProperty(value = "商品注册数")
    //@NotNull(message = "请填写商品注册数")
    private Integer productNum;
    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址")
    @Size(max = 200, message = "详细地址长度不能超过{max}")
    private String address;
    /**
     * 区域-数组
     */
    @ApiModelProperty(value = "区域-数组")
    @Size(max = 255, message = "区域-数组长度不能超过{max}")
    private String regions;
    /**
     * 头像ID
     */
    @ApiModelProperty(value = "头像ID")
    private Long avatarId;


}
