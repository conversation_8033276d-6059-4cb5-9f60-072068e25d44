package top.kx.kxss.base.entity.supplier;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import static com.baomidou.mybatisplus.annotation.SqlCondition.EQUAL;
import static top.kx.kxss.model.constant.Condition.LIKE;
import top.kx.basic.base.entity.Entity;


/**
 * <p>
 * 实体类
 * 供应商信息信息
 * </p>
 *
 * <AUTHOR>
 * @date 2025-02-28 16:36:35
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@TableName("base_supplier")
public class BaseSupplier extends Entity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 姓名
     */
    @TableField(value = "name", condition = LIKE)
    private String name;
    /**
     * 手机号
     */
    @TableField(value = "mobile", condition = LIKE)
    private String mobile;
    /**
     * 公司名称
     */
    @TableField(value = "company_name", condition = LIKE)
    private String companyName;
    /**
     * 性别
     */
    @TableField(value = "sex", condition = LIKE)
    private String sex;
    /**
     * 头像ID
     */
    @TableField(value = "avatar_id", condition = EQUAL)
    private Long avatarId;
    /**
     * 备注
     */
    @TableField(value = "remarks", condition = LIKE)
    private String remarks;
    /**
     * 详细地址
     */
    @TableField(value = "address", condition = LIKE)
    private String address;
    /**
     * 区域-数组
     */
    @TableField(value = "regions", condition = LIKE)
    private String regions;
    /**
     * 创建人组织
     */
    @TableField(value = "created_org_id", condition = EQUAL)
    private Long createdOrgId;



}
