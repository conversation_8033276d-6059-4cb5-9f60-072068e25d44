package top.kx.kxss.base.entity.customers;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.Entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static com.baomidou.mybatisplus.annotation.SqlCondition.EQUAL;
import static top.kx.kxss.model.constant.Condition.LIKE;


/**
 * <p>
 * 实体类
 * 客户咨询信息
 * </p>
 *
 * <AUTHOR>
 * @date 2024-12-19 10:30:39
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@TableName("base_customers")
public class BaseCustomers extends Entity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 名称
     */
    @TableField(value = "name", condition = LIKE)
    private String name;
    /**
     * 手机号
     */
    @TableField(value = "phone", condition = LIKE)
    private String phone;
    /**
     * 城市
     */
    @TableField(value = "city", condition = LIKE)
    private String city;
    /**
     * 球房类型 1:新球房 2:老球房升级
     */
    @TableField(value = "type_", condition = LIKE)
    private String type;
    /**
     * 球房面积
     * 球房面积
     */
    @TableField(value = "area", condition = EQUAL, fill = FieldFill.UPDATE)
    private BigDecimal area;

    /**
     * 领取人
     */
    @TableField(value = "recipients", condition = LIKE, fill = FieldFill.UPDATE)
    private Long recipients;

    /**
     * 领取时间
     */
    @TableField(value = "collection_time", condition = LIKE, fill = FieldFill.UPDATE)
    private LocalDateTime collectionTime;

    /**
     * 备注
     */
    @TableField(value = "remarks")
    private String remarks;

}
