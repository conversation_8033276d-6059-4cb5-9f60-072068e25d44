package top.kx.kxss.base.vo.result.print;

import cn.hutool.core.map.MapUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.annotation.echo.Echo;
import top.kx.basic.base.entity.Entity;
import top.kx.basic.interfaces.echo.EchoVO;
import top.kx.kxss.model.constant.EchoApi;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 表单查询方法返回值VO
 * 打印模板
 * </p>
 *
 * <AUTHOR>
 * @date 2025-08-27 10:48:00
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@ApiModel(value = "BasePrintTemplateResultVO", description = "打印模板")
public class BasePrintTemplateResultVO extends Entity<Long> implements Serializable, EchoVO {

    private static final long serialVersionUID = 1L;

    private Map<String, Object> echoMap = MapUtil.newHashMap();

    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "名称")
    private String name;

    /**
    * 商品ID
    */
    @Echo(api = EchoApi.PRODUCT_ID_CLASS)
    @ApiModelProperty(value = "商品ID")
    private Long productId;
    /**
    * 宽
    */
    @ApiModelProperty(value = "宽")
    private Integer labelSizeWidth;
    /**
    * 高
    */
    @ApiModelProperty(value = "高")
    private Integer labelSizeHeight;
    /**
    * 属性
    */
    @ApiModelProperty(value = "属性")
    private List<String> usedAttrIds;
    /**
    * 打印内容
    */
    @ApiModelProperty(value = "打印内容")
    private String printContent;
    /**
    * 备注
    */
    @ApiModelProperty(value = "备注")
    private String remarks;
    /**
    * 创建人组织
    */
    @ApiModelProperty(value = "创建人组织")
    private Long createdOrgId;



}
