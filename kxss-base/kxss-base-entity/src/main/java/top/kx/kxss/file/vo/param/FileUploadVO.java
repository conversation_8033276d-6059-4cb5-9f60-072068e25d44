package top.kx.kxss.file.vo.param;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import top.kx.kxss.file.enumeration.FileStorageType;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 附件上传
 *
 * <AUTHOR>
 * @date 2019-05-12 18:49
 */
@Data
@ApiModel(value = "FileUploadVO", description = "附件上传")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FileUploadVO implements Serializable {

    @ApiModelProperty(value = "业务类型")
    @NotBlank(message = "请填写业务类型")
    private String bizType;

    @ApiModelProperty(value = "桶")
    private String bucket;

    @ApiModelProperty(value = "存储类型")
    private FileStorageType storageType;
}
