package top.kx.kxss.base.vo.save;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 表单保存方法VO
 * 防伪信息
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-25 13:40:18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode
@Builder
@ApiModel(value = "BaseSecurityCodeSaveVO", description = "防伪信息")
public class BaseSecurityCodeSaveVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 序列号
     */
    @ApiModelProperty(value = "序列号")
    @NotEmpty(message = "请填写序列号")
    @Size(max = 200, message = "序列号长度不能超过{max}")
    private String code;
    /**
     * 防伪码
     */
    @ApiModelProperty(value = "防伪码")
    @Size(max = 255, message = "防伪码长度不能超过{max}")
    @NotEmpty(message = "请填写防伪码")
    private String securityCode;
    /**
     * 防伪链接
     */
    @ApiModelProperty(value = "防伪链接")
    @Size(max = 255, message = "防伪链接长度不能超过{max}")
    private String url;
    /**
     * 绑定日期
     */
    @ApiModelProperty(value = "绑定日期")
    private LocalDateTime billDate;
    /**
     * 绑定用户
     */
    @ApiModelProperty(value = "绑定用户")
    private Long bindUser;
    /**
     * 注册时间
     */
    @ApiModelProperty(value = "注册时间")
    private LocalDateTime registerTime;
    /**
     * 报废时间
     */
    @ApiModelProperty(value = "报废时间", hidden = true)
    private LocalDateTime scrapTime;
    /**
     * 报废人员
     */
    @ApiModelProperty(value = "报废人员", hidden = true)
    private Long scrapUser;
    /**
     * 商品ID
     */
    @ApiModelProperty(value = "商品ID")
    private Long productId;
    /**
     * 仓库(字典)
     */
    @ApiModelProperty(value = "仓库(字典)")
    @Size(max = 10, message = "仓库(字典)长度不能超过{max}")
    private String wareHouse;
    /**
     * 供应商(字典)
     */
    @ApiModelProperty(value = "供应商(字典)")
    @Size(max = 20, message = "供应商(字典)长度不能超过{max}")
    private String supplier;
    /**
     * 创建人组织
     */
    @ApiModelProperty(value = "创建人组织", hidden = true)
    private Long createdOrgId;
    /**
     * 会员ID
     */
    @ApiModelProperty(value = "会员ID")
    private Long memberId;

    /**
     * 大标
     */
    @ApiModelProperty(value = "大标")
    private String bigCode;

    /**
     * 查询次数
     */
    @ApiModelProperty(value = "查询次数", hidden = true)
    private Integer selectNum;
    /**
     * 状态 1 已导入 2 已绑定 3 已注册 4 已报废
     */
    @ApiModelProperty(value = "状态 1 已导入 2 已绑定 3 已注册 4 已报废")
    @NotEmpty(message = "请选择状态")
    private String status;

    /**
     * 报废原因
     */
    @ApiModelProperty(value = "报废原因")
    private String scrapReason;

    /**
     * 要货时间
     */
    @ApiModelProperty(value = "拿货时间")
    private LocalDateTime purchaseControlTime;

    /**
     * 要货经销商
     */
    @ApiModelProperty(value = "要货经销商")
    private Long distributorId;

    /**
     * 要货操作人
     */
    @ApiModelProperty(value = "要货操作人", hidden = true)
    private Long purchaseControlBy;

    /**
     * 要货录入方式
     */
    @ApiModelProperty(value = "要货录入方式", hidden = true)
    private String purchaseEnterType;

    /**
     * 批次ID
     */
    @ApiModelProperty(value = "批次ID")
    private Long batchId;

    /**
     * 批次编号
     */
    @ApiModelProperty(value = "批次编号")
    private String batchCode;

    @ApiModelProperty(value = "防伪码规格属性")
    private List<BaseSecurityCodeAttributeSaveVO> attributeSaveVOList;


}
