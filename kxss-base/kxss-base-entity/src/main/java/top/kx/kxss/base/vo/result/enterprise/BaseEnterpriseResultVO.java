package top.kx.kxss.base.vo.result.enterprise;

import cn.hutool.core.map.MapUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.Entity;
import top.kx.basic.interfaces.echo.EchoVO;

import java.io.Serializable;
import java.util.Map;

/**
 * <p>
 * 表单查询方法返回值VO
 * 企业
 * </p>
 *
 * <AUTHOR>
 * @date 2025-04-25 17:45:11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@ApiModel(value = "BaseEnterpriseResultVO", description = "企业")
public class BaseEnterpriseResultVO extends Entity<Long> implements Serializable, EchoVO {

    private static final long serialVersionUID = 1L;

    private Map<String, Object> echoMap = MapUtil.newHashMap();

    @ApiModelProperty(value = "主键")
    private Long id;

    /**
    * 企业全称
    */
    @ApiModelProperty(value = "企业全称")
    private String name;
    /**
    * 营业执照号码
    */
    @ApiModelProperty(value = "营业执照号码")
    private String businessLicense;
    /**
    * 状态;[0-禁用 1-启用]
    */
    @ApiModelProperty(value = "状态")
    private Boolean state;
    /**
    * 备注
    */
    @ApiModelProperty(value = "备注")
    private String remarks;
    /**
    * 创建人组织
    */
    @ApiModelProperty(value = "创建人组织")
    private Long createdOrgId;



}
