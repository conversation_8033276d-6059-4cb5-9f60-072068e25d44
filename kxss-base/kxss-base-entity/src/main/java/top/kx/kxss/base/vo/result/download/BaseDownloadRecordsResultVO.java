package top.kx.kxss.base.vo.result.download;

import cn.hutool.core.map.MapUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.annotation.echo.Echo;
import top.kx.basic.base.entity.Entity;
import top.kx.basic.interfaces.echo.EchoVO;
import top.kx.kxss.model.constant.EchoApi;

import java.io.Serializable;
import java.util.Map;

/**
 * <p>
 * 表单查询方法返回值VO
 * 下载记录
 * </p>
 *
 * <AUTHOR>
 * @date 2025-04-25 17:45:12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@ApiModel(value = "BaseDownloadRecordsResultVO", description = "下载记录")
public class BaseDownloadRecordsResultVO extends Entity<Long> implements Serializable, EchoVO {

    private static final long serialVersionUID = 1L;

    private Map<String, Object> echoMap = MapUtil.newHashMap();

    @ApiModelProperty(value = "主键")
    private Long id;

    /**
    * 资源ID
    */
    @ApiModelProperty(value = "资源ID")
    private Long sourceId;
    /**
    * 资源类型:BATCH-防伪码批次
    */
    @ApiModelProperty(value = "资源类型:BATCH-防伪码批次")
    private String sourceType;
    /**
    * 员工ID
    */
    @ApiModelProperty(value = "员工ID")
    @Echo(api = EchoApi.EMP_ID_CLASS)
    private Long employeeId;
    /**
    * 下载内容链接
    */
    @ApiModelProperty(value = "下载内容链接")
    private Long url;

    @ApiModelProperty(value = "下载条数")
    private Integer num;

    /**
    * 创建人组织
    */
    @ApiModelProperty(value = "创建人组织")
    private Long createdOrgId;


}
