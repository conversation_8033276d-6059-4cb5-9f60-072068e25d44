package top.kx.kxss.base.vo.result.security;

import cn.hutool.core.map.MapUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.annotation.echo.Echo;
import top.kx.basic.base.entity.Entity;
import top.kx.basic.interfaces.echo.EchoVO;
import top.kx.kxss.model.constant.EchoApi;
import top.kx.kxss.model.constant.EchoDictType;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Map;

/**
 * <p>
 * 表单查询方法返回值VO
 * 防伪码批次
 * </p>
 *
 * <AUTHOR>
 * @date 2025-04-25 17:45:12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@ApiModel(value = "BaseSecurityCodeBatchResultVO", description = "防伪码批次")
public class BaseSecurityCodeBatchResultVO extends Entity<Long> implements Serializable, EchoVO {

    private static final long serialVersionUID = 1L;

    private Map<String, Object> echoMap = MapUtil.newHashMap();

    @ApiModelProperty(value = "主键")
    private Long id;

    /**
    * 批次号
    */
    @ApiModelProperty(value = "批次号")
    private String batchCode;
    /**
    * 生成数量
    */
    @ApiModelProperty(value = "生成数量")
    private Long num;
    /**
    * 商品ID
    */
    @ApiModelProperty(value = "商品ID")
    @Echo(api = EchoApi.PRODUCT_ID_CLASS)
    private Long productId;
    /**
    * 大商品ID
    */
    @ApiModelProperty(value = "大商品ID")
    @Echo(api = EchoApi.PRODUCT_ID_CLASS)
    private Long bigProductId;

    /**
    * 跳转类型:1-小程序，2-H5
    */
    @ApiModelProperty(value = "跳转类型:1-小程序，2-H5")
    private String linkType;
    /**
    * 大跳转类型:1-小程序，2-H5
    */
    @ApiModelProperty(value = "大跳转类型:1-小程序，2-H5")
    private String bigLinkType;
    /**
    * 链接地址
    */
    @ApiModelProperty(value = "链接地址")
    @Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Base.SECURITY_CODE_LINK_TYPE)
    private String linkUrl;
    @ApiModelProperty(value = "大链接地址")
    @Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Base.SECURITY_CODE_LINK_TYPE)
    private String bigLinkUrl;
    /**
    * 是否子母码
    */
    @ApiModelProperty(value = "是否子母码")
    private Boolean isSeries;
    /**
    * 子母码比例
    */
    @ApiModelProperty(value = "子母码比例")
    private Integer seriesRatio;
    /**
    * 状态;[1-生成中。2-已生成]
    */
    @ApiModelProperty(value = "状态")
    @Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Base.SECURITY_CODE_BATCH_STATUS)
    private String state;

    @ApiModelProperty(value = "状态描述")
    private String stateDesc;
    /**
    * 导入状态;[1-未导入,2-导入中,3-已经导入]
    */
    @ApiModelProperty(value = "导入状态")
    @Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Base.SECURITY_CODE_BATCH_IMPORT_STATUS)
    private String importState;

    @ApiModelProperty(value = "导入状态描述")
    private String importStateDesc;
    /**
    * 企业ID
    */
    @ApiModelProperty(value = "企业ID")
    @Echo(api = EchoApi.ENTERPRISE_ID_CLASS)
    private Long enterpriseId;
    /**
    * 物流方式
    */
    @ApiModelProperty(value = "物流方式")
    private String logisticsType;
    /**
    * 物流日期
    */
    @ApiModelProperty(value = "物流日期")
    private LocalDate logisticsDate;
    /**
    * 创建员工
    */
    @ApiModelProperty(value = "创建员工")
    @Echo(api = EchoApi.EMP_ID_CLASS)
    private Long createdEmp;
    /**
    * 备注
    */
    @ApiModelProperty(value = "备注")
    private String remarks;
    /**
    * 创建人组织
    */
    @ApiModelProperty(value = "创建人组织")
    private Long createdOrgId;



}
