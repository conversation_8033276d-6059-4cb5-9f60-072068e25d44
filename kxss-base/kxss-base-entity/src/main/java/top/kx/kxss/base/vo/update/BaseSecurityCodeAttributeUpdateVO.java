package top.kx.kxss.base.vo.update;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.SuperEntity;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <p>
 * 表单修改方法VO
 * 防伪信息规格属性
 * </p>
 *
 * <AUTHOR>
 * @date 2025-02-26 16:17:06
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode
@Builder
@ApiModel(value = "BaseSecurityCodeAttributeUpdateVO", description = "防伪信息规格属性")
public class BaseSecurityCodeAttributeUpdateVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotNull(message = "请填写主键", groups = SuperEntity.Update.class)
    private Long id;
    /**
     * 防伪码
     */
    @ApiModelProperty(value = "防伪码")
    @Size(max = 255, message = "防伪码长度不能超过{max}")
    private String securityCode;

    @ApiModelProperty(value = "商品ID")
    private Long productId;

    /**
     * 防伪ID
     */
    @ApiModelProperty(value = "防伪ID")
    private Long securityCodeId;
    /**
     * 创建人组织
     */
    @ApiModelProperty(value = "创建人组织")
    private Long createdOrgId;

}
