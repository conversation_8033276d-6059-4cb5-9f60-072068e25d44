package top.kx.kxss.base.vo.save.print;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <p>
 * 表单保存方法VO
 * 打印模板
 * </p>
 *
 * <AUTHOR>
 * @date 2025-08-27 10:48:00
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode
@Builder
@ApiModel(value = "BasePrintTemplateSaveVO", description = "打印模板")
public class BasePrintTemplateSaveVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商品ID
     */
    @ApiModelProperty(value = "商品ID")
    @NotNull(message = "请填写商品ID")
    private Long productId;
    /**
     * 标签尺寸(毫米)
     */
    @ApiModelProperty(value = "标签尺寸(毫米)")
    @NotEmpty(message = "请填写标签尺寸(毫米)")
    @Size(max = 1073741824, message = "标签尺寸(毫米)长度不能超过{max}")
    private String labelSizeMm;
    /**
     * 属性
     */
    @ApiModelProperty(value = "属性")
    @NotEmpty(message = "请填写属性")
    @Size(max = 1073741824, message = "属性长度不能超过{max}")
    private String usedAttrIds;
    /**
     * 打印内容
     */
    @ApiModelProperty(value = "打印内容")
    @Size(max = 1073741824, message = "打印内容长度不能超过{max}")
    private String printContent;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Size(max = 200, message = "备注长度不能超过{max}")
    private String remarks;
    /**
     * 创建人组织
     */
    @ApiModelProperty(value = "创建人组织")
    private Long createdOrgId;



}
