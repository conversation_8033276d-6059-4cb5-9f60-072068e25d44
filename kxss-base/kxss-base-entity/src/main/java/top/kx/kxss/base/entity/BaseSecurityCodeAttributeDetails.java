package top.kx.kxss.base.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.Entity;

import static com.baomidou.mybatisplus.annotation.SqlCondition.EQUAL;
import static top.kx.kxss.model.constant.Condition.LIKE;


/**
 * <p>
 * 实体类
 * 防伪信息规格属性
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-17 10:23:59
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@TableName("base_security_code_attribute_details")
public class BaseSecurityCodeAttributeDetails extends Entity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 防伪属性ID
     */
    @TableField(value = "code_attribute_id", condition = EQUAL)
    private Long codeAttributeId;
    /**
     * 基础属性ID
     */
    @TableField(value = "attribute_id", condition = EQUAL)
    private Long attributeId;
    /**
     * 规格属性value
     */
    @TableField(value = "attribute_value", condition = LIKE)
    private String attributeValue;
    /**
     * 创建人组织
     */
    @TableField(value = "created_org_id", condition = EQUAL)
    private Long createdOrgId;


}
