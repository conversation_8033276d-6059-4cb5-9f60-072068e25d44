package top.kx.kxss.base.vo.update.lottery;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.SuperEntity;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <p>
 * 表单修改方法VO
 * 奖品领取表(含收货地址快照+物流信息)
 * </p>
 *
 * <AUTHOR>
 * @date 2025-09-15 17:23:11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode
@Builder
@ApiModel(value = "LotteryPrizeClaimUpdateVO", description = "奖品领取表(含收货地址快照+物流信息)")
public class LotteryPrizeClaimUpdateVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "领取ID")
    @NotNull(message = "请填写领取ID", groups = SuperEntity.Update.class)
    private Long id;

    /**
     * 抽奖记录ID
     */
    @ApiModelProperty(value = "抽奖记录ID")
    @NotNull(message = "请填写抽奖记录ID")
    private Long recordId;
    /**
     * 活动ID
     */
    @ApiModelProperty(value = "活动ID")
    @NotNull(message = "请填写活动ID")
    private Long activityId;
    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    @NotNull(message = "请填写用户ID")
    private Long userId;
    /**
     * 收件人姓名
     */
    @ApiModelProperty(value = "收件人姓名")
    @Size(max = 50, message = "收件人姓名长度不能超过{max}")
    private String receiverName;

    /**
     * 收件人手机号
     */
    @ApiModelProperty(value = "收件人手机号")
    @Size(max = 20, message = "收件人手机号长度不能超过{max}")
    private String phone;

    /**
     * 省份
     */
    @ApiModelProperty(value = "省份")
    @Size(max = 50, message = "省份长度不能超过{max}")
    private String province;

    /**
     * 城市
     */
    @ApiModelProperty(value = "城市")
    @Size(max = 50, message = "城市长度不能超过{max}")
    private String city;

    /**
     * 区/县
     */
    @ApiModelProperty(value = "区/县")
    @Size(max = 50, message = "区/县长度不能超过{max}")
    private String district;

    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址")
    @Size(max = 255, message = "详细地址长度不能超过{max}")
    private String detailAddress;

    /**
     * 领取状态: 0=未领取,1=已确认地址,2=已发货,3=已完成
     */
    @ApiModelProperty(value = "领取状态: 0=未领取,1=已确认地址,2=已发货,3=已完成")
    //@NotNull(message = "请填写领取状态: 0=未领取,1=已确认地址,2=已发货,3=已完成")
    private String claimStatus;
    /**
     * 快递公司
     */
    @ApiModelProperty(value = "快递公司")
    @Size(max = 100, message = "快递公司长度不能超过{max}")
    private String expressCompany;
    /**
     * 快递单号
     */
    @ApiModelProperty(value = "快递单号")
    @Size(max = 100, message = "快递单号长度不能超过{max}")
    private String expressNo;
    /**
     * 额外信息(扩展字段，如备注/发货人)
     */
    @ApiModelProperty(value = "额外信息(扩展字段，如备注/发货人)")
    @Size(max = 1073741824, message = "额外信息(扩展字段，如备注/发货人)长度不能超过{max}")
    private String claimInfo;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Size(max = 200, message = "备注长度不能超过{max}")
    private String remarks;

}
