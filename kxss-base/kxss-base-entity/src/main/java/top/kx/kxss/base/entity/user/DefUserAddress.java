package top.kx.kxss.base.entity.user;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.Entity;

import static com.baomidou.mybatisplus.annotation.SqlCondition.EQUAL;
import static top.kx.kxss.model.constant.Condition.LIKE;


/**
 * <p>
 * 实体类
 * 用户地址库表
 * </p>
 *
 * <AUTHOR>
 * @date 2025-09-15 17:23:11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@TableName("def_user_address")
public class DefUserAddress extends Entity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @TableField(value = "user_id", condition = EQUAL)
    private Long userId;
    /**
     * 收件人姓名
     */
    @TableField(value = "receiver_name", condition = LIKE)
    private String receiverName;
    /**
     * 收件人手机号
     */
    @TableField(value = "phone", condition = LIKE)
    private String phone;
    /**
     * 省份
     */
    @TableField(value = "province", condition = LIKE)
    private String province;
    /**
     * 城市
     */
    @TableField(value = "city", condition = LIKE)
    private String city;
    /**
     * 区/县
     */
    @TableField(value = "district", condition = LIKE)
    private String district;
    /**
     * 详细地址
     */
    @TableField(value = "detail_address", condition = LIKE)
    private String detailAddress;
    /**
     * 是否默认地址: 0=否,1=是
     */
    @TableField(value = "is_default", condition = EQUAL)
    private Integer isDefault;
    /**
     * 备注
     */
    @TableField(value = "remarks", condition = LIKE)
    private String remarks;



}
