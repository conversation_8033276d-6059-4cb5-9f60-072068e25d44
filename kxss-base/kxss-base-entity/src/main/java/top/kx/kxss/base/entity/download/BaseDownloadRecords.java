package top.kx.kxss.base.entity.download;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.Entity;

import static com.baomidou.mybatisplus.annotation.SqlCondition.EQUAL;
import static top.kx.kxss.model.constant.Condition.LIKE;


/**
 * <p>
 * 实体类
 * 下载记录
 * </p>
 *
 * <AUTHOR>
 * @date 2025-04-25 17:45:12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@TableName("base_download_records")
public class BaseDownloadRecords extends Entity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 资源ID
     */
    @TableField(value = "source_id", condition = EQUAL)
    private Long sourceId;
    /**
     * 资源类型:BATCH-防伪码批次
     */
    @TableField(value = "source_type", condition = LIKE)
    private String sourceType;
    /**
     * 员工ID
     */
    @TableField(value = "employee_id", condition = EQUAL)
    private Long employeeId;
    /**
     * 下载内容链接
     */
    @TableField(value = "url", condition = EQUAL)
    private Long url;
    /**
     * 创建人组织
     */
    @TableField(value = "created_org_id", condition = EQUAL)
    private Long createdOrgId;



}
