package top.kx.kxss.base.vo.result.security;

import cn.hutool.core.map.MapUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.Entity;
import top.kx.basic.interfaces.echo.EchoVO;

import java.io.Serializable;
import java.util.Map;

/**
 * <p>
 * 表单查询方法返回值VO
 * 防伪码批次-详情
 * </p>
 *
 * <AUTHOR>
 * @date 2025-04-25 17:45:12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@ApiModel(value = "BaseSecurityCodeBatchDetailsResultVO", description = "防伪码批次-详情")
public class BaseSecurityCodeBatchDetailsResultVO extends Entity<Long> implements Serializable, EchoVO {

    private static final long serialVersionUID = 1L;

    private Map<String, Object> echoMap = MapUtil.newHashMap();

    @ApiModelProperty(value = "主键")
    private Long id;

    /**
    * 批次id
    */
    @ApiModelProperty(value = "批次id")
    private Long batchId;
    /**
    * 防伪码
    */
    @ApiModelProperty(value = "防伪码")
    private String securityCode;
    @ApiModelProperty(value = "防伪码")
    private String bigSecurityCode;
    /**
    * 防伪链接
    */
    @ApiModelProperty(value = "防伪链接")
    private String url;
    @ApiModelProperty(value = "大链接")
    private String bigUrl;


    @ApiModelProperty(value = "跳转类型:1-小程序，2-H5")
    private String linkType;

    @ApiModelProperty(value = "跳转类型:1-小程序，2-H5")
    private String bigLinkType;
    /**
    * 创建人组织
    */
    @ApiModelProperty(value = "创建人组织")
    private Long createdOrgId;



}
