package top.kx.kxss.base.vo.save.password;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <p>
 * 密码验证配置保存VO
 * </p>
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Builder
@ApiModel(value = "BasePasswordConfigSaveVO", description = "密码验证配置保存VO")
public class BasePasswordConfigSaveVO {

    /**
     * 业务模块代码
     */
    @ApiModelProperty(value = "业务模块代码", required = true)
    @NotBlank(message = "业务模块代码不能为空")
    private String businessCode;

    /**
     * 业务模块名称
     */
    @ApiModelProperty(value = "业务模块名称", required = true)
    //@NotBlank(message = "业务模块名称不能为空")
    private String businessName;

    /**
     * 验证密码（原始密码，会自动加密）
     */
    @ApiModelProperty(value = "验证密码", required = true)
    @NotBlank(message = "验证密码不能为空")
    private String password;

    /**
     * 是否启用密码验证
     */
    @ApiModelProperty(value = "是否启用密码验证", required = true)
    @NotNull(message = "是否启用密码验证不能为空")
    private Boolean isEnabled;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remarks;
}