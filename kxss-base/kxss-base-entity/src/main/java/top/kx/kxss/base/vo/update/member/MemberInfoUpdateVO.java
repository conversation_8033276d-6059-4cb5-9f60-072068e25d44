package top.kx.kxss.base.vo.update.member;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.annotation.constraints.NotEmptyPattern;
import top.kx.basic.base.entity.SuperEntity;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

import static top.kx.basic.utils.ValidatorUtil.REGEX_MOBILE;

/**
 * <p>
 * 表单修改方法VO
 * 会员信息
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-24 18:03:33
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode
@Builder
@ApiModel(value = "MemberInfoUpdateVO", description = "会员信息")
public class MemberInfoUpdateVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotNull(message = "请填写主键", groups = SuperEntity.Update.class)
    private Long id;

    /**
     * 会员编码
     */
    @ApiModelProperty(value = "会员编码")
    @NotEmpty(message = "请填写会员编码")
    @Size(max = 50, message = "会员编码长度不能超过{max}")
    private String code;
    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    @NotEmpty(message = "请填写姓名")
    @Size(max = 100, message = "姓名长度不能超过{max}")
    private String name;
    /**
     * 真实姓名
     */
    @ApiModelProperty(value = "真实姓名")
    private String fullName;
    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    @NotEmpty(message = "请填写手机号")
    @Size(max = 20, message = "手机号长度不能超过{max}")
    @NotEmptyPattern(regexp = REGEX_MOBILE, message = "请输入正确的手机号")
    private String mobile;
    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    @NotEmpty(message = "请填写性别")
    @Size(max = 1, message = "性别长度不能超过{max}")
    private String sex;
    /**
     * 生日
     */
    @ApiModelProperty(value = "生日")
    @Size(max = 20, message = "生日长度不能超过{max}")
    private String birth;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Size(max = 255, message = "备注长度不能超过{max}")
    private String remarks;
    /**
     * 创建人组织
     */
    @ApiModelProperty(value = "创建人组织", hidden = true)
    private Long createdOrgId;
    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID",hidden = true)
    private Long userId;
    /**
     * 区域-数组
     */
    @ApiModelProperty(value = "区域-数组")
    private List<String> regions;
    /**
     * 头像ID
     */
    @ApiModelProperty(value = "头像ID")
    private Long avatarId;
    /**
     * 商品注册数
     */
    @ApiModelProperty(value = "商品注册数")
    private Integer productNum;
}
