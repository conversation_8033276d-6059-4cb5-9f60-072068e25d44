package top.kx.kxss.base.vo.query.print;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;


/**
 * <p>
 * 表单查询条件VO
 * 打印模板
 * </p>
 *
 * <AUTHOR>
 * @date 2025-08-27 10:48:00
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode
@Builder
@ApiModel(value = "BasePrintTemplatePageQuery", description = "打印模板")
public class BasePrintTemplatePageQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    private Long id;

    /**
    * 商品ID
    */
    @ApiModelProperty(value = "商品ID")
    private Long productId;
    /**
    * 标签尺寸(毫米)
    */
    @ApiModelProperty(value = "标签尺寸(毫米)")
    private String labelSizeMm;
    /**
    * 属性
    */
    @ApiModelProperty(value = "属性")
    private String usedAttrIds;
    /**
    * 打印内容
    */
    @ApiModelProperty(value = "打印内容")
    private String printContent;
    /**
    * 备注
    */
    @ApiModelProperty(value = "备注")
    private String remarks;
    /**
    * 创建人组织
    */
    @ApiModelProperty(value = "创建人组织")
    private Long createdOrgId;



}
