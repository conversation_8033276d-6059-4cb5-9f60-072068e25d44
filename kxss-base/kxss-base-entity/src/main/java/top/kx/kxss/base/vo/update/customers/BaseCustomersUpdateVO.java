package top.kx.kxss.base.vo.update.customers;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.SuperEntity;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 表单修改方法VO
 * 客户咨询信息
 * </p>
 *
 * <AUTHOR>
 * @date 2024-12-19 10:30:39
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode
@Builder
@ApiModel(value = "BaseCustomersUpdateVO", description = "客户咨询信息")
public class BaseCustomersUpdateVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotNull(message = "请填写主键", groups = SuperEntity.Update.class)
    private Long id;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @NotEmpty(message = "请填写名称")
    @Size(max = 20, message = "名称长度不能超过{max}")
    private String name;
    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    @NotEmpty(message = "请填写手机号")
    @Size(max = 20, message = "手机号长度不能超过{max}")
    private String phone;
    /**
     * 城市
     */
    @ApiModelProperty(value = "城市")
    @NotEmpty(message = "请填写城市")
    @Size(max = 255, message = "城市长度不能超过{max}")
    private String city;
    /**
     * 球房类型 1:新球房 2:老球房升级
     */
    @ApiModelProperty(value = "球房类型 1:新球房 2:老球房升级")
    @NotEmpty(message = "请填写球房类型 1:新球房 2:老球房升级")
    @Size(max = 1, message = "球房类型 1:新球房 2:老球房升级长度不能超过{max}")
    private String type;
    /**
     * 球房面积
     * 球房面积
     */
    @ApiModelProperty(value = "球房面积 球房面积")
    private BigDecimal area;
    /**
     * 创建人组织
     */
    @ApiModelProperty(value = "创建人组织", hidden = true)
    private Long createdOrgId;
    /**
     * 领取人
     */
    @ApiModelProperty(value = "领取人")
    private Long recipients;
    /**
     * 领取时间
     */
    @ApiModelProperty(value = "领取时间")
    private LocalDateTime collectionTime;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Size(max = 200, message = "补充说明长度不能超过{value}")
    private String remarks;

}
