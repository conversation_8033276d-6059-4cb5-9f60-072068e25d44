package top.kx.kxss.base.entity.product;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.Entity;

import static com.baomidou.mybatisplus.annotation.SqlCondition.EQUAL;
import static top.kx.kxss.model.constant.Condition.LIKE;


/**
 * <p>
 * 实体类
 * 商品表
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-25 13:52:40
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@TableName("base_product")
public class BaseProduct extends Entity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 商品名称
     */
    @TableField(value = "name", condition = LIKE)
    private String name;
    /**
     * 商品分类
     */
    @TableField(value = "category", condition = LIKE)
    private String category;
    /**
     * 状态;[引用字典id]
     */
    @TableField(value = "state", condition = EQUAL)
    private Boolean state;

    /**
     * 排序
     */
    @TableField(value = "sort_value", condition = LIKE)
    private Integer sortValue;
    /**
     * 商品编码
     */
    @TableField(value = "code", condition = LIKE)
    private String code;
    /**
     * 计量单位[字典]
     */
    @TableField(value = "measuring_unit", condition = LIKE)
    private String measuringUnit;
    /**
     * 规格
     */
    @TableField(value = "spec", condition = LIKE)
    private String spec;
    /**
     * 详细信息
     */
    @TableField(value = "detail", condition = LIKE)
    private String detail;
    /**
     * 商品图片
     */
    @TableField(value = "product_image", condition = EQUAL, fill = FieldFill.UPDATE)
    private Long productImage;
    /**
     * 创建人组织
     */
    @TableField(value = "created_org_id", condition = EQUAL)
    private Long createdOrgId;
    /**
     * 备注
     */
    @TableField(value = "remarks", condition = LIKE)
    private String remarks;
    /**
     * 品牌
     */
    @TableField(value = "brand", condition = EQUAL)
    private String brand;
    /**
     * client_key 应用标识
     */
    @TableField(value = "client_id", condition = EQUAL)
    private Long clientId;


}
