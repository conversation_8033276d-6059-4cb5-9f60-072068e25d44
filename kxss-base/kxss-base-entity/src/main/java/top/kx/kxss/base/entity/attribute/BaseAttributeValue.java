package top.kx.kxss.base.entity.attribute;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.Entity;

import static com.baomidou.mybatisplus.annotation.SqlCondition.EQUAL;
import static top.kx.kxss.model.constant.Condition.LIKE;


/**
 * <p>
 * 实体类
 * 商品基础属性值
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-16 18:44:16
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@TableName("base_attribute_value")
public class BaseAttributeValue extends Entity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 属性ID
     */
    @TableField(value = "attribute_id", condition = EQUAL)
    private Long attributeId;
    /**
     * 属性值
     */
    @TableField(value = "value", condition = LIKE)
    private String value;
    /**
     * 创建人组织
     */
    @TableField(value = "created_org_id", condition = EQUAL)
    private Long createdOrgId;



}
