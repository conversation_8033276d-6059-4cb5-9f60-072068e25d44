package top.kx.kxss.base.entity.purchase;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.Entity;

import static com.baomidou.mybatisplus.annotation.SqlCondition.EQUAL;
import static top.kx.kxss.model.constant.Condition.LIKE;


/**
 * <p>
 * 实体类
 * 要货
 * </p>
 *
 * <AUTHOR>
 * @date 2025-03-10 14:57:47
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@TableName("base_purchase")
public class BasePurchase extends Entity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    @TableField(value = "code_", condition = LIKE)
    private String code;
    /**
     * 商品id
     */
    @TableField(value = "product_id", condition = EQUAL)
    private Long productId;
    /**
     * 经销商ID
     */
    @TableField(value = "distributor_id", condition = EQUAL)
    private Long distributorId;
    /**
     * 创建人
     */
    @TableField(value = "employee_id", condition = EQUAL)
    private Long employeeId;


    @TableField(value = "num", condition = EQUAL)
    private Integer num;
    /**
     * 备注
     */
    @TableField(value = "remarks", condition = LIKE)
    private String remarks;
    /**
     * 创建人组织
     */
    @TableField(value = "created_org_id", condition = EQUAL)
    private Long createdOrgId;



}
