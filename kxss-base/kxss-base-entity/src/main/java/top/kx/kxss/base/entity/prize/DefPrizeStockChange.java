package top.kx.kxss.base.entity.prize;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.Entity;

import static com.baomidou.mybatisplus.annotation.SqlCondition.EQUAL;
import static top.kx.kxss.model.constant.Condition.LIKE;


/**
 * <p>
 * 实体类
 * 奖品库表库存变动
 * </p>
 *
 * <AUTHOR>
 * @date 2025-09-16 10:01:28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@TableName("def_prize_stock_change")
public class DefPrizeStockChange extends Entity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 库存变动
     */
    @TableField(value = "prize_id", condition = EQUAL)
    private Long prizeId;
    /**
     * 库存变动类型：1-增加，2-减少
     */
    @TableField(value = "num_type", condition = EQUAL)
    private String numType;
    /**
     * 奖品库存
     */
    @TableField(value = "num", condition = EQUAL)
    private Integer num;
    /**
     * 剩余数量
     */
    @TableField(value = "residue_num", condition = EQUAL)
    private Integer residueNum;
    /**
     * 员工ID
     */
    @TableField(value = "employee_id", condition = EQUAL)
    private Long employeeId;

    /**
     * type
     */
    @TableField(value = "type", condition = EQUAL)
    private String type;

    /**
     * source_id
     */
    @TableField(value = "source_id", condition = EQUAL)
    private Long sourceId;
    /**
     * 备注
     */
    @TableField(value = "remarks", condition = LIKE)
    private String remarks;



}
