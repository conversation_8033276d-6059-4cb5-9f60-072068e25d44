package top.kx.kxss.base.vo.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <p>
 * 表单查询方法返回值VO
 * 防伪信息
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-25 13:40:18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@Builder
@ApiModel(value = "BaseMobileQuery", description = "标签统计查询信息")
public class BaseMobileQuery implements Serializable {


    @ApiModelProperty(value = "需要查看的手机号", required = true)
    @NotNull(message = "需要查看的手机号")
    private Long id;

    @ApiModelProperty(value = "类型", required = true)
    @NotBlank(message = "类型")
    private String type;

}
