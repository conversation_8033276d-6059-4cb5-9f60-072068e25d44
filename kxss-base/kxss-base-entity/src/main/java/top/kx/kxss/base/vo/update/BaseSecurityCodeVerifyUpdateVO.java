package top.kx.kxss.base.vo.update;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.SuperEntity;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 表单修改方法VO
 * 防伪验证信息
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-06 16:18:00
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode
@Builder
@ApiModel(value = "BaseSecurityCodeVerifyUpdateVO", description = "防伪验证信息")
public class BaseSecurityCodeVerifyUpdateVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotNull(message = "请填写主键", groups = SuperEntity.Update.class)
    private Long id;

    /**
     * 防伪码
     */
    @ApiModelProperty(value = "防伪码")
    @NotEmpty(message = "请填写防伪码")
    @Size(max = 255, message = "防伪码长度不能超过{max}")
    private String securityCode;
    /**
     * 验证时间
     */
    @ApiModelProperty(value = "验证时间")
    @NotNull(message = "请填写验证时间")
    private LocalDateTime verifyTime;
    /**
     * 第几次验证
     */
    @ApiModelProperty(value = "第几次验证")
    @NotNull(message = "请填写第几次验证")
    private Integer count;
    /**
     * 验证地址
     */
    @ApiModelProperty(value = "验证地址")
    @Size(max = 255, message = "验证地址长度不能超过{max}")
    private String address;
    /**
     * 经度
     */
    @ApiModelProperty(value = "经度")
    private BigDecimal longitude;
    /**
     * 纬度
     */
    @ApiModelProperty(value = "纬度")
    private BigDecimal latitude;
    /**
     * 验证用户ID
     */
    @ApiModelProperty(value = "验证用户ID")
    private Long userId;
    /**
     * 创建人组织
     */
    @ApiModelProperty(value = "创建人组织")
    private Long createdOrgId;


}
