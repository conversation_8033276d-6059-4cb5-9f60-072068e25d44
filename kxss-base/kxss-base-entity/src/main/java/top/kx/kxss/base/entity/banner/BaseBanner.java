package top.kx.kxss.base.entity.banner;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.Entity;

import static com.baomidou.mybatisplus.annotation.SqlCondition.EQUAL;
import static top.kx.kxss.model.constant.Condition.LIKE;


/**
 * <p>
 * 实体类
 * 轮播图
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-25 15:35:00
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@TableName("base_banner")
public class BaseBanner extends Entity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 图片ID
     */
    @TableField(value = "image", condition = EQUAL)
    private Long image;
    /**
     * 是否启用 0 停用 1 启用
     */
    @TableField(value = "state", condition = EQUAL)
    private Boolean state;
    /**
     * 类型 1 首页
     */
    @TableField(value = "type_", condition = EQUAL)
    private String type;
    /**
     * 排序
     */
    @TableField(value = "sort_value", condition = EQUAL)
    private Integer sortValue;
    /**
     * 跳转地址
     */
    @TableField(value = "jump_url", condition = LIKE)
    private String jumpUrl;
    /**
     * 创建人组织
     */
    @TableField(value = "created_org_id", condition = EQUAL)
    private Long createdOrgId;
    /**
     * 应用ID
     */
    @TableField(value = "client_id", condition = EQUAL)
    private Long clientId;



}
