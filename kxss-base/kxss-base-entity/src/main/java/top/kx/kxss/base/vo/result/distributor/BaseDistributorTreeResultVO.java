package top.kx.kxss.base.vo.result.distributor;

import cn.hutool.core.map.MapUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.TreeEntity;
import top.kx.basic.interfaces.echo.EchoVO;

import java.io.Serializable;
import java.util.Map;

/**
 * <p>
 * 实体类
 * 组织
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "BaseDistributorTreeResultVO", description = "经销商")
public class BaseDistributorTreeResultVO extends TreeEntity<BaseDistributorTreeResultVO, Long> implements Serializable, EchoVO {

    private static final long serialVersionUID = 1L;
    private Map<String, Object> echoMap = MapUtil.newHashMap();

    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String name;
    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    private String mobile;

    /**
     * 员工ID
     */
    @ApiModelProperty(value = "员工ID")
    private Long userId;
    /**
     * 性别;[1-男 2-女 3-未知]
     */
    @ApiModelProperty(value = "性别;[1-男 2-女 3-未知]")
    private String sex;
    /**
     * 经销商编号
     */
    @ApiModelProperty(value = "经销商编号")
    private String code;
    /**
     * 经销商层级
     */
    @ApiModelProperty(value = "经销商层级")
    private Integer level;
    /**
     * 上级ID
     */
    @ApiModelProperty(value = "上级ID")
    private Long parentId;
    /**
     * 店铺名称
     */
    @ApiModelProperty(value = "店铺名称")
    private String shopName;
    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址")
    private String address;
    /**
     * 区域-数组
     */
    @ApiModelProperty(value = "区域-数组")
    private String regions;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remarks;

    @ApiModelProperty(value = "拿货数量")
    private Integer purchaseNum;
}
