package top.kx.kxss.base.vo.result.security;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import cn.hutool.core.map.MapUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.Entity;
import top.kx.basic.interfaces.echo.EchoVO;

import java.io.Serializable;
import java.util.Map;

/**
 * <p>
 * 表单查询方法返回值VO
 * 防伪码批次-详情
 * </p>
 *
 * <AUTHOR>
 * @date 2025-04-25 17:45:12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@ExcelTarget("防伪码批次导出")
@ApiModel(value = "BaseSecurityCodeBatchDetailsExportResultVO", description = "防伪码批次-详情")
public class BaseSecurityCodeBatchDetailsExportResultVO extends Entity<Long> implements Serializable, EchoVO {

    private static final long serialVersionUID = 1L;

    private Map<String, Object> echoMap = MapUtil.newHashMap();

    /**
    * 批次id
    */
    @ApiModelProperty(value = "批次id")
    private Long batchId;

    @Excel(name = "防伪码-母码", orderNum = "0", width = 20)
    @ApiModelProperty(value = "防伪码")
    private String bigSecurityCode;
    /**
    * 防伪码
    */
    @Excel(name = "防伪码-子码",  orderNum = "1", width = 20)
    @ApiModelProperty(value = "防伪码")
    private String securityCode;

    @ApiModelProperty(value = "母码防伪码链接")
    @Excel(name = "母码防伪码链接", orderNum = "2", width = 20)
    private String bigUrl;

    /**
    * 防伪链接
    */
    @Excel(name = "子码防伪码链接", orderNum = "3", width = 20)
    @ApiModelProperty(value = "子码防伪码链接")
    private String url;


    @ApiModelProperty(value = "跳转类型:1-小程序，2-H5")
    @Excel(name = "子码跳转类型", orderNum = "4", replace = {"小程序_1", "H5_2"}, width = 20)
    private String linkType;

    @ApiModelProperty(value = "跳转类型:1-小程序，2-H5")
    @Excel(name = "母码跳转类型", orderNum = "5", replace = {"小程序_1", "H5_2", "-_null"}, width = 20)
    private String bigLinkType;
    /**
    * 创建人组织
    */
    @ApiModelProperty(value = "创建人组织")
    private Long createdOrgId;



}
