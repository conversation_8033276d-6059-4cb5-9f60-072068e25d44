package top.kx.kxss.base.entity.distributor;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import static com.baomidou.mybatisplus.annotation.SqlCondition.EQUAL;
import static top.kx.kxss.model.constant.Condition.LIKE;
import top.kx.basic.base.entity.Entity;


/**
 * <p>
 * 实体类
 * 耗材经销商信息
 * </p>
 *
 * <AUTHOR>
 * @date 2025-03-07 15:45:07
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@TableName("base_distributor")
public class BaseDistributor extends Entity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 姓名
     */
    @TableField(value = "name", condition = LIKE)
    private String name;
    /**
     * 手机号
     */
    @TableField(value = "mobile", condition = LIKE)
    private String mobile;
    /**
     * 员工ID
     */
    @TableField(value = "user_id", condition = EQUAL)
    private Long userId;
    /**
     * 性别;[1-男 2-女 3-未知]
     */
    @TableField(value = "sex", condition = EQUAL)
    private String sex;
    /**
     * 经销商编号
     */
    @TableField(value = "code_", condition = LIKE)
    private String code;
    /**
     * 经销商层级
     */
    @TableField(value = "level", condition = EQUAL)
    private Integer level;
    /**
     * 上级ID
     */
    @TableField(value = "parent_id", condition = EQUAL)
    private Long parentId;
    /**
     * 店铺名称
     */
    @TableField(value = "shop_name", condition = LIKE)
    private String shopName;
    /**
     * 详细地址
     */
    @TableField(value = "address", condition = LIKE)
    private String address;
    /**
     * 区域-数组
     */
    @TableField(value = "regions", condition = LIKE)
    private String regions;
    /**
     * 备注
     */
    @TableField(value = "remarks", condition = LIKE)
    private String remarks;
    /**
     * 创建人组织
     */
    @TableField(value = "created_org_id", condition = EQUAL)
    private Long createdOrgId;



}
