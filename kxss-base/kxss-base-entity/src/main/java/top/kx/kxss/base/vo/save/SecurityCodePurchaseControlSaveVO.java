package top.kx.kxss.base.vo.save;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <p>
 * 表单查询方法返回值VO
 * 会员信息
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-24 18:03:33
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@Builder
@ApiModel(value = "SecurityCodePurchaseControlSaveVO", description = "要货新增")
public class SecurityCodePurchaseControlSaveVO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 性别
     */
    @ApiModelProperty(value = "序列号", required = true)
    @NotBlank(message = "请填写序列号")
    private String securityCode;
    /**
     * 扫码类型 1 扫码查询 2 NFC查询 3 手动输入
     */
    @ApiModelProperty(value = "扫码类型 1 扫码查询 2 NFC查询 3 手动输入", required = true)
    @NotBlank(message = "请填写扫码类型")
    private String type;
    /**
     * 商品ID
     */
    @ApiModelProperty(value = "商品ID",required = true)
    @NotNull(message = "请选择商品")
    private Long productId;
    /**
     * 经销商ID
     */
    @ApiModelProperty(value = "经销商ID",required = true)
    @NotNull(message = "请选择经销商")
    private Long distributorId;

    @ApiModelProperty(value = "拿货id",required = true)
    @NotNull(message = "请选择拿货订单")
    private Long purchaseId;


    @ApiModelProperty(value = "拿货备注")
    private String purchaseRemarks;

    /**
     * 经销商查询
     */
    @ApiModelProperty(value = "是否来源经销商录入")
    private Boolean isDistributor;


}
