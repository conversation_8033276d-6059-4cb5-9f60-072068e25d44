package top.kx.kxss.base.vo.update.security;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.SuperEntity;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <p>
 * 表单修改方法VO
 * 防伪码批次-详情
 * </p>
 *
 * <AUTHOR>
 * @date 2025-04-25 17:45:12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode
@Builder
@ApiModel(value = "BaseSecurityCodeBatchDetailsUpdateVO", description = "防伪码批次-详情")
public class BaseSecurityCodeBatchDetailsUpdateVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotNull(message = "请填写主键", groups = SuperEntity.Update.class)
    private Long id;

    /**
     * 批次id
     */
    @ApiModelProperty(value = "批次id")
    @NotNull(message = "请填写批次id")
    private Long batchId;
    /**
     * 防伪码
     */
    @ApiModelProperty(value = "防伪码")
    @Size(max = 255, message = "防伪码长度不能超过{max}")
    private String securityCode;
    /**
     * 防伪码
     */
    @ApiModelProperty(value = "防伪码")
    @Size(max = 255, message = "防伪码长度不能超过{max}")
    private String bigSecurityCode;
    /**
     * 防伪链接
     */
    @ApiModelProperty(value = "防伪链接")
    @Size(max = 255, message = "防伪链接长度不能超过{max}")
    private String url;
    /**
     * 大链接
     */
    @ApiModelProperty(value = "大链接")
    @Size(max = 255, message = "大链接长度不能超过{max}")
    private String bigUrl;


    @ApiModelProperty(value = "跳转类型:1-小程序，2-H5")
    @Size(max = 1, message = "跳转类型:1-小程序，2-H5长度不能超过{max}")
    private String linkType;

    @ApiModelProperty(value = "跳转类型:1-小程序，2-H5")
    @Size(max = 1, message = "跳转类型:1-小程序，2-H5长度不能超过{max}")
    private String bigLinkType;
    /**
     * 创建人组织
     */
    @ApiModelProperty(value = "创建人组织")
    private Long createdOrgId;


}
