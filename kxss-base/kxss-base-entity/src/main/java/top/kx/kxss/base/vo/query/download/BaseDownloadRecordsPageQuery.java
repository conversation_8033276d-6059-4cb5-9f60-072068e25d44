package top.kx.kxss.base.vo.query.download;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;


/**
 * <p>
 * 表单查询条件VO
 * 下载记录
 * </p>
 *
 * <AUTHOR>
 * @date 2025-04-25 17:45:12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode
@Builder
@ApiModel(value = "BaseDownloadRecordsPageQuery", description = "下载记录")
public class BaseDownloadRecordsPageQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private Long id;

    /**
    * 资源ID
    */
    @ApiModelProperty(value = "资源ID")
    private Long sourceId;
    /**
    * 资源类型:BATCH-防伪码批次
    */
    @ApiModelProperty(value = "资源类型:BATCH-防伪码批次")
    private String sourceType;
    /**
    * 员工ID
    */
    @ApiModelProperty(value = "员工ID")
    private Long employeeId;
    /**
    * 下载内容链接
    */
    @ApiModelProperty(value = "下载内容链接")
    private Long url;
    /**
    * 创建人组织
    */
    @ApiModelProperty(value = "创建人组织")
    private Long createdOrgId;



}
