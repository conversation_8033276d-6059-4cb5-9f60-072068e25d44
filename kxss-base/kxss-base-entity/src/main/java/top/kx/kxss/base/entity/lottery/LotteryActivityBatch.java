package top.kx.kxss.base.entity.lottery;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.Entity;

import static top.kx.kxss.model.constant.Condition.LIKE;


/**
 * <p>
 * 实体类
 * 活动与防伪码批次关联表
 * </p>
 *
 * <AUTHOR>
 * @date 2025-09-15 17:23:11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@TableName("lottery_activity_batch")
public class LotteryActivityBatch extends Entity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 活动ID
     */
    @TableField(value = "activity_id")
    private Long activityId;

    /**
     * 防伪码批次号
     */
    @TableField(value = "batch_code", condition = LIKE)
    private String batchCode;



}
