package top.kx.kxss.base.entity.member;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.Entity;

import static com.baomidou.mybatisplus.annotation.SqlCondition.EQUAL;
import static top.kx.kxss.model.constant.Condition.LIKE;


/**
 * <p>
 * 实体类
 * 会员信息
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-24 18:03:33
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@TableName("member_info")
public class MemberInfo extends Entity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 会员编码
     */
    @TableField(value = "code", condition = LIKE)
    private String code;
    /**
     * 姓名
     */
    @TableField(value = "name", condition = LIKE)
    private String name;
    /**
     * 真实姓名
     */
    @TableField(value = "full_name", condition = LIKE)
    private String fullName;
    /**
     * 手机号
     */
    @TableField(value = "mobile", condition = LIKE)
    private String mobile;
    /**
     * 性别
     */
    @TableField(value = "sex", condition = LIKE)
    private String sex;
    /**
     * 生日
     */
    @TableField(value = "birth", condition = LIKE)
    private String birth;
    /**
     * 备注
     */
    @TableField(value = "remarks", condition = LIKE)
    private String remarks;
    /**
     * 创建人组织
     */
    @TableField(value = "created_org_id", condition = EQUAL)
    private Long createdOrgId;

    /**
     * 用户ID
     */
    @TableField(value = "user_id", condition = EQUAL)
    private Long userId;
    /**
     * 区域-数组
     */
    @TableField(value = "regions", condition = EQUAL)
    private String regions;

    /**
     * 头像ID
     */
    @TableField(value = "avatar_id", condition = EQUAL)
    private Long avatarId;
    /**
     * 商品注册数
     */
    @TableField(value = "product_num", condition = EQUAL)
    private Integer productNum;



}
