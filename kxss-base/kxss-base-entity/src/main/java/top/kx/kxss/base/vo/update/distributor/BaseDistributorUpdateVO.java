package top.kx.kxss.base.vo.update.distributor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.annotation.constraints.NotEmptyPattern;
import top.kx.basic.base.entity.SuperEntity;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

import static top.kx.basic.utils.ValidatorUtil.REGEX_MOBILE;

/**
 * <p>
 * 表单修改方法VO
 * 耗材经销商信息
 * </p>
 *
 * <AUTHOR>
 * @date 2025-03-07 15:45:07
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode
@Builder
@ApiModel(value = "BaseDistributorUpdateVO", description = "耗材经销商信息")
public class BaseDistributorUpdateVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotNull(message = "请填写主键", groups = SuperEntity.Update.class)
    private Long id;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    @NotEmpty(message = "请填写姓名")
    @Size(max = 50, message = "姓名长度不能超过{max}")
    private String name;
    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    @NotEmpty(message = "请填写手机号")
    @Size(max = 20, message = "手机号长度不能超过{max}")
    @NotEmptyPattern(regexp = REGEX_MOBILE, message = "请输入正确的手机号")
    private String mobile;

    /**
     * 员工ID
     */
    @ApiModelProperty(value = "员工ID", hidden = true)
    private Long userId;
    /**
     * 性别;[1-男 2-女 3-未知]
     */
    @ApiModelProperty(value = "性别;[1-男 2-女 3-未知]")
    private String sex;
    /**
     * 经销商编号
     */
    @ApiModelProperty(value = "经销商编号")
    @NotEmpty(message = "请填写经销商编号")
    @Size(max = 50, message = "经销商编号长度不能超过{max}")
    private String code;
    /**
     * 经销商层级
     */
    @ApiModelProperty(value = "经销商层级")
    private Integer level;
    /**
     * 上级ID
     */
    @ApiModelProperty(value = "上级ID")
    private Long parentId;
    /**
     * 店铺名称
     */
    @ApiModelProperty(value = "店铺名称")
    @NotEmpty(message = "请填写店铺名称")
    @Size(max = 50, message = "店铺名称长度不能超过{max}")
    private String shopName;
    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址")
    @Size(max = 200, message = "详细地址长度不能超过{max}")
    private String address;
    /**
     * 区域-数组
     */
    @ApiModelProperty(value = "区域-数组")
    @Size(max = 255, message = "区域-数组长度不能超过{max}")
    private String regions;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Size(max = 255, message = "备注长度不能超过{max}")
    private String remarks;
    /**
     * 创建人组织
     */
    @ApiModelProperty(value = "创建人组织")
    private Long createdOrgId;


}
