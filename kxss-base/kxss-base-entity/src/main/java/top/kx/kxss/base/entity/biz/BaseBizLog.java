package top.kx.kxss.base.entity.biz;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.Entity;

import static com.baomidou.mybatisplus.annotation.SqlCondition.EQUAL;
import static top.kx.kxss.model.constant.Condition.LIKE;


/**
 * <p>
 * 实体类
 * 防伪码操作日志
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-29 14:02:40
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@TableName("base_biz_log")
public class BaseBizLog extends Entity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 防伪序列号
     */
    @TableField(value = "security_code", condition = LIKE)
    private String securityCode;
    /**
     * 扫码类型 1 扫码查询 2 NFC查询 3 手动输入
     *
     */
    @TableField(value = "type_", condition = LIKE)
    private String type;
    /**
     * 状态 1 已导入 2 已绑定 3 已注册 4 已报废
     */
    @TableField(value = "status", condition = LIKE)
    private String status;
    /**
     * 描述
     */
    @TableField(value = "desc_", condition = LIKE)
    private String desc;
    /**
     * 操作之前内容
     */
    @TableField(value = "content", condition = LIKE)
    private String content;
    /**
     * 位置
     */
    @TableField(value = "location", condition = LIKE)
    private String location;
    /**
     * 创建人组织
     */
    @TableField(value = "created_org_id", condition = EQUAL)
    private Long createdOrgId;
    /**
     * 删除标识 0 未删除 1 已删除
     */
    @TableField(value = "delete_flag", condition = EQUAL)
    private Integer deleteFlag;



}
