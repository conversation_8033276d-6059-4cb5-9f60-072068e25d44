package top.kx.kxss.base.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.Entity;

import java.time.LocalDateTime;

import static com.baomidou.mybatisplus.annotation.SqlCondition.EQUAL;
import static top.kx.kxss.model.constant.Condition.LIKE;


/**
 * <p>
 * 实体类
 * 防伪信息
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-25 13:40:18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@TableName("base_security_code")
public class BaseSecurityCode extends Entity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 序列号
     */
    @TableField(value = "code", condition = EQUAL)
    private String code;
    /**
     * 防伪码
     */
    @TableField(value = "security_code", condition = EQUAL)
    private String securityCode;
    /**
     * 防伪链接
     */
    @TableField(value = "url", condition = LIKE)
    private String url;
    /**
     * 绑定日期
     */
    @TableField(value = "bill_date", condition = EQUAL, fill = FieldFill.UPDATE)
    private LocalDateTime billDate;
    /**
     * 注册时间
     */
    @TableField(value = "register_time", condition = EQUAL)
    private LocalDateTime registerTime;
    /**
     * 报废时间
     */
    @TableField(value = "scrap_time", condition = EQUAL)
    private LocalDateTime scrapTime;
    /**
     * 录入类型
     */
    @TableField(value = "enter_type", condition = EQUAL, fill = FieldFill.UPDATE)
    private String enterType;
    /**
     * 绑定人员
     */
    @TableField(value = "bind_user", condition = EQUAL, fill = FieldFill.UPDATE)
    private Long bindUser;
    /**
     * 报废人员
     */
    @TableField(value = "scrap_user", condition = EQUAL)
    private Long scrapUser;

    /**
     * 商品ID
     */
    @TableField(value = "product_id", condition = EQUAL, fill = FieldFill.UPDATE)
    private Long productId;
    /**
     * 仓库(字典)
     */
    @TableField(value = "ware_house", condition = EQUAL, fill = FieldFill.UPDATE)
    private String wareHouse;
    /**
     * 供应商(字典)
     */
    @TableField(value = "supplier", condition = EQUAL, fill = FieldFill.UPDATE)
    private String supplier;
    /**
     * 创建人组织
     */
    @TableField(value = "created_org_id", condition = EQUAL)
    private Long createdOrgId;
    /**
     * 会员ID
     */
    @TableField(value = "member_id", condition = EQUAL)
    private Long memberId;

    /**
     * 查询次数
     */
    @TableField(value = "select_num", condition = EQUAL)
    private Integer selectNum;

    /**
     * 状态 1 已导入 2 已绑定 3 已注册 4 已报废
     */
    @TableField(value = "status", condition = EQUAL)
    private String status;

    /**
     * 报废原因
     */
    @TableField(value = "scrap_reason", condition = EQUAL)
    private String scrapReason;

    /**
     * 要货时间
     */
    @TableField(value = "purchase_control_time", condition = EQUAL, fill = FieldFill.UPDATE)
    private LocalDateTime purchaseControlTime;

    /**
     * 要货经销商
     */
    @TableField(value = "distributor_id", condition = EQUAL, fill = FieldFill.UPDATE)
    private Long distributorId;


    @TableField(value = "purchase_id", condition = EQUAL, fill = FieldFill.UPDATE)
    private Long purchaseId;


    /**
     * 要货操作人
     */
    @TableField(value = "purchase_control_by", condition = EQUAL, fill = FieldFill.UPDATE)
    private Long purchaseControlBy;

    /**
     * 要货录入方式
     */
    @TableField(value = "purchase_enter_type", condition = EQUAL, fill = FieldFill.UPDATE)
    private String purchaseEnterType;

    @TableField(value = "purchase_remarks", condition = LIKE, fill = FieldFill.UPDATE)
    private String purchaseRemarks;


    /**
     * 注册经销商
     */
    @TableField(value = "dealers_id", condition = EQUAL, fill = FieldFill.UPDATE)
    private Long dealersId;

    /**
     * 大码
     */
    @TableField(value = "big_code", condition = EQUAL)
    private String bigCode;

    /**
     * 批次ID
     */
    @TableField(value = "batch_id", condition = EQUAL)
    private Long batchId;

    /**
     * 批次编码
     */
    @TableField(value = "batch_code", condition = LIKE)
    private String batchCode;

    /**
     * 跳转类型:1-小程序，2-H5
     */
    @TableField(value = "link_type", condition = LIKE)
    private String linkType;


}
