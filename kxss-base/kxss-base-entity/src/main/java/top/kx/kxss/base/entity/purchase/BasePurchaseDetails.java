package top.kx.kxss.base.entity.purchase;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import static com.baomidou.mybatisplus.annotation.SqlCondition.EQUAL;
import static top.kx.kxss.model.constant.Condition.LIKE;
import top.kx.basic.base.entity.Entity;


/**
 * <p>
 * 实体类
 * 要货详情
 * </p>
 *
 * <AUTHOR>
 * @date 2025-04-10 14:47:18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@TableName("base_purchase_details")
public class BasePurchaseDetails extends Entity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 防伪码
     */
    @TableField(value = "security_code", condition = LIKE)
    private String securityCode;
    /**
     * 商品id
     */
    @TableField(value = "product_id", condition = EQUAL)
    private Long productId;
    /**
     * 商品名称
     */
    @TableField(value = "product_name", condition = LIKE)
    private String productName;
    /**
     * 经销商ID
     */
    @TableField(value = "distributor_id", condition = EQUAL)
    private Long distributorId;
    /**
     * 拿货id
     */
    @TableField(value = "purchase_id", condition = EQUAL)
    private Long purchaseId;
    /**
     * 操作员工
     */
    @TableField(value = "purchase_emp", condition = EQUAL)
    private Long purchaseEmp;
    /**
     * 要货录入方式
     */
    @TableField(value = "purchase_enter_type", condition = LIKE)
    private String purchaseEnterType;
    /**
     * 拿货备注
     */
    @TableField(value = "remarks", condition = LIKE)
    private String remarks;
    /**
     * 创建人组织
     */
    @TableField(value = "created_org_id", condition = EQUAL)
    private Long createdOrgId;



}
