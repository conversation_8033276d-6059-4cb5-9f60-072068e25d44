package top.kx.kxss.base.vo.update;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.SuperEntity;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <p>
 * 表单修改方法VO
 * 防伪信息规格属性
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-17 10:23:59
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode
@Builder
@ApiModel(value = "BaseSecurityCodeAttributeDetailsUpdateVO", description = "防伪信息规格属性")
public class BaseSecurityCodeAttributeDetailsUpdateVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotNull(message = "请填写主键", groups = SuperEntity.Update.class)
    private Long id;

    /**
     * 防伪属性ID
     */
    @ApiModelProperty(value = "防伪属性ID")
    private Long codeAttributeId;
    /**
     * 基础属性ID
     */
    @ApiModelProperty(value = "基础属性ID")
    @NotNull(message = "请填写基础属性ID")
    private Long attributeId;
    /**
     * 规格属性value
     */
    @ApiModelProperty(value = "规格属性value")
    @NotEmpty(message = "请填写规格属性value")
    @Size(max = 255, message = "规格属性value长度不能超过{max}")
    private String attributeValue;
    /**
     * 创建人组织
     */
    @ApiModelProperty(value = "创建人组织")
    private Long createdOrgId;


}
