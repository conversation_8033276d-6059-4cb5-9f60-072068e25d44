package top.kx.kxss.base.entity.pact;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.Entity;

import static com.baomidou.mybatisplus.annotation.SqlCondition.EQUAL;
import static top.kx.kxss.model.constant.Condition.LIKE;


/**
 * <p>
 * 实体类
 * 协议配置
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-25 17:21:04
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@TableName("base_pact")
public class BasePact extends Entity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 标题
     */
    @TableField(value = "title", condition = LIKE)
    private String title;
    /**
     * 内容
     */
    @TableField(value = "content", condition = LIKE)
    private String content;
    /**
     * 跳转地址
     */
    @TableField(value = "jump_url", condition = LIKE)
    private String jumpUrl;
    /**
     * 创建人组织
     */
    @TableField(value = "created_org_id", condition = EQUAL)
    private Long createdOrgId;
    /**
     * 类型 1 登录协议 2 用户协议
     */
    @TableField(value = "type_", condition = LIKE)
    private String type;

    /**
     * 排序
     */
    @TableField(value = "sort_value", condition = LIKE)
    private Integer sortValue;

    /**
     * 启用/禁用
     */
    @TableField(value = "state", condition = LIKE)
    private Boolean state;


}
