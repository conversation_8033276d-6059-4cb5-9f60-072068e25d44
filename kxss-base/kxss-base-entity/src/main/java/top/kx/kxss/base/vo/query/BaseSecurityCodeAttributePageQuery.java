package top.kx.kxss.base.vo.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * <p>
 * 表单查询条件VO
 * 防伪信息规格属性
 * </p>
 *
 * <AUTHOR>
 * @date 2025-02-26 16:17:06
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode
@Builder
@ApiModel(value = "BaseSecurityCodeAttributePageQuery", description = "防伪信息规格属性")
public class BaseSecurityCodeAttributePageQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private Long id;
    /**
    * 防伪码
    */
    @ApiModelProperty(value = "防伪码")
    private String securityCode;

    @ApiModelProperty(value = "商品ID")
    private Long productId;

    @ApiModelProperty(value = "关键词")
    private String keyword;

    @ApiModelProperty(value = "时间筛选类型：1-创建时间，2-更新时间")
    private String type;

    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;

    /**
    * 防伪ID
    */
    @ApiModelProperty(value = "防伪ID")
    private Long securityCodeId;

    /**
    * 创建人组织
    */
    @ApiModelProperty(value = "创建人组织")
    private Long createdOrgId;



}
