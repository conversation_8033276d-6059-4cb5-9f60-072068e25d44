package top.kx.kxss.base.entity.security;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.Entity;

import static com.baomidou.mybatisplus.annotation.SqlCondition.EQUAL;
import static top.kx.kxss.model.constant.Condition.LIKE;


/**
 * <p>
 * 实体类
 * 防伪码批次-详情
 * </p>
 *
 * <AUTHOR>
 * @date 2025-04-25 17:45:12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@TableName("base_security_code_batch_details")
public class BaseSecurityCodeBatchDetails extends Entity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 批次id
     */
    @TableField(value = "batch_id", condition = EQUAL)
    private Long batchId;
    /**
     * 防伪码
     */
    @TableField(value = "security_code", condition = LIKE)
    private String securityCode;

    @TableField(value = "big_security_code", condition = LIKE)
    private String bigSecurityCode;
    /**
     * 防伪链接
     */
    @TableField(value = "url", condition = LIKE)
    private String url;
    /**
     * 防伪链接
     */
    @TableField(value = "big_url", condition = LIKE)
    private String bigUrl;

    @TableField(value = "link_type", condition = LIKE)
    private String linkType;

    @TableField(value = "big_link_type", condition = LIKE)
    private String bigLinkType;
    /**
     * 创建人组织
     */
    @TableField(value = "created_org_id", condition = EQUAL)
    private Long createdOrgId;



}
