package top.kx.kxss.base.vo.update.question;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.SuperEntity;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <p>
 * 表单修改方法VO
 * 常见问题
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-26 16:24:10
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode
@Builder
@ApiModel(value = "BaseQuestionUpdateVO", description = "常见问题")
public class BaseQuestionUpdateVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotNull(message = "请填写主键", groups = SuperEntity.Update.class)
    private Long id;

    /**
     * 问题名称
     */
    @ApiModelProperty(value = "问题名称")
    @NotEmpty(message = "请填写问题名称")
    @Size(max = 200, message = "问题名称长度不能超过{max}")
    private String name;
    /**
     * 回答
     */
    @ApiModelProperty(value = "回答")
    @NotEmpty(message = "请填写回答")
    @Size(max = 65535, message = "回答长度不能超过{max}")
    private String answer;
    /**
     * 是否启用 0 停用 1 启用
     */
    @ApiModelProperty(value = "是否启用 0 停用 1 启用")
    @NotNull(message = "请填写是否启用 0 停用 1 启用")
    private Boolean state;
    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    @NotNull(message = "请填写排序")
    private Integer sortValue;
    /**
     * 创建人组织
     */
    @ApiModelProperty(value = "创建人组织")
    private Long createdOrgId;


}
