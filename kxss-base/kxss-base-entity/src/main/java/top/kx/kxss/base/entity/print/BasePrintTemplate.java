package top.kx.kxss.base.entity.print;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.Entity;

import static com.baomidou.mybatisplus.annotation.SqlCondition.EQUAL;
import static top.kx.kxss.model.constant.Condition.LIKE;


/**
 * <p>
 * 实体类
 * 打印模板
 * </p>
 *
 * <AUTHOR>
 * @date 2025-08-27 10:48:00
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@TableName("base_print_template")
public class BasePrintTemplate extends Entity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 名称
     */
    @TableField(value = "name", condition = LIKE)
    private String name;

    /**
     * 商品ID
     */
    @TableField(value = "product_id", condition = EQUAL)
    private Long productId;
    /**
     * 宽
     */
    @TableField(value = "label_size_width", condition = EQUAL)
    private Integer labelSizeWidth;
    /**
     * 高
     */
    @TableField(value = "label_size_height", condition = EQUAL)
    private Integer labelSizeHeight;
    /**
     * 属性
     */
    @TableField(value = "used_attr_ids", condition = LIKE)
    private String usedAttrIds;
    /**
     * 打印内容
     */
    @TableField(value = "print_content", condition = LIKE)
    private String printContent;
    /**
     * 备注
     */
    @TableField(value = "remarks", condition = LIKE)
    private String remarks;
    /**
     * 创建人组织
     */
    @TableField(value = "created_org_id", condition = EQUAL)
    private Long createdOrgId;



}
