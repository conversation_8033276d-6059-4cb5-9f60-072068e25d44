package top.kx.kxss.base.entity.print;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import static com.baomidou.mybatisplus.annotation.SqlCondition.EQUAL;
import static top.kx.kxss.model.constant.Condition.LIKE;
import top.kx.basic.base.entity.Entity;


/**
 * <p>
 * 实体类
 * 打印模板
 * </p>
 *
 * <AUTHOR>
 * @date 2025-08-27 10:48:00
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@TableName("base_print_template")
public class BasePrintTemplate extends Entity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 商品ID
     */
    @TableField(value = "product_id", condition = EQUAL)
    private Long productId;
    /**
     * 标签尺寸(毫米)
     */
    @TableField(value = "label_size_mm", condition = LIKE)
    private String labelSizeMm;
    /**
     * 属性
     */
    @TableField(value = "used_attr_ids", condition = LIKE)
    private String usedAttrIds;
    /**
     * 打印内容
     */
    @TableField(value = "print_content", condition = LIKE)
    private String printContent;
    /**
     * 备注
     */
    @TableField(value = "remarks", condition = LIKE)
    private String remarks;
    /**
     * 创建人组织
     */
    @TableField(value = "created_org_id", condition = EQUAL)
    private Long createdOrgId;
    /**
     * 删除标识 0 未删除 1 已删除
     */
    @TableField(value = "delete_flag", condition = EQUAL)
    private Integer deleteFlag;



}
