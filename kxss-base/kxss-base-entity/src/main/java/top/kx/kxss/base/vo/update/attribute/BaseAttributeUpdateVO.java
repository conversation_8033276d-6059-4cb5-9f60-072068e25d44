package top.kx.kxss.base.vo.update.attribute;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.SuperEntity;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 表单修改方法VO
 * 商品基础属性
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-16 18:44:16
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode
@Builder
@ApiModel(value = "BaseAttributeUpdateVO", description = "商品基础属性")
public class BaseAttributeUpdateVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotNull(message = "请填写主键", groups = SuperEntity.Update.class)
    private Long id;

    /**
     * 属性名称
     */
    @ApiModelProperty(value = "属性名称")
    @NotEmpty(message = "请填写属性名称")
    @Size(max = 100, message = "属性名称长度不能超过{max}")
    private String name;
    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    @Size(max = 20, message = "单位长度不能超过{max}")
    private String unit;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Boolean state;
    /**
     * 排序数量
     */
    @ApiModelProperty(value = "排序数量")
    private Integer sortValue;
    /**
     * 创建人组织
     */
    @ApiModelProperty(value = "创建人组织")
    private Long createdOrgId;

    @NotEmpty(message = "属性值不能为空")
    @ApiModelProperty(value = "属性值")
    private List<String> valueList;


}
