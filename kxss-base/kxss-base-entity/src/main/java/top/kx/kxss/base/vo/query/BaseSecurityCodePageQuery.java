package top.kx.kxss.base.vo.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * <p>
 * 表单查询条件VO
 * 防伪信息
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-25 13:40:18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode
@Builder
@ApiModel(value = "BaseSecurityCodePageQuery", description = "防伪信息")
public class BaseSecurityCodePageQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 序列号
     */
    @ApiModelProperty(value = "序列号")
    private String code;
    /**
     * 防伪码
     */
    @ApiModelProperty(value = "防伪码")
    private String securityCode;
    /**
     * 防伪链接
     */
    @ApiModelProperty(value = "防伪链接")
    private String url;
    /**
     * 绑定日期
     */
    @ApiModelProperty(value = "绑定日期")
    private LocalDateTime billDate;
    /**
     * 注册时间
     */
    @ApiModelProperty(value = "注册时间")
    private LocalDateTime registerTime;
    /**
     * 商品ID
     */
    @ApiModelProperty(value = "商品ID")
    private Long productId;
    /**
     * 仓库(字典)
     */
    @ApiModelProperty(value = "仓库(字典)")
    private String wareHouse;
    /**
     * 供应商(字典)
     */
    @ApiModelProperty(value = "供应商(字典)")
    private String supplier;
    /**
     * 创建人组织
     */
    @ApiModelProperty(value = "创建人组织", hidden = true)
    private Long createdOrgId;
    /**
     * 会员ID
     */
    @ApiModelProperty(value = "会员ID")
    private Long memberId;
    /**
     * 状态 1 已导入 2 已绑定 3 已注册 4 已报废
     */
    @ApiModelProperty(value = "状态 1 已导入 2 已绑定 3 已注册 4 已报废")
    private String status;
    /**
     * 报废时间
     */
    @ApiModelProperty(value = "报废时间")
    private LocalDateTime scrapTime;
    /**
     * 录入类型
     */
    @ApiModelProperty(value = "录入类型")
    private String enterType;
    /**
     * 绑定人员
     */
    @ApiModelProperty(value = "绑定人员")
    private Long bindUser;
    /**
     * 报废人员
     */
    @ApiModelProperty(value = "报废人员")
    private Long scrapUser;

    /**
     * 要货时间
     */
    @ApiModelProperty(value = "要货时间")
    private LocalDateTime purchaseControlTime;

    /**
     * 要货经销商
     */
    @ApiModelProperty(value = "要货经销商")
    private Long distributorId;

    @ApiModelProperty(value = "查询有属性的数据")
    private Boolean queryAttribute = false;

    @ApiModelProperty(value = "商品类型- 查询分类- QZ:球桌,QG:球杆,HC:耗材")
    private String category;

    /**
     * 大标
     */
    @ApiModelProperty(value = "大标")
    private String bigCode;

    /**
     * 批次ID
     */
    @ApiModelProperty(value = "批次ID")
    private Long batchId;


    /**
     * 批次编号
     */
    @ApiModelProperty(value = "批次编号")
    private String batchCode;


}
