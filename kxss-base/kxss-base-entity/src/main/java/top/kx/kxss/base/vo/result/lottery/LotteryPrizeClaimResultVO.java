package top.kx.kxss.base.vo.result.lottery;

import cn.hutool.core.map.MapUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.Entity;
import top.kx.basic.interfaces.echo.EchoVO;

import java.io.Serializable;
import java.util.Map;

/**
 * <p>
 * 表单查询方法返回值VO
 * 奖品领取表(含收货地址快照+物流信息)
 * </p>
 *
 * <AUTHOR>
 * @date 2025-09-15 17:23:11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@ApiModel(value = "LotteryPrizeClaimResultVO", description = "奖品领取表(含收货地址快照+物流信息)")
public class LotteryPrizeClaimResultVO extends Entity<Long> implements Serializable, EchoVO {

    private static final long serialVersionUID = 1L;

    private Map<String, Object> echoMap = MapUtil.newHashMap();

    @ApiModelProperty(value = "领取ID")
    private Long id;

    /**
    * 抽奖记录ID
    */
    @ApiModelProperty(value = "抽奖记录ID")
    private Long recordId;
    /**
    * 活动ID
    */
    @ApiModelProperty(value = "活动ID")
    private Long activityId;
    /**
    * 用户ID
    */
    @ApiModelProperty(value = "用户ID")
    private Long userId;
    /**
     * 收件人姓名
     */
    @ApiModelProperty(value = "收件人姓名")
    private String receiverName;

    /**
     * 收件人手机号
     */
    @ApiModelProperty(value = "收件人手机号")
    private String phone;

    /**
     * 省份
     */
    @ApiModelProperty(value = "省份")
    private String province;

    /**
     * 城市
     */
    @ApiModelProperty(value = "城市")
    private String city;

    /**
     * 区/县
     */
    @ApiModelProperty(value = "区/县")
    private String district;

    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址")
    private String detailAddress;

    /**
     * 领取状态: 0=未领取,1=已确认地址,2=已发货,3=已完成
     */
    @ApiModelProperty(value = "领取状态: 0=未领取,1=已确认地址,2=已发货,3=已完成")
    private String claimStatus;
    /**
    * 快递公司
    */
    @ApiModelProperty(value = "快递公司")
    private String expressCompany;
    /**
    * 快递单号
    */
    @ApiModelProperty(value = "快递单号")
    private String expressNo;
    /**
    * 额外信息(扩展字段，如备注/发货人)
    */
    @ApiModelProperty(value = "额外信息(扩展字段，如备注/发货人)")
    private String claimInfo;
    /**
    * 备注
    */
    @ApiModelProperty(value = "备注")
    private String remarks;



}
