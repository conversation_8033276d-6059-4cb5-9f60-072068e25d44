package top.kx.kxss.base.vo.save.download;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <p>
 * 表单保存方法VO
 * 下载记录
 * </p>
 *
 * <AUTHOR>
 * @date 2025-04-25 17:45:12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode
@Builder
@ApiModel(value = "BaseDownloadRecordsSaveVO", description = "下载记录")
public class BaseDownloadRecordsSaveVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 资源ID
     */
    @ApiModelProperty(value = "资源ID")
    @NotNull(message = "请填写资源ID")
    private Long sourceId;
    /**
     * 资源类型:BATCH-防伪码批次
     */
    @ApiModelProperty(value = "资源类型:BATCH-防伪码批次")
    @NotEmpty(message = "请填写资源类型:BATCH-防伪码批次")
    @Size(max = 255, message = "资源类型:BATCH-防伪码批次长度不能超过{max}")
    private String sourceType;
    /**
     * 员工ID
     */
    @ApiModelProperty(value = "员工ID")
    @NotNull(message = "请填写员工ID")
    private Long employeeId;
    /**
     * 下载内容链接
     */
    @ApiModelProperty(value = "下载内容链接")
    private Long url;
    /**
     * 创建人组织
     */
    @ApiModelProperty(value = "创建人组织")
    private Long createdOrgId;



}
