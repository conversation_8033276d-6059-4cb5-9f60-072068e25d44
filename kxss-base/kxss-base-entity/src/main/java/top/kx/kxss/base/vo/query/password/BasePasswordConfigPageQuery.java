package top.kx.kxss.base.vo.query.password;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 密码验证配置分页查询VO
 * </p>
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Builder
@ApiModel(value = "BasePasswordConfigPageQuery", description = "密码验证配置分页查询VO")
public class BasePasswordConfigPageQuery {

    /**
     * 业务模块代码
     */
    @ApiModelProperty(value = "业务模块代码")
    private String businessCode;

    /**
     * 业务模块名称
     */
    @ApiModelProperty(value = "业务模块名称")
    private String businessName;

    /**
     * 是否启用密码验证
     */
    @ApiModelProperty(value = "是否启用密码验证")
    private Boolean isEnabled;
}