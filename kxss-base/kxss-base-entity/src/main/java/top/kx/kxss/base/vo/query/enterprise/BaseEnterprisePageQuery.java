package top.kx.kxss.base.vo.query.enterprise;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;


/**
 * <p>
 * 表单查询条件VO
 * 企业
 * </p>
 *
 * <AUTHOR>
 * @date 2025-04-25 17:45:11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode
@Builder
@ApiModel(value = "BaseEnterprisePageQuery", description = "企业")
public class BaseEnterprisePageQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private Long id;

    /**
    * 企业全称
    */
    @ApiModelProperty(value = "企业全称")
    private String name;
    /**
    * 营业执照号码
    */
    @ApiModelProperty(value = "营业执照号码")
    private String businessLicense;
    /**
    * 状态;[0-禁用 1-启用]
    */
    @ApiModelProperty(value = "状态")
    private Boolean state;
    /**
    * 备注
    */
    @ApiModelProperty(value = "备注")
    private String remarks;
    /**
    * 创建人组织
    */
    @ApiModelProperty(value = "创建人组织")
    private Long createdOrgId;



}
