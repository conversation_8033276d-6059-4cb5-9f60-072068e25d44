package top.kx.kxss.base.entity.lottery;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.Entity;

import java.math.BigDecimal;

import static com.baomidou.mybatisplus.annotation.SqlCondition.EQUAL;
import static top.kx.kxss.model.constant.Condition.LIKE;


/**
 * <p>
 * 实体类
 * 用户抽奖记录表
 * </p>
 *
 * <AUTHOR>
 * @date 2025-09-15 17:23:11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@TableName("lottery_record")
public class LotteryRecord extends Entity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 活动ID
     */
    @TableField(value = "activity_id", condition = EQUAL)
    private Long activityId;
    /**
     * 用户ID
     */
    @TableField(value = "user_id", condition = EQUAL)
    private Long userId;
    /**
     * 机会ID
     */
    @TableField(value = "chance_id", condition = EQUAL)
    private Long chanceId;
    /**
     * 奖品库ID (引用原始奖品)
     */
    @TableField(value = "prize_id", condition = EQUAL)
    private Long prizeId;
    /**
     * 奖品名称快照
     */
    @TableField(value = "prize_name", condition = LIKE)
    private String prizeName;
    /**
     * 奖品类型快照: 1=谢谢参与,2=积分,3=优惠券,4=代金券,5=实物
     */
    @TableField(value = "prize_type", condition = EQUAL)
    private String prizeType;
    /**
     * 奖品面额/积分数快照
     */
    @TableField(value = "value_amount", condition = EQUAL)
    private BigDecimal valueAmount;
    /**
     * 使用门槛(金额门槛快照)
     */
    @TableField(value = "threshold", condition = EQUAL)
    private BigDecimal threshold;
    /**
     * 优惠券编码快照
     */
    @TableField(value = "coupon_code", condition = LIKE)
    private String couponCode;
    /**
     * 状态: 0=未中奖,1=已中奖
     */
    @TableField(value = "status", condition = EQUAL)
    private String status;
    /**
     * 备注
     */
    @TableField(value = "remarks", condition = LIKE)
    private String remarks;



}
