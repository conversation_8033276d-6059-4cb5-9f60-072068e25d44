package top.kx.kxss.base.vo.update.supplier;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.SuperEntity;
import java.io.Serializable;

/**
 * <p>
 * 表单修改方法VO
 * 供应商信息信息
 * </p>
 *
 * <AUTHOR>
 * @date 2025-02-28 16:36:35
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode
@Builder
@ApiModel(value = "BaseSupplierUpdateVO", description = "供应商信息信息")
public class BaseSupplierUpdateVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotNull(message = "请填写主键", groups = SuperEntity.Update.class)
    private Long id;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    @NotEmpty(message = "请填写姓名")
    @Size(max = 50, message = "姓名长度不能超过{max}")
    private String name;
    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    @NotEmpty(message = "请填写手机号")
    @Size(max = 20, message = "手机号长度不能超过{max}")
    private String mobile;
    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String companyName;
    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    private String sex;
    /**
     * 头像ID
     */
    @ApiModelProperty(value = "头像ID")
    private Long avatarId;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Size(max = 255, message = "备注长度不能超过{max}")
    private String remarks;
    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址")
    @Size(max = 200, message = "详细地址长度不能超过{max}")
    private String address;
    /**
     * 区域-数组
     */
    @ApiModelProperty(value = "区域-数组")
    @Size(max = 255, message = "区域-数组长度不能超过{max}")
    private String regions;
    /**
     * 创建人组织
     */
    @ApiModelProperty(value = "创建人组织")
    private Long createdOrgId;


}
