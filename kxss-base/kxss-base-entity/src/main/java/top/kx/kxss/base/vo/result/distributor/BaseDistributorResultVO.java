package top.kx.kxss.base.vo.result.distributor;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.hutool.core.map.MapUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.annotation.echo.Echo;
import top.kx.basic.base.entity.Entity;
import top.kx.basic.interfaces.echo.EchoVO;
import top.kx.kxss.model.constant.EchoApi;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * <p>
 * 表单查询方法返回值VO
 * 耗材经销商信息
 * </p>
 *
 * <AUTHOR>
 * @date 2025-03-07 15:45:07
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@ApiModel(value = "BaseDistributorResultVO", description = "耗材经销商信息")
public class BaseDistributorResultVO extends Entity<Long> implements Serializable, EchoVO {

    private static final long serialVersionUID = 1L;

    private Map<String, Object> echoMap = MapUtil.newHashMap();

    @ApiModelProperty(value = "主键")
    private Long id;

    /**
    * 姓名
    */
    @ApiModelProperty(value = "姓名")
    @Excel(name = "经销商名称", orderNum = "1", width = 18)
    private String name;
    /**
    * 手机号
    */
    @ApiModelProperty(value = "手机号")
    @Excel(name = "手机号", orderNum = "3", width = 15)
    private String mobile;

    /**
     * 员工ID
     */
    @ApiModelProperty(value = "员工ID")
    private Long userId;
    /**
     * 性别;[1-男 2-女 3-未知]
     */
    @ApiModelProperty(value = "性别;[1-男 2-女 3-未知]")
    private String sex;
    /**
    * 经销商编号
    */
    @ApiModelProperty(value = "经销商编号")
    @Excel(name = "编号", orderNum = "0", width = 15)
    private String code;
    /**
     * 经销商层级
     */
    @ApiModelProperty(value = "经销商层级")
    private Integer level;
    /**
     * 上级ID
     */
    @ApiModelProperty(value = "上级ID")
    @Echo(api = EchoApi.DISTRIBUTOR_ID_CLASS)
    private Long parentId;
    /**
    * 店铺名称
    */
    @ApiModelProperty(value = "店铺名称")
    @Excel(name = "店铺名称", orderNum = "2", width = 20)
    private String shopName;
    /**
    * 详细地址
    */
    @ApiModelProperty(value = "详细地址")
    @Excel(name = "地址", orderNum = "4", width = 30)
    private String address;
    /**
    * 区域-数组
    */
    @ApiModelProperty(value = "区域-数组")
    private String regions;
    /**
    * 备注
    */
    @ApiModelProperty(value = "备注")
    private String remarks;

    @ApiModelProperty(value = "拿货数量")
    @Excel(name = "拿货数量", orderNum = "5", width = 15)
    private Integer purchaseNum;

    @ApiModelProperty(value = "下级经销商拿货数量")
    @Excel(name = "下级拿货数量", orderNum = "6", width = 18)
    private Integer childPurchaseNum;

    @ApiModelProperty(value = "最近拿货时间")
    @Excel(name = "最后拿货时间", orderNum = "7", format = "yyyy-MM-dd HH:mm:ss", width = 25)
    private LocalDateTime purchaseTime;

    @ApiModelProperty(value = "拿货次数")
    private Long purchaseCount;

    /**
    * 创建人组织
    */
    @ApiModelProperty(value = "创建人组织")
    private Long createdOrgId;



}
