package top.kx.kxss.base.vo.update.lottery;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.SuperEntity;
import top.kx.kxss.base.vo.save.lottery.LotteryActivityPrizeSaveVO;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 表单修改方法VO
 * 抽奖活动表
 * </p>
 *
 * <AUTHOR>
 * @date 2025-09-15 17:23:11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode
@Builder
@ApiModel(value = "LotteryActivityUpdateVO", description = "抽奖活动表")
public class LotteryActivityUpdateVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "活动ID")
    @NotNull(message = "请填写活动ID", groups = SuperEntity.Update.class)
    private Long id;

    /**
     * 活动名称
     */
    @ApiModelProperty(value = "活动名称")
    @NotEmpty(message = "请填写活动名称")
    @Size(max = 30, message = "活动名称长度不能超过{max}")
    private String code;
    /**
     * 活动名称
     */
    @ApiModelProperty(value = "活动名称")
    @NotEmpty(message = "请填写活动名称")
    @Size(max = 100, message = "活动名称长度不能超过{max}")
    private String name;
    /**
     * 玩法类型: 1=大转盘,2=九宫格,3=摇一摇
     */
    @ApiModelProperty(value = "玩法类型: 1=大转盘,2=九宫格,3=摇一摇")
    @NotNull(message = "请填写玩法类型: 1=大转盘,2=九宫格,3=摇一摇")
    private String playType;
    /**
     * 活动类型: 1=注册抽奖
     */
    @ApiModelProperty(value = "活动类型: 1=注册抽奖")
    @NotNull(message = "请填写活动类型: 1=注册抽奖")
    private String type;
    /**
     * 活动描述
     */
    @ApiModelProperty(value = "活动描述")
    @Size(max = 65535, message = "活动描述长度不能超过{max}")
    private String description;
    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @NotNull(message = "请填写开始时间")
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @NotNull(message = "请填写结束时间")
    private LocalDateTime endTime;
    /**
     * 状态: 0=未开始,1=进行中,2=已结束,3=已下架
     */
    @ApiModelProperty(value = "状态: 0=未开始,1=进行中,2=已结束,3=已下架")
    @NotNull(message = "请填写状态: 0=未开始,1=进行中,2=已结束,3=已下架")
    private String status;
    /**
     * 员工ID
     */
    @ApiModelProperty(value = "员工ID")
    private Long employeeId;
    /**
     * 每用户总抽奖次数限制 (0=不限)
     */
    @ApiModelProperty(value = "每用户总抽奖次数限制 (0=不限)")
    private Integer totalLimit;
    /**
     * 排序
     */
    @ApiModelProperty(value = "排序 ")
    @NotNull(message = "请填写排序")
    private Integer sortValue;
    /**
     * 系统赠送初始抽奖机会数(如注册即送)
     */
    @ApiModelProperty(value = "系统赠送初始抽奖机会数(如注册即送)")
    @NotNull(message = "请填写系统赠送初始抽奖机会数(如注册即送)")
    private Integer initialChances;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Size(max = 200, message = "备注长度不能超过{max}")
    private String remarks;


    /**
     * 活动商品
     */
    @ApiModelProperty(value = "请选择活动商品")
    private List<LotteryActivityPrizeSaveVO> prizeList;


    @ApiModelProperty(value = "活动批次")
    private List<String> batchList;


}
