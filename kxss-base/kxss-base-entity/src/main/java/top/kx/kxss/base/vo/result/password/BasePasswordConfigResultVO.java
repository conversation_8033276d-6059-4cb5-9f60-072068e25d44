package top.kx.kxss.base.vo.result.password;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 密码验证配置结果VO
 * </p>
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Builder
@ApiModel(value = "BasePasswordConfigResultVO", description = "密码验证配置结果VO")
public class BasePasswordConfigResultVO {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 业务模块代码
     */
    @ApiModelProperty(value = "业务模块代码")
    private String businessCode;

    /**
     * 业务模块名称
     */
    @ApiModelProperty(value = "业务模块名称")
    private String businessName;

    /**
     * 是否启用密码验证
     */
    @ApiModelProperty(value = "是否启用密码验证")
    private Boolean isEnabled;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remarks;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedTime;
}