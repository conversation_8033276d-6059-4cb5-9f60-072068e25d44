package top.kx.kxss.base.vo.query.distributor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;


/**
 * <p>
 * 表单查询条件VO
 * 耗材经销商信息
 * </p>
 *
 * <AUTHOR>
 * @date 2025-03-07 15:45:07
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode
@Builder
@ApiModel(value = "BaseDistributorPageQuery", description = "耗材经销商信息")
public class BaseDistributorPageQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private Long id;

    /**
    * 姓名
    */
    @ApiModelProperty(value = "姓名")
    private String name;
    /**
    * 手机号
    */
    @ApiModelProperty(value = "手机号")
    private String mobile;
    /**
    * 经销商编号
    */
    @ApiModelProperty(value = "经销商编号")
    private String code;
    /**
     * 经销商层级
     */
    @ApiModelProperty(value = "经销商层级")
    private Integer level;
    /**
     * 上级ID
     */
    @ApiModelProperty(value = "上级ID")
    private Long parentId;

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private Long userId;
    /**
     * 性别;[1-男 2-女 3-未知]
     */
    @ApiModelProperty(value = "性别;[1-男 2-女 3-未知]")
    private String sex;
    /**
    * 店铺名称
    */
    @ApiModelProperty(value = "店铺名称")
    private String shopName;
    /**
    * 详细地址
    */
    @ApiModelProperty(value = "详细地址")
    private String address;
    /**
    * 区域-数组
    */
    @ApiModelProperty(value = "区域-数组")
    private String regions;
    /**
    * 备注
    */
    @ApiModelProperty(value = "备注")
    private String remarks;
    /**
    * 创建人组织
    */
    @ApiModelProperty(value = "创建人组织")
    private Long createdOrgId;


    @ApiModelProperty(value = "模糊搜索")
    private String keyword;


    /**
     * 经销商查询
     */
    @ApiModelProperty(value = "是否来源经销商查询")
    private Boolean isDistributor = false;



}
