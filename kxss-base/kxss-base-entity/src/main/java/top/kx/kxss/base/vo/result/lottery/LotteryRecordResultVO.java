package top.kx.kxss.base.vo.result.lottery;

import cn.hutool.core.map.MapUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.Entity;
import top.kx.basic.interfaces.echo.EchoVO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

/**
 * <p>
 * 表单查询方法返回值VO
 * 用户抽奖记录表
 * </p>
 *
 * <AUTHOR>
 * @date 2025-09-15 17:23:11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@ApiModel(value = "LotteryRecordResultVO", description = "用户抽奖记录表")
public class LotteryRecordResultVO extends Entity<Long> implements Serializable, EchoVO {

    private static final long serialVersionUID = 1L;

    private Map<String, Object> echoMap = MapUtil.newHashMap();

    @ApiModelProperty(value = "记录ID")
    private Long id;

    /**
    * 活动ID
    */
    @ApiModelProperty(value = "活动ID")
    private Long activityId;
    /**
    * 用户ID
    */
    @ApiModelProperty(value = "用户ID")
    private Long userId;
    /**
    * 抽奖机会ID
    */
    @ApiModelProperty(value = "抽奖机会ID")
    private Long chanceId;
    /**
    * 奖品库ID (引用原始奖品)
    */
    @ApiModelProperty(value = "奖品库ID (引用原始奖品)")
    private Long prizeId;
    /**
    * 奖品名称快照
    */
    @ApiModelProperty(value = "奖品名称快照")
    private String prizeName;
    /**
    * 奖品类型快照: 1=谢谢参与,2=积分,3=优惠券,4=代金券,5=实物
    */
    @ApiModelProperty(value = "奖品类型快照: 1=谢谢参与,2=积分,3=优惠券,4=代金券,5=实物")
    private String prizeType;
    /**
    * 奖品面额/积分数快照
    */
    @ApiModelProperty(value = "奖品面额/积分数快照")
    private BigDecimal valueAmount;
    /**
    * 使用门槛(金额门槛快照)
    */
    @ApiModelProperty(value = "使用门槛(金额门槛快照)")
    private BigDecimal threshold;
    /**
    * 优惠券编码快照
    */
    @ApiModelProperty(value = "优惠券编码快照")
    private String couponCode;
    /**
    * 状态: 0=未中奖,1=已中奖
    */
    @ApiModelProperty(value = "状态: 0=未中奖,1=已中奖")
    private String status;
    /**
    * 备注
    */
    @ApiModelProperty(value = "备注")
    private String remarks;



}
