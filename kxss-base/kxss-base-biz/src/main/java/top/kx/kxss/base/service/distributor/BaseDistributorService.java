package top.kx.kxss.base.service.distributor;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.base.entity.distributor.BaseDistributor;
import top.kx.kxss.base.vo.result.distributor.BaseDistributorTreeResultVO;
import top.kx.kxss.base.vo.save.distributor.BaseDistributorSaveVO;
import top.kx.kxss.base.vo.update.distributor.BaseDistributorUpdateVO;
import top.kx.kxss.base.vo.result.distributor.BaseDistributorResultVO;
import top.kx.kxss.base.vo.query.distributor.BaseDistributorPageQuery;

import java.util.List;


/**
 * <p>
 * 业务接口
 * 耗材经销商信息
 * </p>
 *
 * <AUTHOR>
 * @date 2025-03-07 15:45:07
 * @create [2025-03-07 15:45:07] [yan] [代码生成器生成]
 */
public interface BaseDistributorService extends SuperService<Long, BaseDistributor, BaseDistributorSaveVO,
    BaseDistributorUpdateVO, BaseDistributorPageQuery, BaseDistributorResultVO> {

    BaseDistributorResultVO getByUserId(Long userId);

    Boolean refreshUserId();

    List<BaseDistributorTreeResultVO> tree(BaseDistributorPageQuery pageQuery);

    /**
     * 有拿货单的不能删除
     * @param id
     * @return
     */
    Boolean remove(Long id);

}


