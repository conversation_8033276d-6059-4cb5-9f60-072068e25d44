package top.kx.kxss.base.service.purchase;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.base.entity.purchase.BasePurchase;
import top.kx.kxss.base.vo.query.purchase.BasePurchasePageQuery;
import top.kx.kxss.base.vo.result.distributor.BaseDistributorPurchaseResultVO;
import top.kx.kxss.base.vo.result.purchase.BasePurchaseResultVO;
import top.kx.kxss.base.vo.result.purchase.BasePurchaseSumResultVO;
import top.kx.kxss.base.vo.save.purchase.BasePurchaseSaveVO;
import top.kx.kxss.base.vo.update.purchase.BasePurchaseUpdateVO;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * <p>
 * 业务接口
 * 要货
 * </p>
 *
 * <AUTHOR>
 * @date 2025-03-10 14:57:47
 * @create [2025-03-10 14:57:47] [yan] [代码生成器生成]
 */
public interface BasePurchaseService extends SuperService<Long, BasePurchase, BasePurchaseSaveVO,
    BasePurchaseUpdateVO, BasePurchasePageQuery, BasePurchaseResultVO> {

    Boolean addNum(@NotNull Long id, @NotNull Integer num);

    Boolean reduceNum(@NotNull Long id, @NotNull Integer num);

    List<BaseDistributorPurchaseResultVO> purchaseSumList(List<Long> ids);

    BasePurchaseSumResultVO querySum(BasePurchasePageQuery query);

    Boolean refreshPurchase();

}


