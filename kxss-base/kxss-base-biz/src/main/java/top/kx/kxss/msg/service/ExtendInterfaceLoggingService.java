package top.kx.kxss.msg.service;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.msg.entity.ExtendInterfaceLogging;
import top.kx.kxss.msg.vo.query.ExtendInterfaceLoggingPageQuery;
import top.kx.kxss.msg.vo.result.ExtendInterfaceLoggingResultVO;
import top.kx.kxss.msg.vo.save.ExtendInterfaceLoggingSaveVO;
import top.kx.kxss.msg.vo.update.ExtendInterfaceLoggingUpdateVO;


/**
 * <p>
 * 业务接口
 * 接口执行日志记录
 * </p>
 *
 * <AUTHOR>
 * @date 2022-07-09 23:58:59
 * @create [2022-07-09 23:58:59] [zuihou] [代码生成器生成]
 */
public interface ExtendInterfaceLoggingService extends SuperService<Long, ExtendInterfaceLogging, ExtendInterfaceLoggingSaveVO,
        ExtendInterfaceLoggingUpdateVO, ExtendInterfaceLoggingPageQuery, ExtendInterfaceLoggingResultVO> {

}


