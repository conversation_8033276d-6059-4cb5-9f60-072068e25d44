package top.kx.kxss.base.manager.pact.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.base.entity.pact.BasePact;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.base.manager.pact.BasePactManager;
import top.kx.kxss.base.mapper.pact.BasePactMapper;

/**
 * <p>
 * 通用业务实现类
 * 协议配置
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-25 17:21:04
 * @create [2023-05-25 17:21:04] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BasePactManagerImpl extends SuperManagerImpl<BasePactMapper, BasePact> implements BasePactManager {

}


