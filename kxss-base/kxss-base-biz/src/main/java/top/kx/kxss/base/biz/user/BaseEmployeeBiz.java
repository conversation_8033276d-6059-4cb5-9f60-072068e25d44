package top.kx.kxss.base.biz.user;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.ImmutableMap;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.exception.BizException;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.CollHelper;
import top.kx.kxss.base.entity.user.BaseEmployee;
import top.kx.kxss.base.service.user.BaseEmployeeOrgRelService;
import top.kx.kxss.base.service.user.BaseEmployeeService;
import top.kx.kxss.base.vo.query.user.BaseEmployeePageQuery;
import top.kx.kxss.base.vo.result.user.BaseEmployeeResultVO;
import top.kx.kxss.base.vo.save.system.DefBindUserVO;
import top.kx.kxss.base.vo.save.user.BaseEmployeeSaveVO;
import top.kx.kxss.model.entity.system.SysUser;
import top.kx.kxss.model.enumeration.base.ActiveStatusEnum;
import top.kx.kxss.model.enumeration.base.EmployeeTypeEnum;
import top.kx.kxss.system.entity.tenant.DefUser;
import top.kx.kxss.system.service.tenant.DefUserService;
import top.kx.kxss.system.vo.query.tenant.DefUserPageQuery;
import top.kx.kxss.system.vo.save.tenant.DefUserSaveVO;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 员工大业务层
 *
 * <AUTHOR>
 * @date 2021/10/22 10:37
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BaseEmployeeBiz {
    private final BaseEmployeeService baseEmployeeService;
    private final BaseEmployeeOrgRelService baseEmployeeOrgRelService;
    private final DefUserService defUserService;

    /**
     * 保存员工信息
     *
     * @param saveVO saveVO
     * @return top.kx.kxss.base.entity.user.BaseEmployee
     * <AUTHOR>
     * @date 2022/10/28 12:15 AM
     * @create [2022/10/28 12:15 AM ] [tangyh] [初始创建]
     */
    @Transactional(rollbackFor = Exception.class)
    public BaseEmployee save(BaseEmployeeSaveVO saveVO) {
        boolean existDefUser = defUserService.checkMobile(saveVO.getMobile(), null);
        if (existDefUser) {
            throw new BizException("手机号已被注册,请重新输入手机号 或 直接邀请它加入贵公司。");
        }
        String username = StrUtil.isBlank(saveVO.getUsername()) ? IdUtil.simpleUUID() : saveVO.getUsername();
        // 保存 用户表 和 员工表
        DefUserSaveVO userSaveVO = DefUserSaveVO.builder().username(username).nickName(saveVO.getRealName()).build();
        BeanUtil.copyProperties(saveVO, userSaveVO);
        DefUser defUser = defUserService.save(userSaveVO);

        // 保存 基础库的员工表
        saveVO.setUserId(defUser.getId());
        saveVO.setActiveStatus(ActiveStatusEnum.ACTIVATED.getCode());
        saveVO.setIsDefault(true);
        saveVO.setType(EmployeeTypeEnum.Normal.getCode());
        return baseEmployeeService.save(saveVO);
    }

    @Transactional(rollbackFor = Exception.class)
    public BaseEmployee saveDistributor(BaseEmployeeSaveVO saveVO) {
        DefUser defUser = defUserService.getUserByMobile(saveVO.getMobile());
        if (ObjectUtil.isNotNull(defUser)) {
            BaseEmployee employeeByUser = baseEmployeeService.getEmployeeByUser(defUser.getId());
            ArgumentAssert.isNull(employeeByUser, "经销商手机号已存在，请重新输入手机号");
        }
        String username = StrUtil.isBlank(saveVO.getUsername()) || ObjectUtil.isNull(saveVO.getUsername())
                ? "a".concat(saveVO.getMobile()) : saveVO.getUsername();
        if (ObjectUtil.isNull(defUser)) {
            saveVO.setUsername(username);
            // 保存 用户表 和 员工表
            DefUserSaveVO userSaveVO = DefUserSaveVO.builder().username(username).nickName(saveVO.getRealName()).build();
            BeanUtil.copyProperties(saveVO, userSaveVO);
            defUser = defUserService.save(userSaveVO);
        }

        // 保存 基础库的员工表
        saveVO.setUserId(defUser.getId());
        saveVO.setActiveStatus(ActiveStatusEnum.ACTIVATED.getCode());
        saveVO.setIsDefault(true);
        saveVO.setType(EmployeeTypeEnum.Distributor.getCode());
        return baseEmployeeService.save(saveVO);
    }

    /**
     * 根据员工ID 查询员工、用户和他所在的机构 信息
     *
     * @param employeeId 员工ID
     * @return top.kx.kxss.base.vo.result.user.BaseEmployeeResultVO
     * <AUTHOR>
     * @date 2022/10/28 12:13 AM
     * @create [2022/10/28 12:13 AM ] [tangyh] [初始创建]
     */
    public BaseEmployeeResultVO getEmployeeUserById(Long employeeId) {
        // 租户库
        BaseEmployee employee = baseEmployeeService.getById(employeeId);
        if (employee == null) {
            return null;
        }
        // 员工信息
        BaseEmployeeResultVO resultVO = new BaseEmployeeResultVO();
        BeanUtil.copyProperties(employee, resultVO);

        // 机构信息
        resultVO.setOrgIdList(baseEmployeeOrgRelService.findOrgIdListByEmployeeId(employeeId));

        // 用户信息
        DefUser defUser = defUserService.getById(employee.getUserId());
        resultVO.setDefUser(BeanUtil.toBean(defUser, SysUser.class));

        return resultVO;
    }

    /**
     * 分页查员工数据
     *
     * @param params 参数
     * @return IPage
     * <AUTHOR>
     * @date 2022/10/28 12:19 AM
     * @create [2022/10/28 12:19 AM ] [tangyh] [初始创建]
     */
    public IPage<BaseEmployeeResultVO> findPageResultVO(PageParams<BaseEmployeePageQuery> params) {
        BaseEmployeePageQuery pageQuery = params.getModel();
        List<Long> userIdList;
        if (!StrUtil.isAllEmpty(pageQuery.getMobile(), pageQuery.getEmail(), pageQuery.getUsername(), pageQuery.getIdCard())) {
            userIdList = defUserService.findUserIdList(BeanUtil.toBean(pageQuery, DefUserPageQuery.class));
            if (CollUtil.isEmpty(userIdList)) {
                return new Page<>(params.getCurrent(), params.getSize());
            }

            params.getModel().setUserIdList(userIdList);
        }
        IPage<BaseEmployeeResultVO> pageResultVO = baseEmployeeService.findPageResultVO(params);

        if (CollUtil.isNotEmpty(pageResultVO.getRecords())) {
            List<Long> userIds = pageResultVO.getRecords().stream().map(BaseEmployeeResultVO::getUserId).collect(Collectors.toList());
            List<DefUser> defUsers = defUserService.listByIds(userIds);
            List<SysUser> userResultVos = BeanUtil.copyToList(defUsers, SysUser.class);
            ImmutableMap<Long, SysUser> map = CollHelper.uniqueIndex(userResultVos, SysUser::getId, user -> user);

            pageResultVO.getRecords().forEach(item -> item.setDefUser(map.get(item.getUserId())));
        }

        return pageResultVO;
    }

    @Transactional
    public Boolean invitationUser(DefBindUserVO param) {
        List<BaseEmployee> baseEmployeeList = findEmployeeList(param);
        return baseEmployeeService.saveBatch(baseEmployeeList);
    }

    /**
     * 将用户绑定某个租户的员工
     *
     * @param param param
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2022/10/28 12:21 AM
     * @create [2022/10/28 12:21 AM ] [tangyh] [初始创建]
     */
    @Transactional
    public Boolean bindUser(DefBindUserVO param) {
        List<BaseEmployee> baseEmployeeList = findEmployeeList(param);
        return baseEmployeeService.saveBatchBaseEmployeeAndRole(baseEmployeeList);
    }

    private List<BaseEmployee> findEmployeeList(DefBindUserVO param) {

        List<DefUser> defUsers = defUserService.listByIds(param.getUserIdList());
        ArgumentAssert.notEmpty(defUsers, "请选择用户");

        long employeeCount = baseEmployeeService.getSuperManager().count(Wraps.<BaseEmployee>lbQ().in(BaseEmployee::getUserId, param.getUserIdList()));
        ArgumentAssert.isFalse(employeeCount > 0, "对不起，您选择的用户已经是员工");

        return defUsers.stream().map(defUser -> {
            return BaseEmployee.builder().activeStatus(ActiveStatusEnum.ACTIVATED.getCode())
                    .positionStatus(ActiveStatusEnum.NOT_ACTIVE.getCode()).state(true)
                    .realName(defUser.getNickName() + "-" + defUser.getMobile()).userId(defUser.getId())
                    .isDefault(false).build();
        }).collect(Collectors.toList());
    }

    @Transactional
    public Boolean unInvitationUser(DefBindUserVO param) {
        List<Long> employeeIdList = findEmployeeIdList(param);
        return baseEmployeeService.removeByIds(employeeIdList);
    }

    private List<Long> findEmployeeIdList(DefBindUserVO param) {
        List<DefUser> defUsers = defUserService.listByIds(param.getUserIdList());
        ArgumentAssert.notEmpty(defUsers, "请选择用户");
        List<BaseEmployee> employeeList = baseEmployeeService.getSuperManager().list(Wraps.<BaseEmployee>lbQ().in(BaseEmployee::getUserId, param.getUserIdList()));
        ArgumentAssert.notEmpty(employeeList, "对不起，您选择的用户不是员工");
        return employeeList.stream().map(BaseEmployee::getId).collect(Collectors.toList());
    }

}
