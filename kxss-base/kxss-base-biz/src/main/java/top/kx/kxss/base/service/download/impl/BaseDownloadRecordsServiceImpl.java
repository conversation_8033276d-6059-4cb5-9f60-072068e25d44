package top.kx.kxss.base.service.download.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.kxss.base.service.download.BaseDownloadRecordsService;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.kxss.base.manager.download.BaseDownloadRecordsManager;
import top.kx.kxss.base.entity.download.BaseDownloadRecords;
import top.kx.kxss.base.vo.save.download.BaseDownloadRecordsSaveVO;
import top.kx.kxss.base.vo.update.download.BaseDownloadRecordsUpdateVO;
import top.kx.kxss.base.vo.result.download.BaseDownloadRecordsResultVO;
import top.kx.kxss.base.vo.query.download.BaseDownloadRecordsPageQuery;

/**
 * <p>
 * 业务实现类
 * 下载记录
 * </p>
 *
 * <AUTHOR>
 * @date 2025-04-25 17:45:12
 * @create [2025-04-25 17:45:12] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
@Transactional(readOnly = true)
public class BaseDownloadRecordsServiceImpl extends SuperServiceImpl<BaseDownloadRecordsManager, Long, BaseDownloadRecords, BaseDownloadRecordsSaveVO,
    BaseDownloadRecordsUpdateVO, BaseDownloadRecordsPageQuery, BaseDownloadRecordsResultVO> implements BaseDownloadRecordsService {


}


