package top.kx.kxss.base.service.user.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import top.kx.basic.base.entity.SuperEntity;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.base.entity.user.UserBackground;
import top.kx.kxss.base.manager.user.UserBackgroundManager;
import top.kx.kxss.base.service.user.UserBackgroundService;
import top.kx.kxss.base.vo.query.user.UserBackgroundPageQuery;
import top.kx.kxss.base.vo.result.user.UserBackgroundResultVO;
import top.kx.kxss.base.vo.save.user.UserBackgroundSaveVO;
import top.kx.kxss.base.vo.update.user.UserBackgroundUpdateVO;
import top.kx.kxss.file.service.FileService;
import top.kx.kxss.file.vo.param.FileUploadVO;
import top.kx.kxss.file.vo.result.FileResultVO;
import top.kx.kxss.model.enumeration.base.FileBizTypeEnum;

import java.util.Objects;

/**
 * <p>
 * 业务实现类
 * 用户背景图
 * </p>
 *
 * <AUTHOR>
 * @date 2025-04-19 16:25:46
 * @create [2025-04-19 16:25:46] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
//@Transactional(readOnly = true)
public class UserBackgroundServiceImpl extends SuperServiceImpl<UserBackgroundManager, Long, UserBackground, UserBackgroundSaveVO,
        UserBackgroundUpdateVO, UserBackgroundPageQuery, UserBackgroundResultVO> implements UserBackgroundService {

    private final FileService fileService;

    @Override
    public UserBackground upload(MultipartFile file, String appKey) {
        FileResultVO upload = fileService.upload(file, FileUploadVO.builder().bizType(FileBizTypeEnum.USER_BACKGROUND.getCode()).build());
        ArgumentAssert.notNull(upload, "上传失败");
        UserBackground userBackground = UserBackground.builder().userId(ContextUtil.getUserId()).appKey(appKey).fileId(upload.getId()).fileUrl(upload.getUrl()).build();
        superManager.save(userBackground);
        return userBackground;
    }

    @Override
    protected UserBackground saveBefore(UserBackgroundSaveVO userBackgroundSaveVO) {
        userBackgroundSaveVO.setUserId(ContextUtil.getUserId());
        return super.saveBefore(userBackgroundSaveVO);
    }

    @Override
    public UserBackgroundResultVO getOneByAppKey(String appKey) {
        UserBackground userBackground = superManager.getOne(Wraps.<UserBackground>lbQ().eq(UserBackground::getAppKey, appKey)
                .eq(UserBackground::getUserId, ContextUtil.getUserId()).orderByDesc(SuperEntity::getCreatedTime).last("limit 1"));
        if (Objects.isNull(userBackground)) {
            return null;
        }
        return BeanPlusUtil.toBean(userBackground, UserBackgroundResultVO.class);
    }
}


