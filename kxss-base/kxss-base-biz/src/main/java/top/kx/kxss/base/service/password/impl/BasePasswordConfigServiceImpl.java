package top.kx.kxss.base.service.password.impl;

import cn.hutool.crypto.SecureUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import top.kx.basic.base.entity.SuperEntity;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.kxss.base.entity.password.BasePasswordConfig;
import top.kx.kxss.base.manager.password.BasePasswordConfigManager;
import top.kx.kxss.base.service.password.BasePasswordConfigService;
import top.kx.kxss.base.vo.query.password.BasePasswordConfigPageQuery;
import top.kx.kxss.base.vo.result.password.BasePasswordConfigResultVO;
import top.kx.kxss.base.vo.save.password.BasePasswordConfigSaveVO;
import top.kx.kxss.base.vo.update.password.BasePasswordConfigUpdateVO;
import top.kx.kxss.base.vo.update.password.BasePasswordUpdateVO;
import top.kx.kxss.common.properties.SystemProperties;
import top.kx.kxss.model.enumeration.base.PasswordBusinessEnum;

import java.util.Objects;

/**
 * <p>
 * 密码验证配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BasePasswordConfigServiceImpl extends SuperServiceImpl<BasePasswordConfigManager, Long, BasePasswordConfig, BasePasswordConfigSaveVO,
        BasePasswordConfigUpdateVO, BasePasswordConfigPageQuery, BasePasswordConfigResultVO> implements BasePasswordConfigService {
    @Override
    public BasePasswordConfig getByBusinessCode(String businessCode) {
        if (!StringUtils.hasText(businessCode)) {
            return null;
        }
        return superManager.getOne(Wraps.<BasePasswordConfig>lbQ().eq(BasePasswordConfig::getBusinessCode, businessCode).orderByDesc(SuperEntity::getCreatedTime).last("limit 1"));
    }

    @Override
    public boolean validatePassword(String businessCode, String password) {
        if (!StringUtils.hasText(businessCode) || !StringUtils.hasText(password)) {
            return false;
        }
        
        BasePasswordConfig config = getByBusinessCode(businessCode);
        if (config == null || !Boolean.TRUE.equals(config.getIsEnabled())) {
            return false;
        }
        return SecureUtil.sha256(password + SystemProperties.PARAM_PASSWORD_SALT).equals(config.getPassword());
    }

    @Override
    public boolean needPasswordValidation(String businessCode) {
        if (!StringUtils.hasText(businessCode)) {
            return false;
        }
        
        BasePasswordConfig config = getByBusinessCode(businessCode);
        return config != null && config.getIsEnabled();
    }

    @Override
    public Boolean updatePassword(BasePasswordUpdateVO updateVO) {
        updateVO.setPassword(SecureUtil.sha256(updateVO.getPassword() + SystemProperties.PARAM_PASSWORD_SALT));
        return superManager.update(Wraps.<BasePasswordConfig>lbU().set(BasePasswordConfig::getPassword, updateVO.getPassword()).eq(BasePasswordConfig::getId, updateVO.getId()));
    }

    @Override
    protected BasePasswordConfig saveBefore(BasePasswordConfigSaveVO basePasswordConfigSaveVO) {
        basePasswordConfigSaveVO.setPassword(SecureUtil.sha256(basePasswordConfigSaveVO.getPassword() + SystemProperties.PARAM_PASSWORD_SALT));
        PasswordBusinessEnum businessEnum = PasswordBusinessEnum.get(basePasswordConfigSaveVO.getBusinessCode());
        if (Objects.nonNull(businessEnum)) {
            basePasswordConfigSaveVO.setBusinessName(businessEnum.getDesc());
        }
        return super.saveBefore(basePasswordConfigSaveVO);
    }

    public static void main(String[] args) {
        String s = SecureUtil.sha256("123456" + SystemProperties.PARAM_PASSWORD_SALT);
        System.out.println(s);
    }
}