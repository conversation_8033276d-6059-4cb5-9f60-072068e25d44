package top.kx.kxss.base.service.system;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.base.entity.system.BaseRoleResourceRel;
import top.kx.kxss.base.vo.query.system.BaseRoleResourceRelPageQuery;
import top.kx.kxss.base.vo.result.system.BaseRoleResourceRelResultVO;
import top.kx.kxss.base.vo.save.system.BaseRoleResourceRelSaveVO;
import top.kx.kxss.base.vo.update.system.BaseRoleResourceRelUpdateVO;

/**
 * <p>
 * 业务接口
 * 角色的资源
 * </p>
 *
 * <AUTHOR>
 * @date 2021-10-18
 */
public interface BaseRoleResourceRelService extends SuperService<Long, BaseRoleResourceRel, BaseRoleResourceRelSaveVO, BaseRoleResourceRelUpdateVO, BaseRoleResourceRelPageQuery, BaseRoleResourceRelResultVO> {
}
