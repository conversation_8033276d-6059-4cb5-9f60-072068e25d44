package top.kx.kxss.base.service.product.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.CodeUtils;
import top.kx.kxss.base.entity.product.BaseProduct;
import top.kx.kxss.base.entity.product.BaseProductAttribute;
import top.kx.kxss.base.manager.product.BaseProductManager;
import top.kx.kxss.base.service.product.BaseProductAttributeService;
import top.kx.kxss.base.service.product.BaseProductService;
import top.kx.kxss.base.vo.query.product.BaseProductAttributeQuery;
import top.kx.kxss.base.vo.query.product.BaseProductPageQuery;
import top.kx.kxss.base.vo.result.product.BaseProductResultVO;
import top.kx.kxss.base.vo.save.product.BaseProductSaveVO;
import top.kx.kxss.base.vo.update.product.BaseProductUpdateVO;
import top.kx.kxss.model.enumeration.base.CodeIdentifyEnum;

import java.io.Serializable;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 业务实现类
 * 商品表
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-25 13:52:40
 * @create [2023-05-25 13:52:40] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
//@Transactional(readOnly = true)
public class BaseProductServiceImpl extends SuperServiceImpl<BaseProductManager, Long, BaseProduct, BaseProductSaveVO,
    BaseProductUpdateVO, BaseProductPageQuery, BaseProductResultVO> implements BaseProductService {

    private final BaseProductAttributeService baseProductAttributeService;

    @Override
    public String getCode() {
        String code = CodeUtils.generateCode(CodeIdentifyEnum.PRODUCT.getCode(), true, CodeUtils.YYYY_MM_DD, 6);
        if (checkCode(code, null)) {
            return getCode();
        }
        return code;
    }

    @Override
    public Boolean checkCode(String code, Long id) {
        return superManager.count(Wraps.<BaseProduct>lbQ().ne(BaseProduct::getId, id).eq(BaseProduct::getCode, code)) > 0;
    }
    @Override
    public Map<Serializable, Object> findByIds(Set<Serializable> ids) {
        return superManager.findByIds(ids.stream().map(Convert::toLong).collect(Collectors.toSet()));
    }
    @Override
    protected BaseProduct saveBefore(BaseProductSaveVO baseProductSaveVO) {
        if (ObjectUtil.isNotNull(baseProductSaveVO.getProductImageFile())) {
            baseProductSaveVO.setProductImage(baseProductSaveVO.getProductImageFile().getId());
        }
        baseProductSaveVO.setCode(StrUtil.isBlank(baseProductSaveVO.getCode()) ? getCode() : baseProductSaveVO.getCode());
        return super.saveBefore(baseProductSaveVO);
    }

    @Override
    protected BaseProduct updateBefore(BaseProductUpdateVO baseProductUpdateVO) {
        baseProductUpdateVO.setProductImage(null);
        if (ObjectUtil.isNotNull(baseProductUpdateVO.getProductImageFile())) {
            baseProductUpdateVO.setProductImage(baseProductUpdateVO.getProductImageFile().getId());
        }
        return super.updateBefore(baseProductUpdateVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateState(Long id, Boolean state) {
        BaseProduct build = BaseProduct.builder().state(state).build();
        build.setId(id);
        return superManager.updateById(build);
    }

    @Override
    public Boolean relationAttribute(BaseProductAttributeQuery query) {
        ArgumentAssert.isTrue(ObjectUtil.isNotNull(query.getProductId()), "请选择需要绑定的商品");
        // 先删除绑定关系
        Boolean remove = baseProductAttributeService.remove(Wraps.<BaseProductAttribute>lbQ().eq(BaseProductAttribute::getProductId, query.getProductId()));
        // 再重新绑定
        if (ObjectUtil.isNotNull(query.getAttributeIds())) {
            baseProductAttributeService.saveBatch(query.getAttributeIds().stream().map(attributeId -> BaseProductAttribute.builder()
                    .productId(query.getProductId())
                    .attributeId(attributeId)
                    .createdOrgId(ContextUtil.getCurrentCompanyId())
                    .build()).collect(Collectors.toList()));
        }
        return remove;
    }
}


