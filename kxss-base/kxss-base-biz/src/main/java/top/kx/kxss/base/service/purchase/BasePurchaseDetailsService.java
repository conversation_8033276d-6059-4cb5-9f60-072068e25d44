package top.kx.kxss.base.service.purchase;

import com.baomidou.mybatisplus.core.metadata.IPage;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.base.service.SuperService;
import top.kx.kxss.base.entity.distributor.BaseDistributor;
import top.kx.kxss.base.entity.purchase.BasePurchaseDetails;
import top.kx.kxss.base.vo.query.purchase.BasePurchaseDetailsPageQuery;
import top.kx.kxss.base.vo.query.purchase.BasePurchaseItemPageQuery;
import top.kx.kxss.base.vo.result.purchase.BasePurchaseDetailsResultVO;
import top.kx.kxss.base.vo.result.purchase.BasePurchaseItemResultVO;
import top.kx.kxss.base.vo.save.purchase.BasePurchaseDetailsSaveVO;
import top.kx.kxss.base.vo.update.purchase.BasePurchaseDetailsUpdateVO;


/**
 * <p>
 * 业务接口
 * 要货详情
 * </p>
 *
 * <AUTHOR>
 * @date 2025-04-10 14:47:18
 * @create [2025-04-10 14:47:18] [yan] [代码生成器生成]
 */
public interface BasePurchaseDetailsService extends SuperService<Long, BasePurchaseDetails, BasePurchaseDetailsSaveVO,
    BasePurchaseDetailsUpdateVO, BasePurchaseDetailsPageQuery, BasePurchaseDetailsResultVO> {

    IPage<BasePurchaseItemResultVO> securityCodePurchasePage(PageParams<BasePurchaseItemPageQuery> query);

    Boolean checkBind(Long purchaseId, String securityCode, BaseDistributor baseDistributor);

    boolean checkDistributorPurchase(Long distributorId, String securityCode);
}


