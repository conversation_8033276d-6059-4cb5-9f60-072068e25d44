package top.kx.kxss.base.service.attribute;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.base.entity.attribute.BaseAttribute;
import top.kx.kxss.base.vo.save.attribute.BaseAttributeSaveVO;
import top.kx.kxss.base.vo.update.attribute.BaseAttributeUpdateVO;
import top.kx.kxss.base.vo.result.attribute.BaseAttributeResultVO;
import top.kx.kxss.base.vo.query.attribute.BaseAttributePageQuery;


/**
 * <p>
 * 业务接口
 * 商品基础属性
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-16 18:44:16
 * @create [2025-06-16 18:44:16] [yan] [代码生成器生成]
 */
public interface BaseAttributeService extends SuperService<Long, BaseAttribute, BaseAttributeSaveVO,
    BaseAttributeUpdateVO, BaseAttributePageQuery, BaseAttributeResultVO> {

}


