package top.kx.kxss.base.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.util.IOUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.exception.BizException;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.basic.utils.DateUtils;
import top.kx.basic.utils.StrPool;
import top.kx.kxss.base.entity.BaseSecurityCode;
import top.kx.kxss.base.entity.BaseSecurityCodeVerify;
import top.kx.kxss.base.entity.dealers.BaseDealers;
import top.kx.kxss.base.entity.member.MemberInfo;
import top.kx.kxss.base.entity.product.BaseProduct;
import top.kx.kxss.base.entity.user.BaseEmployee;
import top.kx.kxss.base.manager.BaseSecurityCodeManager;
import top.kx.kxss.base.manager.product.BaseProductManager;
import top.kx.kxss.base.service.BaseSecurityCodeAttributeService;
import top.kx.kxss.base.service.BaseSecurityCodeQZService;
import top.kx.kxss.base.service.BaseSecurityCodeVerifyService;
import top.kx.kxss.base.service.biz.BaseBizLogService;
import top.kx.kxss.base.service.dealers.BaseDealersService;
import top.kx.kxss.base.service.member.MemberInfoService;
import top.kx.kxss.base.service.user.BaseEmployeeService;
import top.kx.kxss.base.vo.query.*;
import top.kx.kxss.base.vo.result.*;
import top.kx.kxss.base.vo.result.member.RegisterConfirmQZResultVO;
import top.kx.kxss.base.vo.result.product.BaseProductResultVO;
import top.kx.kxss.base.vo.result.product.ProductResultVO;
import top.kx.kxss.base.vo.save.*;
import top.kx.kxss.base.vo.save.biz.BizLogSaveVO;
import top.kx.kxss.base.vo.update.BaseSecurityCodeUpdateVO;
import top.kx.kxss.file.entity.File;
import top.kx.kxss.file.enumeration.FileStorageType;
import top.kx.kxss.file.properties.FileServerProperties;
import top.kx.kxss.file.service.FileService;
import top.kx.kxss.file.utils.FileTypeUtil;
import top.kx.kxss.file.vo.param.FileUploadVO;
import top.kx.kxss.model.enumeration.base.SecurityCodeStatusEnum;
import top.kx.kxss.system.entity.system.DefClient;
import top.kx.kxss.system.entity.tenant.DefUser;
import top.kx.kxss.system.service.system.DefClientService;
import top.kx.kxss.system.service.tenant.DefUserService;
import top.kx.kxss.system.vo.save.tenant.DefUserSaveVO;

import javax.annotation.Resource;
import java.io.*;
import java.math.BigDecimal;
import java.nio.file.Paths;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static top.kx.basic.utils.DateUtils.DEFAULT_DATE_TIME_FORMAT;
import static top.kx.basic.utils.DateUtils.SLASH_DATE_FORMAT;

/**
 * <p>
 * 业务实现类
 * 防伪信息
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-25 13:40:18
 * @create [2023-05-25 13:40:18] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
@Transactional(readOnly = true)
public class BaseSecurityCodeQZServiceImpl extends SuperServiceImpl<BaseSecurityCodeManager, Long, BaseSecurityCode, BaseSecurityCodeSaveVO,
        BaseSecurityCodeUpdateVO, BaseSecurityCodePageQuery, BaseSecurityCodeResultVO> implements BaseSecurityCodeQZService {


    @Resource
    private BaseProductManager baseProductManager;
    @Resource
    private BaseDealersService dealersService;
    @Resource
    private DefUserService defUserService;
    @Resource
    private FileService fileService;
    @Resource
    private EchoService echoService;
    @Resource
    private BaseBizLogService bizLogService;
    @Resource
    private BaseEmployeeService employeeService;
    @Resource
    private MemberInfoService memberInfoService;

    private final FileServerProperties fileProperties;
    private final DefClientService defClientService;
    private final BaseSecurityCodeVerifyService baseSecurityCodeVerifyService;
    private final BaseSecurityCodeAttributeService baseSecurityCodeAttributeService;

    @Override
    public Boolean check(String code) {
        code = decrypt(code);
        BaseSecurityCode baseSecurityCode = superManager.getOne(Wraps.<BaseSecurityCode>lbQ().eq(BaseSecurityCode::getSecurityCode, code)
                .ne(BaseSecurityCode::getStatus, SecurityCodeStatusEnum.IMPORT.getCode())
                .ne(BaseSecurityCode::getStatus, SecurityCodeStatusEnum.DEL.getCode())
                .last("limit 1"));
        if (baseSecurityCode != null && baseSecurityCode.getProductId() != null) {
            BaseProduct baseProduct = baseProductManager.getById(baseSecurityCode.getProductId());
            ArgumentAssert.notNull(baseProduct, "商品不存在");
            DefClient defClient = defClientService.getById(baseProduct.getClientId());
            if (!ObjectUtil.equal(defClient.getClientId(), ContextUtil.getClientId())) {
                throw new BizException(1001, "防伪码不属于当前应用，请前往【" + defClient.getName() + "】进行查询");
            }
        }
        return baseSecurityCode != null;
    }

    @Override
    public String checkCode(String code) {
        code = decrypt(code);
        BaseSecurityCode baseSecurityCode = superManager.getOne(Wraps.<BaseSecurityCode>lbQ().eq(BaseSecurityCode::getSecurityCode, code)
                .ne(BaseSecurityCode::getStatus, SecurityCodeStatusEnum.IMPORT.getCode())
                .ne(BaseSecurityCode::getStatus, SecurityCodeStatusEnum.DEL.getCode()).last("limit 1"));
        if (baseSecurityCode != null) {
            if (baseSecurityCode.getProductId() != null) {
                BaseProduct baseProduct = baseProductManager.getById(baseSecurityCode.getProductId());
                ArgumentAssert.notNull(baseProduct, "商品不存在");
                DefClient defClient = defClientService.getById(baseProduct.getClientId());
                if (!ObjectUtil.equal(defClient.getClientId(), ContextUtil.getClientId())) {
                    throw new BizException(1001, "防伪码不属于当前应用，请前往【" + defClient.getName() + "】进行查询");
                }
            }
            return code;
        } else {
            return null;
        }
    }

    @Override
    public Boolean checkSecurityCode(String code, Long id) {
        ArgumentAssert.notEmpty(code, "请填写防伪编码");
        LbQueryWrap<BaseSecurityCode> wrap = Wraps.<BaseSecurityCode>lbQ()
                .eq(BaseSecurityCode::getSecurityCode, code).ne(BaseSecurityCode::getId, id);
        return superManager.count(wrap) > 0;
    }

    @Override
    public Boolean checkCode(String code, Long id) {
        ArgumentAssert.notEmpty(code, "请填写防伪序列号");
        LbQueryWrap<BaseSecurityCode> wrap = Wraps.<BaseSecurityCode>lbQ()
                .eq(BaseSecurityCode::getCode, code).ne(BaseSecurityCode::getId, id);
        return superManager.count(wrap) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseProductResultVO productBySecurityCode(String code, String type, String address, BigDecimal longitude, BigDecimal latitude) {
        code = decrypt(code);
        BaseSecurityCode securityCode = superManager.getOne(Wraps.<BaseSecurityCode>lbQ().eq(BaseSecurityCode::getSecurityCode, code));
        ArgumentAssert.notNull(securityCode, "您所识别非康溪防伪二维码，请谨慎购买！");
        ArgumentAssert.isFalse(ObjectUtil.equal(securityCode.getStatus(), SecurityCodeStatusEnum.IMPORT.getCode()), "您所识别非康溪防伪二维码，请谨慎购买！");
        ArgumentAssert.isFalse(ObjectUtil.equal(securityCode.getStatus(), SecurityCodeStatusEnum.DEL.getCode()), "您所识别非康溪防伪二维码，请谨慎购买！");
        BaseProduct product = baseProductManager.getOne(Wraps.<BaseProduct>lbQ().eq(BaseProduct::getId, securityCode.getProductId()));
        ArgumentAssert.notNull(product, "商品不存在！");
        DefClient defClient = defClientService.getById(product.getClientId());
        if (!ObjectUtil.equal(defClient.getClientId(), ContextUtil.getClientId())) {
            throw new BizException(1001, "防伪码不属于当前应用，请前往【" + defClient.getName() + "】进行查询");
        } //新增绑定日志
        bizLogService.saveLog(BizLogSaveVO.builder()
                .securityCode(securityCode).status(SecurityCodeStatusEnum.SELECT.getCode())
                .type(type).desc("查询序列号").build());
        //记录码的查询次数
        securityCode.setSelectNum(securityCode.getSelectNum() + 1);
        superManager.updateById(securityCode);
        BaseProductResultVO productResultVO = BeanUtil.copyProperties(product, BaseProductResultVO.class);
        //是否注册
        productResultVO.setIsRegister(ObjectUtil.equal(securityCode.getStatus(), SecurityCodeStatusEnum.REGISTER.getCode()));
        productResultVO.setRegisterTime(securityCode.getRegisterTime());

        if (ObjectUtil.isNotNull(productResultVO.getProductImage())) {
            productResultVO.setProductImageFile(fileService.getById(productResultVO.getProductImage()));
        }
        productResultVO.setSecurityCode(securityCode.getSecurityCode());
        productResultVO.setSelectNum(securityCode.getSelectNum());
        if (StringUtils.isNotBlank( address) && ObjectUtil.isNotEmpty(longitude) && ObjectUtil.isNotEmpty(latitude)) {
            BaseSecurityCodeVerify securityCodeVerify = baseSecurityCodeVerifyService.save(BaseSecurityCodeVerifySaveVO.builder()
                    .securityCode(code)
                    .verifyTime(LocalDateTime.now())
                    .verifyCount(securityCode.getSelectNum())
                    .address(address)
                    .longitude(longitude)
                    .latitude(latitude)
                    .userId(ContextUtil.getUserId())
                    .createdOrgId(ContextUtil.getUserId())
                    .build());
            BaseSecurityCodeVerify firstVerify = baseSecurityCodeVerifyService.getOne(Wraps.<BaseSecurityCodeVerify>lbQ()
                    .eq(BaseSecurityCodeVerify::getSecurityCode, code).orderByAsc(BaseSecurityCodeVerify::getVerifyTime).last("limit 1"));
            BaseSecurityCodeVerifyResultVO verifyResultVO = BeanPlusUtil.toBean(securityCodeVerify, BaseSecurityCodeVerifyResultVO.class);
            if (Objects.nonNull(firstVerify)) {
                verifyResultVO.setFirstVerifyTime(firstVerify.getVerifyTime());
            } else {
                verifyResultVO.setFirstVerifyTime(verifyResultVO.getVerifyTime());
            }
            productResultVO.setVerifyResultVO(verifyResultVO);
        }
        // 查询规格属性
        productResultVO.setAttributeResultVO(baseSecurityCodeAttributeService.getAttributeDetailList(securityCode.getId()));
        return productResultVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RegisterConfirmQZResultVO confirmInfo(String code) {
        BaseSecurityCode securityCode = superManager.getOne(Wraps.<BaseSecurityCode>lbQ().eq(BaseSecurityCode::getSecurityCode, code));
        ArgumentAssert.notNull(securityCode, "您所识别非康溪防伪二维码，请谨慎购买！");
        ArgumentAssert.isFalse(ObjectUtil.equal(securityCode.getStatus(), SecurityCodeStatusEnum.IMPORT.getCode())
                || ObjectUtil.equal(securityCode.getStatus(), SecurityCodeStatusEnum.DEL.getCode()), "您所识别非康溪防伪二维码，请谨慎购买！");
        ArgumentAssert.isFalse(ObjectUtil.equal(securityCode.getStatus(), SecurityCodeStatusEnum.REGISTER.getCode()), "已被注册，请勿重复注册");
        BaseProduct product = baseProductManager.getOne(Wraps.<BaseProduct>lbQ().eq(BaseProduct::getId, securityCode.getProductId()));
        ArgumentAssert.notNull(product, "您所识别非康溪防伪二维码，请谨慎购买！");
        RegisterConfirmQZResultVO build = RegisterConfirmQZResultVO.builder()
                .productName(product.getName()).securityCode(securityCode.getSecurityCode())
                .build();
        echoService.action(build);
        return build;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseSecurityCode registerSecurityCode(RegisterConfirmQZSaveVO saveVO) {
        BaseSecurityCode securityCode = superManager.getOne(Wraps.<BaseSecurityCode>lbQ().eq(BaseSecurityCode::getSecurityCode, saveVO.getSecurityCode())
        );
        ArgumentAssert.notNull(securityCode, "您所识别非康溪防伪二维码，请谨慎购买！");
        ArgumentAssert.isFalse(ObjectUtil.equal(securityCode.getStatus(), SecurityCodeStatusEnum.IMPORT.getCode())
                || ObjectUtil.equal(securityCode.getStatus(), SecurityCodeStatusEnum.DEL.getCode()), "防伪二维码未绑定商品或已删除！");
        ArgumentAssert.isFalse(ObjectUtil.equal(securityCode.getStatus(), SecurityCodeStatusEnum.REGISTER.getCode()), "已被注册，请勿重复注册");
        BaseProduct product = baseProductManager.getOne(Wraps.<BaseProduct>lbQ().eq(BaseProduct::getId, securityCode.getProductId()));
        ArgumentAssert.notNull(product, "您所识别非康溪防伪二维码，请谨慎购买！");
        //新增绑定日志
        bizLogService.saveLog(BizLogSaveVO.builder().securityCode(securityCode).type(saveVO.getType()).desc("查询序列号").build());
        //会员信息
        BaseDealers dealers = dealersService.getOne(Wraps.<BaseDealers>lbQ()
                .eq(BaseDealers::getDeleteFlag, 0)
                .eq(BaseDealers::getMobile, saveVO.getMobile())
                .eq(BaseDealers::getBallRoomName, saveVO.getBallRoomName())
                .orderByDesc(BaseDealers::getCreatedTime).last("limit 1")
        );
        if (dealers == null) {
            dealers = BeanUtil.copyProperties(saveVO, BaseDealers.class);
            dealers.setProductNum(0);
            dealers.setId(null);
        }
        BeanUtil.copyProperties(saveVO, dealers);
        DefUser defUser = defUserService.getUserByMobile(saveVO.getMobile());
        if (defUser == null) {
            DefUser save = defUserService.save(DefUserSaveVO.builder().sex(saveVO.getSex())
                    .state(true).username(saveVO.getMobile())
                    .nickName(saveVO.getName()).mobile(saveVO.getMobile())
                    .build());
            dealers.setUserId(save.getId());
        } else {
            dealers.setUserId(defUser.getId());
        }
        dealers.setProductNum(dealers.getProductNum() + 1);
        dealers.setType("1");
        dealersService.saveOrUpdate(dealers);
        MemberInfo memberInfo = memberInfoService.getByUserId(ContextUtil.getUserId());
        securityCode.setMemberId(memberInfo.getId());
        securityCode.setDealersId(dealers.getId());
        securityCode.setRegisterTime(LocalDateTime.now());
        securityCode.setStatus(SecurityCodeStatusEnum.REGISTER.getCode());
        ArgumentAssert.isFalse(!superManager.updateById(securityCode), "注册失败");
        return securityCode;
    }

    @Override
    public List<ProductResultVO> getProductByMemberId(Long memberId) {
        List<BaseSecurityCode> securityCodeList = superManager.list(Wraps.<BaseSecurityCode>lbQ()
                .eq(BaseSecurityCode::getStatus, SecurityCodeStatusEnum.REGISTER.getCode())
                .eq(BaseSecurityCode::getMemberId, memberId).orderByDesc(BaseSecurityCode::getRegisterTime));
        if (CollUtil.isEmpty(securityCodeList)) {
            return Lists.newArrayList();
        }
        //商品信息
        List<Long> productIds = securityCodeList.stream().map(BaseSecurityCode::getProductId).collect(Collectors.toList());
        Map<Long, BaseProduct> productMap = baseProductManager.list(Wraps.<BaseProduct>lbQ().in(BaseProduct::getId, productIds))
                .stream().collect(Collectors.toMap(BaseProduct::getId, k -> k));
        //图片信息
        List<Long> productImages = productMap.values().stream().map(BaseProduct::getProductImage).collect(Collectors.toList());
        Map<Long, File> fileMap = CollectionUtil.isNotEmpty(productImages) ? fileService.list(Wraps.<File>lbQ().in(File::getId, productImages))
                .stream().collect(Collectors.toMap(File::getId, k -> k)) : new HashMap<>();
        return securityCodeList.stream().map(v -> {
            ProductResultVO productResultVO = BeanUtil.copyProperties(v, ProductResultVO.class);
            BaseProduct baseProduct = productMap.get(v.getProductId());
            BeanUtil.copyProperties(baseProduct, productResultVO);
            productResultVO.setProductImageFile(fileMap.get(baseProduct.getProductImage()));
            return productResultVO;
        }).collect(Collectors.toList());
    }

    @Override
    public CodeCountStatisticsVO statistics() {
        //获取本周绑定数据
        LocalDate weekStar = DateUtils.getWeekStar();
        LocalDate weekEnd = DateUtils.getWeekEnd();
        //查询本周全部数据
        List<BaseSecurityCode> weekList = superManager.list(Wraps.<BaseSecurityCode>lbQ()
                .in(BaseSecurityCode::getStatus, Arrays.asList(SecurityCodeStatusEnum.BIND.getCode(), SecurityCodeStatusEnum.REGISTER.getCode()))
                .isNotNull(BaseSecurityCode::getBillDate).between(BaseSecurityCode::getBillDate,
                        DateUtils.getStartTime(DateUtils.format(weekStar, DateUtils.DEFAULT_DATE_FORMAT)),
                        DateUtils.getEndTime(DateUtils.format(weekEnd, DateUtils.DEFAULT_DATE_FORMAT))));
        List<BaseSecurityCode> scrapWeekList = superManager.list(Wraps.<BaseSecurityCode>lbQ()
                .eq(BaseSecurityCode::getStatus, SecurityCodeStatusEnum.DEL.getCode())
                .isNotNull(BaseSecurityCode::getScrapTime).between(BaseSecurityCode::getScrapTime,
                        DateUtils.getStartTime(DateUtils.format(weekStar, DateUtils.DEFAULT_DATE_FORMAT)),
                        DateUtils.getEndTime(DateUtils.format(weekEnd, DateUtils.DEFAULT_DATE_FORMAT))));
        //根据日期进行分组
        Map<String, List<BaseSecurityCode>> dateMap = weekList.stream().collect(Collectors.groupingBy(o -> DateUtils.format(o.getBillDate(), DateUtils.DEFAULT_DATE_FORMAT)));
        Map<String, List<BaseSecurityCode>> dateScrapMap = scrapWeekList.stream().collect(Collectors.groupingBy(o -> DateUtils.format(o.getScrapTime(), DateUtils.DEFAULT_DATE_FORMAT)));
        //获取今日数据
        List<BaseSecurityCode> todayList = CollUtil.isEmpty(dateMap) ? Lists.newArrayList() : dateMap.get(DateUtils.format(LocalDate.now(), DateUtils.DEFAULT_DATE_FORMAT));
        List<BaseSecurityCode> todayScrapList = CollUtil.isEmpty(dateScrapMap) ? Lists.newArrayList() : dateScrapMap.get(DateUtils.format(LocalDate.now(), DateUtils.DEFAULT_DATE_FORMAT));
        return CodeCountStatisticsVO.builder()
                .todayBind(CollUtil.isEmpty(todayList) ? 0L : todayList.size())
                .todayScrap(CollUtil.isEmpty(todayScrapList) ? 0L : todayScrapList.size())
                .weekScrap(CollUtil.isEmpty(scrapWeekList) ? 0L : scrapWeekList.size())
                .weekBind(CollUtil.isEmpty(weekList) ? 0L : weekList.size())
                .weekStar(DateUtils.format(weekStar, "M.d"))
                .weekEnd(DateUtils.format(weekEnd, "M.d"))
                .build();
    }


    @Override
    public List<EmpSecurityBindResultVO> bindList(SecurityCodeBindQuery query) {
        LbQueryWrap<BaseSecurityCode> queryWrap = Wraps.<BaseSecurityCode>lbQ();
        queryWrap.isNotNull(BaseSecurityCode::getProductId)
                .in(BaseSecurityCode::getStatus, Arrays.asList(SecurityCodeStatusEnum.BIND.getCode(), SecurityCodeStatusEnum.REGISTER.getCode()))
                .between(BaseSecurityCode::getBillDate, DateUtils.getMonthStar(query.getMonth() + "-01"), DateUtils.getMonthEnd(query.getMonth() + "-01"))
        ;
        if (StrUtil.isNotBlank(query.getKeyword())) {
            List<Long> empIds = employeeService.list(Wraps.<BaseEmployee>lbQ().like(BaseEmployee::getRealName, query.getKeyword()))
                    .stream().map(BaseEmployee::getId).collect(Collectors.toList());
            queryWrap.and(wrap -> wrap.like(BaseSecurityCode::getSecurityCode, query.getKeyword())
                    .or()
                    .inSql(BaseSecurityCode::getProductId,
                            "select id from base_product where delete_flag = 0 and name like '%" + query.getKeyword() + "%'")
                    .or().in(CollUtil.isNotEmpty(empIds), BaseSecurityCode::getBindUser, empIds)
            );
        }
        queryWrap.orderByDesc(BaseSecurityCode::getBillDate);
        List<BaseSecurityCode> list = superManager.list(queryWrap);
        Map<String, List<BaseSecurityCode>> codeMap = list.stream().collect(Collectors.groupingBy(baseSecurityCode -> DateUtils.format(baseSecurityCode.getBillDate(), DateUtils.DEFAULT_DATE_FORMAT_EN)));
        List<String> keyList = codeMap.keySet().stream().sorted(Comparator.comparing(String::valueOf).reversed()).collect(Collectors.toList());
        return keyList.stream().map(billDate -> {
            //获取星期信息
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtils.DEFAULT_DATE_FORMAT_EN);
            LocalDate localDate = LocalDate.parse(billDate, formatter);
            DayOfWeek dayOfWeek = localDate.getDayOfWeek();
            //封装详情信息
            List<EmpSecurityCodeResultVO> bindList = codeMap.get(billDate).stream().map(v -> BeanUtil.copyProperties(v, EmpSecurityCodeResultVO.class)).collect(Collectors.toList());
            bindList.sort(Comparator.comparing(EmpSecurityCodeResultVO::getBillDate).reversed());
            echoService.action(bindList);
            return EmpSecurityBindResultVO.builder()
                    .month(DateUtils.format(localDate, "M月dd日")).week(DateUtils.getWeekStr(dayOfWeek.getValue()))
                    .count(bindList.size()).children(bindList).build();
        }).collect(Collectors.toList());
    }

    @Override
    public IPage<SecurityCodeBindResultVO> bindPageList(PageParams<SecurityCodeBindPageQuery> params) {
        params.setSort("");
        params.setOrder("");
        IPage<BaseSecurityCode> page = params.buildPage(BaseSecurityCode.class);
        SecurityCodeBindPageQuery query = params.getModel();
        BaseSecurityCode model = BeanUtil.toBean(query, BaseSecurityCode.class);
        LbQueryWrap<BaseSecurityCode> wraps = Wraps.lbq(model, params.getExtra(), BaseSecurityCode.class);
        wraps.eq(BaseSecurityCode::getStatus, SecurityCodeStatusEnum.BIND.getCode());
        wraps.eq(BaseSecurityCode::getDeleteFlag, 0);
        wraps.between(BaseSecurityCode::getBillDate,
                DateUtils.format(DateUtils.getStartTime(query.getStartDate()), DEFAULT_DATE_TIME_FORMAT),
                DateUtils.format(DateUtils.getEndTime(query.getEndDate()), DEFAULT_DATE_TIME_FORMAT));
        //关键字查询
        if (StrUtil.isNotBlank(query.getKeyword())) {
            List<Long> empIds = employeeService.list(Wraps.<BaseEmployee>lbQ().like(BaseEmployee::getRealName, query.getKeyword()))
                    .stream().map(BaseEmployee::getId).collect(Collectors.toList());
            wraps.and(wrap -> wrap.like(BaseSecurityCode::getSecurityCode, query.getKeyword())
                    .or()
                    .inSql(BaseSecurityCode::getProductId,
                            "select id from base_product where delete_flag = 0 and name like '%" + query.getKeyword() + "%'")
                    .or().in(CollUtil.isNotEmpty(empIds), BaseSecurityCode::getBindUser, empIds)
            );
        }
        wraps.orderByDesc(BaseSecurityCode::getBillDate);
        page(page, wraps);
        IPage<SecurityCodeBindResultVO> pageVO = BeanPlusUtil.toBeanPage(page, SecurityCodeBindResultVO.class);
        echoService.action(pageVO);
        return pageVO;
    }


    @Override
    public IPage<SecurityCodeScrapResultVO> scrapPageList(PageParams<SecurityCodeScrapQuery> params) {
        params.setSort("");
        params.setOrder("");
        IPage<BaseSecurityCode> page = params.buildPage(BaseSecurityCode.class);
        SecurityCodeScrapQuery query = params.getModel();
        BaseSecurityCode model = BeanUtil.toBean(query, BaseSecurityCode.class);
        LbQueryWrap<BaseSecurityCode> wraps = Wraps.lbq(model, params.getExtra(), BaseSecurityCode.class);
        wraps.eq(BaseSecurityCode::getStatus, SecurityCodeStatusEnum.DEL.getCode());
        wraps.eq(BaseSecurityCode::getDeleteFlag, 0);
        wraps.between(BaseSecurityCode::getScrapTime,
                DateUtils.format(DateUtils.getStartTime(query.getStartDate()), DEFAULT_DATE_TIME_FORMAT),
                DateUtils.format(DateUtils.getEndTime(query.getEndDate()), DEFAULT_DATE_TIME_FORMAT));
        //关键字查询
        if (StrUtil.isNotBlank(query.getKeyword())) {
            List<Long> empIds = employeeService.list(Wraps.<BaseEmployee>lbQ().like(BaseEmployee::getRealName, query.getKeyword()))
                    .stream().map(BaseEmployee::getId).collect(Collectors.toList());
            wraps.and(wrap -> wrap.like(BaseSecurityCode::getSecurityCode, query.getKeyword())
                    .or()
                    .inSql(BaseSecurityCode::getProductId,
                            "select id from base_product where delete_flag = 0 and name like '%" + query.getKeyword() + "%'")
                    .or().in(CollUtil.isNotEmpty(empIds), BaseSecurityCode::getScrapUser, empIds)
            );
        }
        wraps.orderByDesc(BaseSecurityCode::getScrapTime);
        page(page, wraps);
        IPage<SecurityCodeScrapResultVO> pageVO = BeanPlusUtil.toBeanPage(page, SecurityCodeScrapResultVO.class);
        echoService.action(pageVO);
        return pageVO;
    }

    @Override
    public IPage<SecurityCodePurchaseControlResultVO> purchaseControlPageList(PageParams<SecurityCodePurchaseControlQuery> params) {
        params.setSort("");
        params.setOrder("");
        IPage<BaseSecurityCode> page = params.buildPage(BaseSecurityCode.class);
        SecurityCodePurchaseControlQuery query = params.getModel();
        BaseSecurityCode model = BeanUtil.toBean(query, BaseSecurityCode.class);
        LbQueryWrap<BaseSecurityCode> wraps = Wraps.lbq(model, params.getExtra(), BaseSecurityCode.class);
        wraps.between(BaseSecurityCode::getPurchaseControlTime,
                DateUtils.format(DateUtils.getStartTime(query.getStartDate()), DEFAULT_DATE_TIME_FORMAT),
                DateUtils.format(DateUtils.getEndTime(query.getEndDate()), DEFAULT_DATE_TIME_FORMAT));
        wraps.eq(BaseSecurityCode::getDeleteFlag, 0);
        //关键字查询
        if (StrUtil.isNotBlank(query.getKeyword())) {
            List<Long> empIds = employeeService.list(Wraps.<BaseEmployee>lbQ().like(BaseEmployee::getRealName, query.getKeyword()))
                    .stream().map(BaseEmployee::getId).collect(Collectors.toList());
            wraps.and(wrap -> wrap.like(BaseSecurityCode::getSecurityCode, query.getKeyword())
                    .or()
                    .inSql(BaseSecurityCode::getProductId,
                            "select id from base_product where delete_flag = 0 and name like '%" + query.getKeyword() + "%'")
                    .or().in(CollUtil.isNotEmpty(empIds), BaseSecurityCode::getDistributorId, empIds)
            );
        }
        wraps.isNotNull(BaseSecurityCode::getDistributorId);
        wraps.orderByDesc(BaseSecurityCode::getPurchaseControlTime);
        page(page, wraps);
        IPage<SecurityCodePurchaseControlResultVO> pageVO = BeanPlusUtil.toBeanPage(page, SecurityCodePurchaseControlResultVO.class);
        echoService.action(pageVO);
        return pageVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseSecurityCode bindCode(SecurityCodeBindSaveVO model) {
        model.setSecurityCode(decrypt(model.getSecurityCode()));
        BaseProduct product = baseProductManager.getOne(Wraps.<BaseProduct>lbQ().eq(BaseProduct::getId, model.getProductId()));
        ArgumentAssert.notNull(product, "您所绑定商品不存在！");
        BaseSecurityCode securityCode = superManager.getOne(Wraps.<BaseSecurityCode>lbQ().eq(BaseSecurityCode::getSecurityCode, model.getSecurityCode()));
        ArgumentAssert.notNull(securityCode, "标签不存在！");
        ArgumentAssert.isFalse(ObjectUtil.equal(securityCode.getStatus(), SecurityCodeStatusEnum.BIND.getCode()), "标签已绑定，请勿重复操作！");
        ArgumentAssert.isFalse(ObjectUtil.equal(securityCode.getStatus(), SecurityCodeStatusEnum.DEL.getCode()), "标签已报废，无法绑定！");
        ArgumentAssert.isFalse(ObjectUtil.equal(securityCode.getStatus(), SecurityCodeStatusEnum.REGISTER.getCode()), "标签已注册，无法再次绑定！！");
        //新增绑定日志
        bizLogService.saveLog(BizLogSaveVO.builder().securityCode(securityCode).type(model.getType()).desc("绑定序列号").build());
        //绑定信息
        securityCode.setStatus(SecurityCodeStatusEnum.BIND.getCode());
        securityCode.setBillDate(LocalDateTime.now());
        securityCode.setProductId(model.getProductId());
        securityCode.setSupplier(model.getSupplier());
        securityCode.setWareHouse(model.getWareHouse());
        securityCode.setBindUser(ContextUtil.getEmployeeId());
        securityCode.setEnterType(model.getType());
        superManager.updateById(securityCode);
        return securityCode;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseSecurityCode scrap(SecurityCodeScrapSaveVO model) {
        model.setSecurityCode(decrypt(model.getSecurityCode()));
        BaseSecurityCode securityCode = superManager.getOne(Wraps.<BaseSecurityCode>lbQ().eq(BaseSecurityCode::getSecurityCode, model.getSecurityCode()));
        ArgumentAssert.notNull(securityCode, "标签不存在！");
        ArgumentAssert.isFalse(ObjectUtil.equal(securityCode.getStatus(), SecurityCodeStatusEnum.BIND.getCode()), "标签已绑定，无法报废！");
        ArgumentAssert.isFalse(ObjectUtil.equal(securityCode.getStatus(), SecurityCodeStatusEnum.DEL.getCode()), "标签已报废，请勿重复操作！");
        ArgumentAssert.isFalse(ObjectUtil.equal(securityCode.getStatus(), SecurityCodeStatusEnum.REGISTER.getCode()), "标签已注册，无法报废！！");
        //新增报废日志
        bizLogService.saveLog(BizLogSaveVO.builder().securityCode(securityCode).type(model.getType()).desc("报废序列号").build());
        //报废信息
        securityCode.setStatus(SecurityCodeStatusEnum.DEL.getCode());
        securityCode.setScrapTime(LocalDateTime.now());
        securityCode.setScrapUser(ContextUtil.getEmployeeId());
        securityCode.setEnterType(model.getType());
        securityCode.setProductId(model.getProductId());
        securityCode.setWareHouse(model.getWareHouse());
        securityCode.setSupplier(model.getSupplier());
        securityCode.setScrapReason(model.getScrapReason());
        superManager.updateById(securityCode);
        return securityCode;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseSecurityCode purchaseControl(SecurityCodePurchaseControlSaveVO model) {
        model.setSecurityCode(decrypt(model.getSecurityCode()));
        BaseProduct product = baseProductManager.getOne(Wraps.<BaseProduct>lbQ().eq(BaseProduct::getId, model.getProductId()));
        ArgumentAssert.notNull(product, "您所绑定商品不存在！");
        BaseSecurityCode securityCode = superManager.getOne(Wraps.<BaseSecurityCode>lbQ().eq(BaseSecurityCode::getSecurityCode, model.getSecurityCode()));
        ArgumentAssert.notNull(securityCode, "标签不存在！");
        ArgumentAssert.notNull(securityCode.getProductId(), "标签未绑定商品！");
        ArgumentAssert.isFalse(!securityCode.getProductId().equals(model.getProductId()), "请检查当前商品是否和绑定商品一致！");
        ArgumentAssert.isNull(securityCode.getDistributorId(), "此序列号已绑定经销商！");
        //新增绑定日志
        bizLogService.saveLog(BizLogSaveVO.builder().securityCode(securityCode).type(model.getType()).desc("序列号绑定经销商").build());
        //信息
        securityCode.setPurchaseControlTime(LocalDateTime.now());
        securityCode.setDistributorId(model.getDistributorId());
        securityCode.setPurchaseControlBy(ContextUtil.getEmployeeId());
        securityCode.setPurchaseEnterType(model.getType());
        superManager.updateById(securityCode);
        return securityCode;
    }

    @Override
    public SecurityCodePurchaseControlResultVO purchaseDetail(String securityCode) {
        securityCode = decrypt(securityCode);
        BaseSecurityCode baseSecurityCode = superManager.getOne(Wraps.<BaseSecurityCode>lbQ()
                .isNotNull(BaseSecurityCode::getDistributorId)
                .eq(BaseSecurityCode::getSecurityCode, securityCode));
        ArgumentAssert.notNull(baseSecurityCode, "标签不存在！");
        SecurityCodePurchaseControlResultVO resultVO =
                BeanUtil.copyProperties(baseSecurityCode, SecurityCodePurchaseControlResultVO.class);
        if (ObjectUtil.isNotNull(resultVO) && ObjectUtil.isNotNull(resultVO.getDistributorId())) {
            BaseEmployee byId = employeeService.getById(resultVO.getDistributorId());
            if (ObjectUtil.isNotNull(byId)) {
                resultVO.setRemarks(byId.getRemarks());
                if (ObjectUtil.isNotNull(byId.getUserId())) {
                    resultVO.setRemarks(byId.getRemarks());
                    DefUser defUser = defUserService.getById(byId.getUserId());
                    if (ObjectUtil.isNotNull(defUser)) {
                        resultVO.setMobile(defUser.getMobile());
                    }
                }
            }
        }
        echoService.action(resultVO);
        return resultVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean del(String code) {
        BaseSecurityCode securityCode = superManager.getOne(Wraps.<BaseSecurityCode>lbQ().eq(BaseSecurityCode::getSecurityCode, code)
                .isNotNull(BaseSecurityCode::getProductId));
        ArgumentAssert.notNull(securityCode, "标签不存在！");
        ArgumentAssert.isFalse(ObjectUtil.isNotNull(securityCode.getMemberId()), "已被注册，请勿删除");
        //新增删除日志
        bizLogService.saveLog(BizLogSaveVO.builder().securityCode(securityCode).desc("删除序列号").build());
        securityCode.setBindUser(null);
        securityCode.setSupplier(null);
        securityCode.setProductId(null);
        securityCode.setEnterType(null);
        securityCode.setWareHouse(null);
        securityCode.setBillDate(null);
        securityCode.setStatus(SecurityCodeStatusEnum.IMPORT.getCode());
        return superManager.updateById(securityCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean delPurchase(String code) {
        BaseSecurityCode securityCode = superManager.getOne(Wraps.<BaseSecurityCode>lbQ().eq(BaseSecurityCode::getSecurityCode, code)
                .isNotNull(BaseSecurityCode::getProductId));
        ArgumentAssert.notNull(securityCode, "标签不存在！");
        //新增删除日志
        bizLogService.saveLog(BizLogSaveVO.builder().securityCode(securityCode).desc("删除经销商绑定序列号").build());
        securityCode.setPurchaseControlBy(null);
        securityCode.setPurchaseControlTime(null);
        securityCode.setDistributorId(null);
        return superManager.updateById(securityCode);
    }

    @Override
    public CodeStatisticsVO statisticsData(SecurityCodeStatisticsQuery query) {
        //防伪码信息
        List<BaseSecurityCode> securityCodes = Lists.newArrayList();
        if (query.getSelectType() == null || query.getSelectType() == 1) {
            //绑定数据
            securityCodes = superManager.list(Wraps.<BaseSecurityCode>lbQ()
                    .between(BaseSecurityCode::getBillDate, DateUtils.getStartTime(query.getStartDate()), DateUtils.getEndTime(query.getEndDate()))
                    .in(BaseSecurityCode::getStatus, Arrays.asList(SecurityCodeStatusEnum.BIND.getCode(), SecurityCodeStatusEnum.REGISTER.getCode()))
                    .isNotNull(BaseSecurityCode::getProductId).isNotNull(BaseSecurityCode::getBindUser)
                    .ne(BaseSecurityCode::getStatus, SecurityCodeStatusEnum.IMPORT.getCode())
                    .ne(BaseSecurityCode::getStatus, SecurityCodeStatusEnum.DEL.getCode()))
            ;
        } else if (query.getSelectType() == 2) {
            //报废数据
            securityCodes = superManager.list(Wraps.<BaseSecurityCode>lbQ()
                    .between(BaseSecurityCode::getScrapTime, DateUtils.getStartTime(query.getStartDate()), DateUtils.getEndTime(query.getEndDate()))
                    .isNotNull(BaseSecurityCode::getScrapUser).eq(BaseSecurityCode::getStatus, SecurityCodeStatusEnum.DEL.getCode()));
        } else if (query.getSelectType() == 3) {
            //注册数据
            securityCodes = superManager.list(Wraps.<BaseSecurityCode>lbQ()
                    .between(BaseSecurityCode::getRegisterTime, DateUtils.getStartTime(query.getStartDate()), DateUtils.getEndTime(query.getEndDate()))
                    .isNotNull(BaseSecurityCode::getMemberId).eq(BaseSecurityCode::getStatus, SecurityCodeStatusEnum.REGISTER.getCode()));
        } else {
            ArgumentAssert.isFalse(true, "查询类型不存在");
        }
        CodeStatisticsVO statisticsVO = new CodeStatisticsVO();
        statisticsVO.setTotalCount(CollUtil.isNotEmpty(securityCodes) ? securityCodes.size() : 0);
        //商品信息
        Map<Long, BaseProduct> productMap = baseProductManager.list(Wraps.<BaseProduct>lbQ().eq(BaseProduct::getState, true))
                .stream().collect(Collectors.toMap(BaseProduct::getId, k -> k));
        Map<Long, List<BaseSecurityCode>> productIdMap = securityCodes.stream().collect(Collectors.groupingBy(BaseSecurityCode::getProductId));
        //封装数据
        if (CollUtil.isNotEmpty(productMap)) {
            statisticsVO.setDataList(productMap.keySet().stream().filter(productIdMap::containsKey).map(v -> {
                List<BaseSecurityCode> securityCodeList = productIdMap.get(v);
                BaseProduct baseProduct = productMap.get(v);
                return StatisticsVO.builder().value(CollUtil.isNotEmpty(securityCodeList) ? securityCodeList.size() : 0)
                        .name(ObjectUtil.isNotNull(baseProduct) ? baseProduct.getName() : "").build();
            }).collect(Collectors.toList()));
        }
        return statisticsVO;
    }

    @Override
    public List<StatisticsVO> operateStatistics(SecurityCodeStatisticsQuery query) {
        //防伪码信息
        List<BaseSecurityCode> securityCodes = Lists.newArrayList();
        if (query.getSelectType() == null || query.getSelectType() == 1) {
            //绑定数据
            securityCodes = superManager.list(Wraps.<BaseSecurityCode>lbQ()
                    .between(BaseSecurityCode::getBillDate, DateUtils.getStartTime(query.getStartDate()), DateUtils.getEndTime(query.getEndDate()))
                    .in(BaseSecurityCode::getStatus, Arrays.asList(SecurityCodeStatusEnum.BIND.getCode(), SecurityCodeStatusEnum.REGISTER.getCode()))
                    .isNotNull(BaseSecurityCode::getProductId).isNotNull(BaseSecurityCode::getBindUser)
                    .ne(BaseSecurityCode::getStatus, SecurityCodeStatusEnum.IMPORT.getCode())
                    .ne(BaseSecurityCode::getStatus, SecurityCodeStatusEnum.DEL.getCode()));
        } else if (query.getSelectType() == 2) {
            //报废数据
            securityCodes = superManager.list(Wraps.<BaseSecurityCode>lbQ()
                    .between(BaseSecurityCode::getScrapTime, DateUtils.getStartTime(query.getStartDate()), DateUtils.getEndTime(query.getEndDate()))
                    .isNotNull(BaseSecurityCode::getScrapUser).eq(BaseSecurityCode::getStatus, SecurityCodeStatusEnum.DEL.getCode()));
        } else if (query.getSelectType() == 3) {
            //注册数据
            securityCodes = superManager.list(Wraps.<BaseSecurityCode>lbQ()
                    .between(BaseSecurityCode::getRegisterTime, DateUtils.getStartTime(query.getStartDate()), DateUtils.getEndTime(query.getEndDate()))
                    .isNotNull(BaseSecurityCode::getMemberId).eq(BaseSecurityCode::getStatus, SecurityCodeStatusEnum.REGISTER.getCode()));
        } else {
            ArgumentAssert.isFalse(true, "查询类型不存在");
        }
        //根据type进行分组
        Map<Long, List<BaseSecurityCode>> bindUserMap = securityCodes.stream()
                .collect(Collectors.groupingBy(ObjectUtil.isNull(query.getSelectType()) || query.getSelectType() == 1 ? BaseSecurityCode::getBindUser : (ObjectUtil.isNotNull(query.getSelectType()) && query.getSelectType() == 2) ? BaseSecurityCode::getScrapUser : BaseSecurityCode::getMemberId));
        if (CollUtil.isNotEmpty(bindUserMap)) {
            Map<Long, String> empUserMap;
            if (ObjectUtil.isNotNull(query.getSelectType()) && query.getSelectType() == 3) {
//                empUserMap = memberInfoService.list(Wraps.<MemberInfo>lbQ().in(MemberInfo::getId, bindUserMap.keySet())).stream().collect(Collectors.toMap(MemberInfo::getId, MemberInfo::getName));
                empUserMap = MapUtil.newHashMap();
            } else {
                empUserMap = employeeService.list(Wraps.<BaseEmployee>lbQ().in(BaseEmployee::getId, bindUserMap.keySet())).stream().collect(Collectors.toMap(BaseEmployee::getId, BaseEmployee::getRealName));
            }
            //进行数据分组
            return bindUserMap.keySet().stream().map(v -> {
                String name = empUserMap.get(v);
                List<BaseSecurityCode> securityCodeList = bindUserMap.get(v);
                return StatisticsVO.builder()
                        .name(StrUtil.isBlank(name) ? "" : name)
                        .value(CollUtil.isNotEmpty(securityCodeList) ? securityCodeList.size() : 0)
                        .build();
            }).collect(Collectors.toList()).stream().sorted(Comparator.comparing(StatisticsVO::getValue).reversed()).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SecurityCodeFileVO importCode(MultipartFile file) {
        StringBuilder stringBuilder = null;
        List<BaseSecurityCode> list = Lists.newArrayList();
        InputStreamReader streamReader = null;
        BufferedReader reader = null;
        try {
            fileService.upload(file, FileUploadVO.builder()
                    .bizType("SECURITY_CODE_IMPORT_ERROR")
                    .build());
            streamReader = new InputStreamReader(file.getInputStream());
            reader = new BufferedReader(streamReader);
            String line;
            stringBuilder = new StringBuilder();
            while ((line = reader.readLine()) != null) {
                if (line == "null" || line.isEmpty() || line == "") {
                    continue;
                }
                String[] split = line.split(",");
                if (split.length != 3) {
                    stringBuilder.append(line).append("----数据格式异常\r\n");
                    continue;
                }
                list.add(BaseSecurityCode.builder().code(split[0]).securityCode(split[1]).url(split[2]).selectNum(0).status(SecurityCodeStatusEnum.IMPORT.getCode()).build());
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            IOUtils.close(streamReader);
            IOUtils.close(reader);
        }
        //新增数据
        if (CollUtil.isNotEmpty(list)) {
            List<String> securityCodes = list.stream().map(BaseSecurityCode::getSecurityCode).collect(Collectors.toList());
            Map<String, BaseSecurityCode> securityCodeMap = CollUtil.isNotEmpty(securityCodes) ? superManager.list(Wraps.<BaseSecurityCode>lbQ()
                    .in(BaseSecurityCode::getSecurityCode, securityCodes)).stream().collect(Collectors.toMap(BaseSecurityCode::getSecurityCode, k -> k)) : new HashMap<>();
            for (String securityCode : securityCodeMap.keySet()) {
                BaseSecurityCode saveVO = list.stream().filter(baseSecurityCodeSaveVO -> ObjectUtil.equal(securityCode, baseSecurityCodeSaveVO.getSecurityCode())).findFirst().get();
                stringBuilder.append(saveVO.getCode()).append(",").append(saveVO.getSecurityCode()).append(",").append(saveVO.getUrl()).append("----数据已存在\n");
            }
            list = list.stream().filter(baseSecurityCodeSaveVO -> !securityCodeMap.keySet().contains(baseSecurityCodeSaveVO.getSecurityCode()))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(list)) {
                saveBatch(list);
            }
        }
        String str = String.valueOf(stringBuilder);
        if (str == "null" || StrUtil.isBlank(str)) {
            return SecurityCodeFileVO.builder().errFile(null).flag(true).build();
        }
        //异常信息 生成文件
        log.info("错误数据：{}", str);
        FileServerProperties.Local local = fileProperties.getLocal();
        // 相对路径
        String originalFileName = System.currentTimeMillis() + "_error.txt";
        String path = getPath("SECURITY_CODE_UPLOAD_ERROR", originalFileName);
        // web服务器存放的绝对路径
        String absolutePath = Paths.get(local.getStoragePath(), "", path).toString();
        BufferedWriter writer = null;
        try {
            java.io.File errFile = new java.io.File(absolutePath);
            if (!errFile.exists()) {
                errFile.getParentFile().mkdirs();
                errFile.createNewFile();
                if (errFile.mkdir()) {
                    System.out.println("Directory is created!");
                } else {
                    System.out.println("Failed to create directory!");
                }
            }
            writer = new BufferedWriter(new FileWriter(absolutePath));
            writer.write(str);
            writer.flush();
            writer.close();
            //上传错误文件;
            File file1 = fileService.save(File.builder()
                    .bizType("SECURITY_CODE_UPLOAD_ERROR").path(path).storageType(FileStorageType.LOCAL).url(local.getUrlPrefix() + "" + StrPool.SLASH + path)
                    .originalFileName(originalFileName)
                    .contentType(file.getContentType())
                    .size(file.getSize()).uniqueFileName(originalFileName)
                    .suffix(FilenameUtils.getExtension(originalFileName))
                    .fileType(FileTypeUtil.getFileType(file.getContentType()))
                    .build());
            return SecurityCodeFileVO.builder().errFile(file1).flag(false).build();
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            IOUtils.close(writer);
        }
    }

    /**
     * 企业/年/月/日/业务类型/唯一文件名
     */
    protected String getPath(String bizType, String uniqueFileName) {
        return new StringJoiner(StrPool.SLASH)
                .add(bizType).add(getDateFolder()).add(uniqueFileName).toString();
    }

    /**
     * 获取年月日 2020/09/01
     *
     * @return 日期文件夹
     */
    protected String getDateFolder() {
        return LocalDate.now().format(DateTimeFormatter.ofPattern(SLASH_DATE_FORMAT));
    }


    @Override
    public String decrypt(String code) {
        ArgumentAssert.isFalse(StrUtil.isBlank(code), "密文为空");
        try {
            log.info("防伪码：{}", code);
            if(code.startsWith("kxp")){
                return code;
            }
            if (code.startsWith("https://") || code.startsWith("http://")) {
                if (code.contains("=")) {
                    code = code.substring(code.lastIndexOf("=") + 1);
                } else {
                    code = code.substring(code.lastIndexOf("?") + 1);
                }
            }
            log.info("解析后防伪码：{}", code);
        } catch (Exception e) {
        }
        if (StrUtil.isBlank(code)) {
            code = "-9999";
        }
        return code;
    }

    public static String reverseEveryTwoChars(String s) {
        return IntStream.range(0, s.length() / 2)
                .mapToObj(i -> new StringBuilder(s.substring(i * 2, i * 2 + 2)).reverse().toString())
                .collect(Collectors.joining());
    }

//    public static void main(String[] args) {
//        String s = "3E6C442D20C3770EB0CC808FE31CC2315AFAD739".substring(0, 16);
//        System.out.println("反转前16位：" + s);
//        System.out.println("反转后16位：" + reverseEveryTwoChars(s));
//    }

    public static void main(String[] args) {
        String code = "https://fw.kxss147.com/fw/?60024641967940039437";
        code = code.substring(code.lastIndexOf("?") + 1);
        System.out.println(code);
    }
}


