package top.kx.kxss.base.service.purchase.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import top.kx.basic.base.entity.SuperEntity;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.base.entity.distributor.BaseDistributor;
import top.kx.kxss.base.entity.purchase.BasePurchaseDetails;
import top.kx.kxss.base.manager.purchase.BasePurchaseDetailsManager;
import top.kx.kxss.base.service.purchase.BasePurchaseDetailsService;
import top.kx.kxss.base.vo.query.purchase.BasePurchaseDetailsPageQuery;
import top.kx.kxss.base.vo.query.purchase.BasePurchaseItemPageQuery;
import top.kx.kxss.base.vo.result.purchase.BasePurchaseDetailsResultVO;
import top.kx.kxss.base.vo.result.purchase.BasePurchaseItemResultVO;
import top.kx.kxss.base.vo.save.purchase.BasePurchaseDetailsSaveVO;
import top.kx.kxss.base.vo.update.purchase.BasePurchaseDetailsUpdateVO;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 业务实现类
 * 要货详情
 * </p>
 *
 * <AUTHOR>
 * @date 2025-04-10 14:47:18
 * @create [2025-04-10 14:47:18] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
//@Transactional(readOnly = true)
public class BasePurchaseDetailsServiceImpl extends SuperServiceImpl<BasePurchaseDetailsManager, Long, BasePurchaseDetails, BasePurchaseDetailsSaveVO,
        BasePurchaseDetailsUpdateVO, BasePurchaseDetailsPageQuery, BasePurchaseDetailsResultVO> implements BasePurchaseDetailsService {

    private final EchoService echoService;


    @Override
    public IPage<BasePurchaseItemResultVO> securityCodePurchasePage(PageParams<BasePurchaseItemPageQuery> params) {
        params.setSort("");
        params.setOrder("");
        IPage<BasePurchaseDetails> page = params.buildPage(BasePurchaseDetails.class);
        BasePurchaseItemPageQuery query = params.getModel();
        LbQueryWrap<BasePurchaseDetails> queryWrap = Wraps.<BasePurchaseDetails>lbQ().eq(SuperEntity::getDeleteFlag, 0)
                .eq(StringUtils.isNotBlank(query.getSecurityCode()), BasePurchaseDetails::getSecurityCode, query.getSecurityCode())
                .eq(Objects.nonNull(query.getProductId()), BasePurchaseDetails::getProductId, query.getProductId()).groupBy(BasePurchaseDetails::getSecurityCode);
        IPage<BasePurchaseDetails> iPage = superManager.page(page, queryWrap);
        IPage<BasePurchaseItemResultVO> pageVO = BeanPlusUtil.toBeanPage(iPage, BasePurchaseItemResultVO.class);
        if (CollUtil.isNotEmpty(pageVO.getRecords())) {
            List<String> securityCodeList = pageVO.getRecords().stream().map(BasePurchaseItemResultVO::getSecurityCode).collect(Collectors.toList());
            List<BasePurchaseDetails> basePurchaseDetailsList = superManager.list(Wraps.<BasePurchaseDetails>lbQ().in(BasePurchaseDetails::getSecurityCode, securityCodeList).orderByAsc(SuperEntity::getCreatedTime));
            Map<String, List<BasePurchaseDetails>> basePurchaseDetailsMap = basePurchaseDetailsList.stream().collect(Collectors.groupingBy(BasePurchaseDetails::getSecurityCode));
            pageVO.getRecords().forEach(item -> {
                if (basePurchaseDetailsMap.containsKey(item.getSecurityCode())) {
                    item.setDistributorIds(basePurchaseDetailsMap.get(item.getSecurityCode()).stream().map(BasePurchaseDetails::getDistributorId).distinct().collect(Collectors.toList()));
                }
            });
        }
        echoService.action(pageVO);
        return pageVO;
    }

    @Override
    public Boolean checkBind(Long purchaseId, String securityCode, BaseDistributor baseDistributor) {
        LbQueryWrap<BasePurchaseDetails> queryWrap = Wraps.<BasePurchaseDetails>lbQ()
                //.eq(BasePurchaseDetails::getPurchaseId, purchaseId)
                .eq(BasePurchaseDetails::getSecurityCode, securityCode)
                .inSql(BasePurchaseDetails::getDistributorId, "select distinct id from base_distributor where delete_flag = 0 and level = " + baseDistributor.getLevel());
        return superManager.count(queryWrap) > 0;
    }
}


