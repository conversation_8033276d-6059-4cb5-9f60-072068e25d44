package top.kx.kxss.base.mapper.purchase;

import org.apache.ibatis.annotations.Param;
import top.kx.basic.base.mapper.SuperMapper;
import top.kx.kxss.base.entity.purchase.BasePurchase;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * Mapper 接口
 * 要货
 * </p>
 *
 * <AUTHOR>
 * @date 2025-03-10 14:57:47
 * @create [2025-03-10 14:57:47] [yan] [代码生成器生成]
 */
@Repository
public interface BasePurchaseMapper extends SuperMapper<BasePurchase> {

    int addNum(@Param("id") Long id, @Param("num") Integer num);

    int reduceNum(@Param("id") Long id, @Param("num") Integer num);

}


