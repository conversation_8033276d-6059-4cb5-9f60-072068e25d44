package top.kx.kxss.base.manager.system.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.base.manager.system.BaseRoleManager;
import top.kx.basic.base.manager.impl.SuperCacheManagerImpl;
import top.kx.basic.cache.redis2.CacheResult;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.model.cache.CacheKey;
import top.kx.basic.model.cache.CacheKeyBuilder;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.base.entity.system.BaseRole;
import top.kx.kxss.base.manager.user.BaseEmployeeOrgRelManager;
import top.kx.kxss.base.mapper.system.BaseRoleMapper;
import top.kx.kxss.base.mapper.system.BaseRoleResourceRelMapper;
import top.kx.kxss.common.cache.base.system.RoleCacheKeyBuilder;
import top.kx.kxss.common.cache.base.system.RoleResourceCacheKeyBuilder;
import top.kx.kxss.common.cache.base.user.EmployeeRoleCacheKeyBuilder;
import top.kx.kxss.common.cache.base.user.OrgRoleCacheKeyBuilder;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 通用业务实现类
 * 角色
 * </p>
 *
 * <AUTHOR>
 * @date 2021-10-18
 * @create [2021-10-18] [zuihou] [代码生成器生成]
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BaseRoleManagerImpl extends SuperCacheManagerImpl<BaseRoleMapper, BaseRole> implements BaseRoleManager {
    private final BaseRoleResourceRelMapper baseRoleResourceRelMapper;
    private final BaseEmployeeOrgRelManager baseEmployeeOrgRelManager;

    @Override
    protected CacheKeyBuilder cacheKeyBuilder() {
        return new RoleCacheKeyBuilder();
    }

    @Override
    public BaseRole getRoleByCode(String code) {
        ArgumentAssert.notEmpty(code, "请传入角色编码");
        return getOne(Wraps.<BaseRole>lbQ().eq(BaseRole::getCode, code));
    }

    @Override
    public List<Long> listEmployeeIdByRoleId(List<Long> roleIds) {
        return baseMapper.listEmployeeIdByRoleId(roleIds);
    }


    /**
     * 查询员工拥有的资源
     *
     * @param employeeId    员工ID
     * @param applicationId 应用ID
     * @return java.util.List<java.lang.Long>
     * <AUTHOR>
     * @date 2022/10/20 11:25 AM
     * @create [2022/10/20 11:25 AM ] [tangyh] [初始创建]
     */
    @Override
    public List<Long> findResourceIdByEmployeeId(Long applicationId, Long employeeId) {

        List<Long> roleIdList = findRoleIdByEmployeeId(employeeId);
        log.debug("roleIdList={}", roleIdList.size());

        if (CollUtil.isEmpty(roleIdList)) {
            return roleIdList;
        }

        // 查询角色拥有的资源
        Set<Long> resourceIdSet = new HashSet<>();
        for (Long roleId : roleIdList) {
            CacheKey roleResourceCacheKey = RoleResourceCacheKeyBuilder.build(applicationId, roleId);
            CacheResult<List<Long>> resourceIds = cacheOps.get(roleResourceCacheKey, k -> baseRoleResourceRelMapper.selectResourceIdByRoleId(applicationId, roleId));
            resourceIdSet.addAll(resourceIds.asList());
        }
        log.debug("resourceIdSet={}", resourceIdSet.size());

        return new ArrayList<>(resourceIdSet);
    }

    @Override
    public List<BaseRole> findRoleByEmployeeId(Long employeeId) {
        List<Long> roleIdList = findRoleIdByEmployeeId(employeeId);
        return findByIds(roleIdList, null).stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 查询员工拥有的角色
     *
     * @param employeeId employeeId
     * @return java.util.List<java.lang.Long>
     * <AUTHOR>
     * @date 2022/10/20 4:46 PM
     * @create [2022/10/20 4:46 PM ] [tangyh] [初始创建]
     */
    @Override
    public List<Long> findRoleIdByEmployeeId(Long employeeId) {
        // 员工 - 角色
        CacheKey erKey = EmployeeRoleCacheKeyBuilder.build(employeeId);
        CacheResult<List<Long>> roleIdList = cacheOps.get(erKey, k -> baseMapper.selectRoleByEmployeeId(employeeId));
        log.debug("roleIdList={}", roleIdList.asList().size());

        // 员工 - 机构
        List<Long> orgIdList = baseEmployeeOrgRelManager.findOrgIdByEmployeeId(employeeId);
        log.debug("orgIdList={}", orgIdList.size());

        // 机构 - 角色
        Set<Long> roleIdSet = new HashSet<>();
        for (Long orgId : orgIdList) {
            CacheKey orKey = OrgRoleCacheKeyBuilder.build(orgId);
            CacheResult<List<Long>> roleIds = cacheOps.get(orKey, k -> baseMapper.selectRoleIdByOrgId(orgId));
            roleIdSet.addAll(roleIds.asList());
        }

        roleIdSet.addAll(roleIdList.asList());
        return new ArrayList<>(roleIdSet);
    }

    @Override
    public boolean checkRole(Long employeeId, String... codes) {
        ArgumentAssert.notEmpty(codes, "请传递角色编码");

        List<Long> roleIds = findRoleIdByEmployeeId(employeeId);
        List<BaseRole> roleList = findByIds(roleIds, null);
        return roleList.stream()
                .filter(Objects::nonNull)
                .anyMatch(item -> item.getState() && ArrayUtil.contains(codes, item.getCode()));
    }

}
