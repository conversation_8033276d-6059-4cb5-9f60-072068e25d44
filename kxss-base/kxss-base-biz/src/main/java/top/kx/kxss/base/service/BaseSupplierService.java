package top.kx.kxss.base.service;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.base.entity.supplier.BaseSupplier;
import top.kx.kxss.base.vo.save.supplier.BaseSupplierSaveVO;
import top.kx.kxss.base.vo.update.supplier.BaseSupplierUpdateVO;
import top.kx.kxss.base.vo.result.supplier.BaseSupplierResultVO;
import top.kx.kxss.base.vo.query.supplier.BaseSupplierPageQuery;


/**
 * <p>
 * 业务接口
 * 供应商信息信息
 * </p>
 *
 * <AUTHOR>
 * @date 2025-02-28 16:36:35
 * @create [2025-02-28 16:36:35] [yan] [代码生成器生成]
 */
public interface BaseSupplierService extends SuperService<Long, BaseSupplier, BaseSupplierSaveVO,
    BaseSupplierUpdateVO, BaseSupplierPageQuery, BaseSupplierResultVO> {

}


