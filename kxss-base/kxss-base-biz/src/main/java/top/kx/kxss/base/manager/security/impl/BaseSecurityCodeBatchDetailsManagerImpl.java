package top.kx.kxss.base.manager.security.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.base.entity.security.BaseSecurityCodeBatchDetails;
import top.kx.kxss.base.manager.security.BaseSecurityCodeBatchDetailsManager;
import top.kx.kxss.base.mapper.security.BaseSecurityCodeBatchDetailsMapper;
import top.kx.kxss.base.vo.result.security.BaseSecurityCodeBatchDetailsResultVO;

import java.util.List;

/**
 * <p>
 * 通用业务实现类
 * 防伪码批次-详情
 * </p>
 *
 * <AUTHOR>
 * @date 2025-04-25 17:45:12
 * @create [2025-04-25 17:45:12] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BaseSecurityCodeBatchDetailsManagerImpl extends SuperManagerImpl<BaseSecurityCodeBatchDetailsMapper, BaseSecurityCodeBatchDetails> implements BaseSecurityCodeBatchDetailsManager {

    @Override
    public List<String> getRepeatCode(Long batchId) {
        return baseMapper.getRepeatCode(batchId);
    }

    @Override
    public List<String> getBigRepeatCode(Long batchId) {
        return baseMapper.getBigRepeatCode(batchId);
    }

    @Override
    public List<BaseSecurityCodeBatchDetailsResultVO> getAllBigCode(Long batchId) {
        return baseMapper.getAllBigCode(batchId);
    }
}


