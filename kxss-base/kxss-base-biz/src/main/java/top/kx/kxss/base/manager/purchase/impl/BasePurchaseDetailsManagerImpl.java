package top.kx.kxss.base.manager.purchase.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.base.entity.purchase.BasePurchaseDetails;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.base.manager.purchase.BasePurchaseDetailsManager;
import top.kx.kxss.base.mapper.purchase.BasePurchaseDetailsMapper;

/**
 * <p>
 * 通用业务实现类
 * 要货详情
 * </p>
 *
 * <AUTHOR>
 * @date 2025-04-10 14:47:18
 * @create [2025-04-10 14:47:18] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BasePurchaseDetailsManagerImpl extends SuperManagerImpl<BasePurchaseDetailsMapper, BasePurchaseDetails> implements BasePurchaseDetailsManager {

}


