package top.kx.kxss.base.manager.customers.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.base.entity.customers.BaseCustomers;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.base.manager.customers.BaseCustomersManager;
import top.kx.kxss.base.mapper.customers.BaseCustomersMapper;

/**
 * <p>
 * 通用业务实现类
 * 客户咨询信息
 * </p>
 *
 * <AUTHOR>
 * @date 2024-12-19 10:30:39
 * @create [2024-12-19 10:30:39] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BaseCustomersManagerImpl extends SuperManagerImpl<BaseCustomersMapper, BaseCustomers> implements BaseCustomersManager {

}


