package top.kx.kxss.base.manager.password.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.base.entity.password.BasePasswordConfig;
import top.kx.kxss.base.manager.password.BasePasswordConfigManager;
import top.kx.kxss.base.mapper.password.BasePasswordConfigMapper;

/**
 * <p>
 * 通用业务实现类
 * 密码验证配置表
 * </p>
 *
 * <AUTHOR>
 * @date 2025-09-12 11:17:53
 * @create [2025-09-12 11:17:53] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BasePasswordConfigManagerImpl extends SuperManagerImpl<BasePasswordConfigMapper, BasePasswordConfig> implements BasePasswordConfigManager {

}


