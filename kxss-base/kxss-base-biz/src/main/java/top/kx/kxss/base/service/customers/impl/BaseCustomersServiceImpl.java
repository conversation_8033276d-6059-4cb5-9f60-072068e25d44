package top.kx.kxss.base.service.customers.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.base.entity.customers.BaseCustomers;
import top.kx.kxss.base.manager.customers.BaseCustomersManager;
import top.kx.kxss.base.service.customers.BaseCustomersService;
import top.kx.kxss.base.vo.query.customers.BaseCustomersPageQuery;
import top.kx.kxss.base.vo.result.customers.BaseCustomersResultVO;
import top.kx.kxss.base.vo.save.customers.BaseCustomersSaveVO;
import top.kx.kxss.base.vo.update.customers.BaseCustomersUpdateVO;

import java.time.LocalDateTime;

/**
 * <p>
 * 业务实现类
 * 客户咨询信息
 * </p>
 *
 * <AUTHOR>
 * @date 2024-12-19 10:30:39
 * @create [2024-12-19 10:30:39] [dou] [代码生成器生成]
 */

@Slf4j
@RequiredArgsConstructor
@Service
public class BaseCustomersServiceImpl extends SuperServiceImpl<BaseCustomersManager, Long, BaseCustomers, BaseCustomersSaveVO,
        BaseCustomersUpdateVO, BaseCustomersPageQuery, BaseCustomersResultVO> implements BaseCustomersService {


    @Override
    public Boolean receive(Long id) {
        BaseCustomers baseCustomers = superManager.getById(id);
        ArgumentAssert.notNull(baseCustomers, "客户咨询不存在！");
        ArgumentAssert.isNull(baseCustomers.getRecipients(), "此记录已被领取！");
        baseCustomers.setRecipients(ContextUtil.getEmployeeId());
        baseCustomers.setCollectionTime(LocalDateTime.now());
        return superManager.updateById(baseCustomers);
    }
}


