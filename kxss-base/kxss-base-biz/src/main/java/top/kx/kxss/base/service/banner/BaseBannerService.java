package top.kx.kxss.base.service.banner;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.base.entity.banner.BaseBanner;
import top.kx.kxss.base.vo.save.banner.BaseBannerSaveVO;
import top.kx.kxss.base.vo.update.banner.BaseBannerUpdateVO;
import top.kx.kxss.base.vo.result.banner.BaseBannerResultVO;
import top.kx.kxss.base.vo.query.banner.BaseBannerPageQuery;

import java.util.List;


/**
 * <p>
 * 业务接口
 * 轮播图
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-25 15:35:00
 * @create [2023-05-25 15:35:00] [dou] [代码生成器生成]
 */
public interface BaseBannerService extends SuperService<Long, BaseBanner, BaseBannerSaveVO,
    BaseBannerUpdateVO, BaseBannerPageQuery, BaseBannerResultVO> {

    /**
     * 根据类型获取轮播信息
     * @param type
     * @return
     */
    List<BaseBannerResultVO> getBannerListByType(String type);
    /**
     * 修改状态
     * @param id
     * @param state
     * @return
     */
    Boolean updateState(Long id, Boolean state);
}


