package top.kx.kxss.base.service.system.impl;

import cn.hutool.core.bean.BeanUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.kxss.base.service.system.BaseOperationLogService;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.kxss.base.entity.system.BaseOperationLog;
import top.kx.kxss.base.entity.system.BaseOperationLogExt;
import top.kx.kxss.base.manager.system.BaseOperationLogManager;
import top.kx.kxss.base.mapper.system.BaseOperationLogExtMapper;
import top.kx.kxss.base.vo.query.system.BaseOperationLogPageQuery;
import top.kx.kxss.base.vo.result.system.BaseOperationLogResultVO;
import top.kx.kxss.base.vo.save.system.BaseOperationLogSaveVO;
import top.kx.kxss.base.vo.update.system.BaseOperationLogUpdateVO;

import java.time.LocalDateTime;

/**
 * <p>
 * 业务实现类
 * 操作日志
 * </p>
 *
 * <AUTHOR>
 * @date 2021-11-08
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)

public class BaseOperationLogServiceImpl extends SuperServiceImpl<BaseOperationLogManager, Long, BaseOperationLog, BaseOperationLogSaveVO, BaseOperationLogUpdateVO, BaseOperationLogPageQuery, BaseOperationLogResultVO> implements BaseOperationLogService {

    private final BaseOperationLogExtMapper baseOperationLogExtMapper;

    @Override
    public BaseOperationLogResultVO getDetail(Long id) {
        BaseOperationLog operationLog = superManager.getById(id);
        BaseOperationLogExt ext = baseOperationLogExtMapper.selectById(id);

        BaseOperationLogResultVO result = BeanUtil.toBean(ext, BaseOperationLogResultVO.class);
        BeanUtil.copyProperties(operationLog, result);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean clearLog(LocalDateTime clearBeforeTime, Integer clearBeforeNum) {
        return superManager.clearLog(clearBeforeTime, clearBeforeNum) > 0;
    }

    @Override
    public BaseOperationLog save(BaseOperationLogSaveVO saveVO) {
        BaseOperationLogExt baseOperationLogExt = BeanUtil.toBean(saveVO, BaseOperationLogExt.class);
        baseOperationLogExtMapper.insert(baseOperationLogExt);
        saveVO.setId(baseOperationLogExt.getId());
        return super.save(saveVO);
    }
}
