package top.kx.kxss.base.manager.security.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.base.entity.security.BaseSecurityCodeBatch;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.base.manager.security.BaseSecurityCodeBatchManager;
import top.kx.kxss.base.mapper.security.BaseSecurityCodeBatchMapper;

/**
 * <p>
 * 通用业务实现类
 * 防伪码批次
 * </p>
 *
 * <AUTHOR>
 * @date 2025-04-25 17:45:12
 * @create [2025-04-25 17:45:12] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BaseSecurityCodeBatchManagerImpl extends SuperManagerImpl<BaseSecurityCodeBatchMapper, BaseSecurityCodeBatch> implements BaseSecurityCodeBatchManager {

}


