package top.kx.kxss.base.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.web.multipart.MultipartFile;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.base.service.SuperService;
import top.kx.kxss.base.entity.BaseSecurityCode;
import top.kx.kxss.base.vo.query.*;
import top.kx.kxss.base.vo.result.*;
import top.kx.kxss.base.vo.result.member.RegisterConfirmResultVO;
import top.kx.kxss.base.vo.result.product.BaseProductResultVO;
import top.kx.kxss.base.vo.result.product.ProductResultVO;
import top.kx.kxss.base.vo.save.*;
import top.kx.kxss.base.vo.save.purchase.BasePurchaseRemarksVO;
import top.kx.kxss.base.vo.update.BaseSecurityCodeUpdateVO;

import java.math.BigDecimal;
import java.util.List;


/**
 * <p>
 * 业务接口
 * 防伪信息
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-25 13:40:18
 * @create [2023-05-25 13:40:18] [dou] [代码生成器生成]
 */
public interface BaseSecurityCodeService extends SuperService<Long, BaseSecurityCode, BaseSecurityCodeSaveVO,
        BaseSecurityCodeUpdateVO, BaseSecurityCodePageQuery, BaseSecurityCodeResultVO> {

    /**
     * 验证防伪码是否存在
     *
     * @param code
     * @return
     */
    Boolean check(String code);

    /**
     * 验证防伪码是否存在
     *
     * @param code
     * @return
     */
    String checkCode(String code);
    /**
     * 检测防伪码是否存在
     *
     * @return
     */
    Boolean checkSecurityCode(String code, Long id);

    Boolean checkBigCode(String code, Long id);

    /**
     * 检测防伪序列号是否存在
     *
     * @return
     */
    Boolean checkCode(String code, Long id);

    /**
     * 返回查询的商品信息
     *
     * @param code
     * @return
     */
    BaseProductResultVO productBySecurityCode(String code, String type, String address, BigDecimal longitude, BigDecimal latitude);

    /**
     * 注册确认页信息
     *
     * @param code
     * @return
     */
    RegisterConfirmResultVO confirmInfo(String code);

    /**
     * 注册绑定防伪码
     *
     * @param saveVO
     * @return
     */
    BaseSecurityCode registerSecurityCode(RegisterConfirmSaveVO saveVO);

    /**
     * 根据会员获取商品信息
     *
     * @param id
     * @return
     */
    List<ProductResultVO> getProductByMemberId(Long id);

    /**
     * 数量统计
     *
     * @return
     */
    CodeCountStatisticsVO statistics();

    /**
     * 获取绑定list
     *
     * @param query
     * @return
     */
    List<EmpSecurityBindResultVO> bindList(SecurityCodeBindQuery query);

    /**
     * 标签绑定
     *
     * @param model
     * @return
     */
    BaseSecurityCode bindCode(SecurityCodeBindSaveVO model);

    /**
     * 标签报废
     *
     * @param model
     * @return
     */
    BaseSecurityCode scrap(SecurityCodeScrapSaveVO model);

    /**
     * 标签删除
     *
     * @param securityCode
     * @return
     */
    Boolean del(String securityCode);

    /**
     * 报废列表
     * @param query
     * @return
     */
    IPage<SecurityCodeScrapResultVO> scrapPageList(PageParams<SecurityCodeScrapQuery> query);

    /**
     * 数据统计
     * @param query
     * @return
     */
    CodeStatisticsVO statisticsData(SecurityCodeStatisticsQuery query);

    /**
     * 查询统计数据
     * @param query
     * @return
     */
    List<StatisticsVO> operateStatistics(SecurityCodeStatisticsQuery query);

    SecurityCodeFileVO importCode(MultipartFile file,String bizType);

    /**
     *防伪码密文换取明文
     * @param code
     * @return
     */
    String decrypt(String code);

    /**
     * 绑定列表分页查询
     * @param params
     * @return
     */
    IPage<SecurityCodeBindResultVO> bindPageList(PageParams<SecurityCodeBindPageQuery> params);

    IPage<SecurityCodePurchaseControlResultVO> purchaseControlPageList(PageParams<SecurityCodePurchaseControlQuery> params);

    IPage<SecurityCodePurchaseControlResultVO> purchaseItemPageList(PageParams<SecurityCodePurchaseQuery> params);

    BaseSecurityCode purchaseControl(SecurityCodePurchaseControlSaveVO model);

    List<BaseSecurityCode> purchaseControlNew(SecurityCodePurchaseControlSaveVO model);

    Boolean delPurchase(String securityCode, Long purchaseId);

    Boolean updatePurchaseRemarks(BasePurchaseRemarksVO remarksVO);

    Boolean removePurchaseControl(SecurityCodePurchaseControlRemoveVO removeVO);

    List<SecurityCodePurchaseControlResultVO> purchaseDetail(String securityCode);


//    BaseSecurityCodeResultVO queryOneContainAttribute(String productCategory, String securityCode);

    BaseSecurityCodeResultVO getOneBySecurityCode(String securityCode);


    BaseSecurityCodeResultVO getBaseAttributeBySecurityCode(String securityCode);


    BaseSecurityCodeResultVO getDetail(Long id);

    Boolean checkCodeBindProduct(String securityCode);

    BaseSecurityCodeResultVO securityCodeTrace(String securityCode);

//    Boolean removeAttribute(String code);

//    Boolean addAttribute(SecurityCodeAttributeUpdateVO attributeUpdateVO);

}


