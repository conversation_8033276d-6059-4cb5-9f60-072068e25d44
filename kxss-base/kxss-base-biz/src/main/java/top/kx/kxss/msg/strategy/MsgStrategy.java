package top.kx.kxss.msg.strategy;

import cn.hutool.core.util.StrUtil;
import top.kx.kxss.msg.strategy.domain.MsgParam;
import top.kx.kxss.msg.strategy.domain.MsgResult;
import top.kx.basic.jackson.JsonUtil;
import top.kx.basic.model.Kv;
import top.kx.kxss.common.utils.FreeMarkerUtil;
import top.kx.kxss.msg.entity.DefMsgTemplate;
import top.kx.kxss.msg.entity.ExtendMsg;
import top.kx.kxss.msg.glue.GlueFactory;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/7/10 0010 14:18
 */
public interface MsgStrategy {
    /**
     * 解析参数
     *
     * @param param param
     * @return java.util.Map<java.lang.String, java.lang.String>
     * <AUTHOR>
     * @date 2022/10/28 4:58 PM
     * @create [2022/10/28 4:58 PM ] [tangyh] [初始创建]
     */
    default Map<String, String> parseParam(String param) {
        Map<String, String> map = new LinkedHashMap<>();
        if (StrUtil.isNotEmpty(param)) {
            List<Kv> list = JsonUtil.parseArray(param, Kv.class);
            for (Kv kv : list) {
                map.put(kv.getKey(), kv.getValue());
            }
        }
        return map;
    }

    /**
     * 替换变量
     *
     * @param extendMsg         extendMsg
     * @param extendMsgTemplate extendMsgTemplate
     * @return top.kx.kxss.msg.strategy.domain.MsgResult
     * <AUTHOR>
     * @date 2022/10/28 4:58 PM
     * @create [2022/10/28 4:58 PM ] [tangyh] [初始创建]
     */
    default MsgResult replaceVariable(ExtendMsg extendMsg, DefMsgTemplate extendMsgTemplate) {
        String script = extendMsgTemplate.getScript();
        String templateContent = extendMsgTemplate.getContent();
        String templateTitle = extendMsgTemplate.getTitle();
        Map<String, Object> params = new LinkedHashMap<>();
        if (StrUtil.isNotEmpty(extendMsg.getParam())) {
            List<Kv> list = JsonUtil.parseArray(extendMsg.getParam(), Kv.class);
            for (Kv kv : list) {
                params.put(kv.getKey(), kv.getValue());
            }
        }
        Map<String, Object> resultParams = params;
        String title = templateTitle;
        String content = templateContent;
        if (StrUtil.isNotEmpty(script)) {
            resultParams = (Map<String, Object>) GlueFactory.getInstance().exeGroovyScript(script, params);
        }
        if (StrUtil.isNotEmpty(templateTitle)) {
            title = FreeMarkerUtil.generateString(templateTitle, resultParams);
        }
        if (StrUtil.isNotEmpty(templateContent)) {
            content = FreeMarkerUtil.generateString(templateContent, resultParams);
        }

        return MsgResult.builder().title(title).content(content).build();
    }

    /**
     * 执行发送
     *
     * @param msgParam msgParam
     * @return top.kx.kxss.msg.strategy.domain.MsgResult
     * @throws Exception 异常
     * <AUTHOR>
     * @date 2022/10/28 4:58 PM
     * @create [2022/10/28 4:58 PM ] [tangyh] [初始创建]
     */
    MsgResult exec(MsgParam msgParam) throws Exception;
}
