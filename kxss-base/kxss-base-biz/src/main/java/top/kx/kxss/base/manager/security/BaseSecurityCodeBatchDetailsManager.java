package top.kx.kxss.base.manager.security;

import top.kx.basic.base.manager.SuperManager;
import top.kx.kxss.base.entity.security.BaseSecurityCodeBatchDetails;
import top.kx.kxss.base.vo.result.security.BaseSecurityCodeBatchDetailsResultVO;

import java.util.List;

/**
 * <p>
 * 通用业务接口
 * 防伪码批次-详情
 * </p>
 *
 * <AUTHOR>
 * @date 2025-04-25 17:45:12
 * @create [2025-04-25 17:45:12] [yan] [代码生成器生成]
 */
public interface BaseSecurityCodeBatchDetailsManager extends SuperManager<BaseSecurityCodeBatchDetails> {

    /**
     * 获取重复的防伪码
     */
    List<String> getRepeatCode(Long batchId);
    /**
     * 获取重复的防伪码
     */
    List<String> getBigRepeatCode(Long batchId);


    List<BaseSecurityCodeBatchDetailsResultVO> getAllBigCode(Long batchId);

}


