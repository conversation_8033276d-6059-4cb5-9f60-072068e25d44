package top.kx.kxss.msg.service;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.msg.entity.ExtendInterfaceLog;
import top.kx.kxss.msg.vo.query.ExtendInterfaceLogPageQuery;
import top.kx.kxss.msg.vo.result.ExtendInterfaceLogResultVO;
import top.kx.kxss.msg.vo.save.ExtendInterfaceLogSaveVO;
import top.kx.kxss.msg.vo.update.ExtendInterfaceLogUpdateVO;


/**
 * <p>
 * 业务接口
 * 接口执行日志
 * </p>
 *
 * <AUTHOR>
 * @date 2022-07-09 23:58:59
 * @create [2022-07-09 23:58:59] [zuihou] [代码生成器生成]
 */
public interface ExtendInterfaceLogService extends SuperService<Long, ExtendInterfaceLog, ExtendInterfaceLogSaveVO,
        ExtendInterfaceLogUpdateVO, ExtendInterfaceLogPageQuery, ExtendInterfaceLogResultVO> {

}


