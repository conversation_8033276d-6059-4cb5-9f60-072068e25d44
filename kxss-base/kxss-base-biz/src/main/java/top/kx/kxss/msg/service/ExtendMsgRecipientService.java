package top.kx.kxss.msg.service;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.msg.entity.ExtendMsgRecipient;
import top.kx.kxss.msg.vo.query.ExtendMsgRecipientPageQuery;
import top.kx.kxss.msg.vo.result.ExtendMsgRecipientResultVO;
import top.kx.kxss.msg.vo.save.ExtendMsgRecipientSaveVO;
import top.kx.kxss.msg.vo.update.ExtendMsgRecipientUpdateVO;

import java.util.List;


/**
 * <p>
 * 业务接口
 * 消息接收人
 * </p>
 *
 * <AUTHOR>
 * @date 2022-07-10 11:41:17
 * @create [2022-07-10 11:41:17] [zuihou] [代码生成器生成]
 */
public interface ExtendMsgRecipientService extends SuperService<Long, ExtendMsgRecipient, ExtendMsgRecipientSaveVO,
        ExtendMsgRecipientUpdateVO, ExtendMsgRecipientPageQuery, ExtendMsgRecipientResultVO> {
    /**
     * 根据消息ID查询接收人
     *
     * @param id
     * @return
     */
    List<ExtendMsgRecipient> listByMsgId(Long id);
}


