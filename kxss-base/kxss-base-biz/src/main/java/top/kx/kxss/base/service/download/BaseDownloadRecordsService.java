package top.kx.kxss.base.service.download;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.base.entity.download.BaseDownloadRecords;
import top.kx.kxss.base.vo.save.download.BaseDownloadRecordsSaveVO;
import top.kx.kxss.base.vo.update.download.BaseDownloadRecordsUpdateVO;
import top.kx.kxss.base.vo.result.download.BaseDownloadRecordsResultVO;
import top.kx.kxss.base.vo.query.download.BaseDownloadRecordsPageQuery;


/**
 * <p>
 * 业务接口
 * 下载记录
 * </p>
 *
 * <AUTHOR>
 * @date 2025-04-25 17:45:12
 * @create [2025-04-25 17:45:12] [yan] [代码生成器生成]
 */
public interface BaseDownloadRecordsService extends SuperService<Long, BaseDownloadRecords, BaseDownloadRecordsSaveVO,
    BaseDownloadRecordsUpdateVO, BaseDownloadRecordsPageQuery, BaseDownloadRecordsResultVO> {

}


