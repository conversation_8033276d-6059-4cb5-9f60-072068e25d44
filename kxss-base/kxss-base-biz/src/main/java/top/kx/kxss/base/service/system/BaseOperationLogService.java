package top.kx.kxss.base.service.system;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.base.entity.system.BaseOperationLog;
import top.kx.kxss.base.vo.query.system.BaseOperationLogPageQuery;
import top.kx.kxss.base.vo.result.system.BaseOperationLogResultVO;
import top.kx.kxss.base.vo.save.system.BaseOperationLogSaveVO;
import top.kx.kxss.base.vo.update.system.BaseOperationLogUpdateVO;

import java.time.LocalDateTime;

/**
 * <p>
 * 业务接口
 * 操作日志
 * </p>
 *
 * <AUTHOR>
 * @date 2021-11-08
 */
public interface BaseOperationLogService extends SuperService<Long, BaseOperationLog, BaseOperationLogSaveVO, BaseOperationLogUpdateVO, BaseOperationLogPageQuery, BaseOperationLogResultVO> {
    /**
     * 清理日志
     *
     * @param clearBeforeTime 多久之前的
     * @param clearBeforeNum  多少条
     * @return 是否成功
     */
    boolean clearLog(LocalDateTime clearBeforeTime, Integer clearBeforeNum);


    /**
     * 根据id查询详情
     *
     * @param id id
     * @return top.kx.kxss.base.vo.result.system.BaseOperationLogResultVO
     * <AUTHOR>
     * @date 2022/10/13 10:31 AM
     * @create [2022/10/13 10:31 AM ] [tangyh] [初始创建]
     */
    BaseOperationLogResultVO getDetail(Long id);
}
