package top.kx.kxss.base.manager.download.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.base.entity.download.BaseDownloadRecords;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.base.manager.download.BaseDownloadRecordsManager;
import top.kx.kxss.base.mapper.download.BaseDownloadRecordsMapper;

/**
 * <p>
 * 通用业务实现类
 * 下载记录
 * </p>
 *
 * <AUTHOR>
 * @date 2025-04-25 17:45:12
 * @create [2025-04-25 17:45:12] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BaseDownloadRecordsManagerImpl extends SuperManagerImpl<BaseDownloadRecordsMapper, BaseDownloadRecords> implements BaseDownloadRecordsManager {

}


