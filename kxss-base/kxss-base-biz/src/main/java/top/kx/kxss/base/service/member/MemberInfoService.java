package top.kx.kxss.base.service.member;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.base.entity.member.MemberInfo;
import top.kx.kxss.base.vo.save.member.MemberInfoSaveVO;
import top.kx.kxss.base.vo.update.member.MemberInfoUpdateVO;
import top.kx.kxss.base.vo.result.member.MemberInfoResultVO;
import top.kx.kxss.base.vo.query.member.MemberInfoPageQuery;
import top.kx.kxss.base.vo.update.member.MemberUpdateVO;


/**
 * <p>
 * 业务接口
 * 会员信息
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-24 18:03:33
 * @create [2023-05-24 18:03:33] [dou] [代码生成器生成]
 */
public interface MemberInfoService extends SuperService<Long, MemberInfo, MemberInfoSaveVO,
    MemberInfoUpdateVO, MemberInfoPageQuery, MemberInfoResultVO> {

    /**
     * 根据用户查询会员
     * @param userId
     * @return
     */
    MemberInfo getByUserId(Long userId);

    /**
     * 获取会员编号
     * @return
     */
    String getCode();


    /**
     * 检查编码是否存在
     * @param code
     * @return
     */
    Boolean checkCode(String code,Long id);

    /**
     * 更新会员信息
     * @param updateVO
     * @return
     */
    Boolean updateMember(MemberUpdateVO updateVO);

}


