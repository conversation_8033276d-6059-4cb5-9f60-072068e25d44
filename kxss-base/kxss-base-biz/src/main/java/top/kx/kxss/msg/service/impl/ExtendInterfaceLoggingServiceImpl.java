package top.kx.kxss.msg.service.impl;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.kxss.msg.service.ExtendInterfaceLoggingService;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.kxss.msg.entity.ExtendInterfaceLogging;
import top.kx.kxss.msg.manager.ExtendInterfaceLoggingManager;
import top.kx.kxss.msg.vo.query.ExtendInterfaceLoggingPageQuery;
import top.kx.kxss.msg.vo.result.ExtendInterfaceLoggingResultVO;
import top.kx.kxss.msg.vo.save.ExtendInterfaceLoggingSaveVO;
import top.kx.kxss.msg.vo.update.ExtendInterfaceLoggingUpdateVO;

/**
 * <p>
 * 业务实现类
 * 接口执行日志记录
 * </p>
 *
 * <AUTHOR>
 * @date 2022-07-09 23:58:59
 * @create [2022-07-09 23:58:59] [zuihou] [代码生成器生成]
 */

@Slf4j
@RequiredArgsConstructor
@Service
@Transactional(readOnly = true)
public class ExtendInterfaceLoggingServiceImpl extends SuperServiceImpl<ExtendInterfaceLoggingManager, Long, ExtendInterfaceLogging, ExtendInterfaceLoggingSaveVO,
        ExtendInterfaceLoggingUpdateVO, ExtendInterfaceLoggingPageQuery, ExtendInterfaceLoggingResultVO> implements ExtendInterfaceLoggingService {


}


