package top.kx.kxss.base.manager.distributor;

import top.kx.basic.base.manager.SuperManager;
import top.kx.basic.interfaces.echo.LoadService;
import top.kx.kxss.base.entity.distributor.BaseDistributor;

/**
 * <p>
 * 通用业务接口
 * 耗材经销商信息
 * </p>
 *
 * <AUTHOR>
 * @date 2025-03-07 15:45:07
 * @create [2025-03-07 15:45:07] [yan] [代码生成器生成]
 */
public interface BaseDistributorManager extends SuperManager<BaseDistributor>, LoadService {

}


