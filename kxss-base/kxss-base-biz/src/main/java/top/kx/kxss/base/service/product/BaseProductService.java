package top.kx.kxss.base.service.product;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.base.entity.product.BaseProduct;
import top.kx.kxss.base.vo.query.product.BaseProductAttributeQuery;
import top.kx.kxss.base.vo.save.product.BaseProductSaveVO;
import top.kx.kxss.base.vo.update.product.BaseProductUpdateVO;
import top.kx.kxss.base.vo.result.product.BaseProductResultVO;
import top.kx.kxss.base.vo.query.product.BaseProductPageQuery;

import java.io.Serializable;
import java.util.Map;
import java.util.Set;


/**
 * <p>
 * 业务接口
 * 商品表
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-25 13:52:40
 * @create [2023-05-25 13:52:40] [dou] [代码生成器生成]
 */
public interface BaseProductService extends SuperService<Long, BaseProduct, BaseProductSaveVO,
    BaseProductUpdateVO, BaseProductPageQuery, BaseProductResultVO> {

    /**
     * 获取商品编号
     * @return
     */
    String getCode();
    /**
     * 检查商品编码
     *
     * @param code
     * @return
     */
    Boolean checkCode(String code, Long id);
    /**
     * 根据id查询待回显参数
     *
     * @param ids 唯一键（可能不是主键ID)
     * @return
     */
    Map<Serializable, Object> findByIds(Set<Serializable> ids);
    /**
     * 修改状态
     * @param id
     * @param state
     * @return
     */
    Boolean updateState(Long id, Boolean state);


    Boolean relationAttribute(BaseProductAttributeQuery query);
}


