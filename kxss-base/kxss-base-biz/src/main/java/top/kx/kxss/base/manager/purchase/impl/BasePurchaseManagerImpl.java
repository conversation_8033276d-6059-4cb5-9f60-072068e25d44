package top.kx.kxss.base.manager.purchase.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.base.entity.purchase.BasePurchase;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.base.manager.purchase.BasePurchaseManager;
import top.kx.kxss.base.mapper.purchase.BasePurchaseMapper;

/**
 * <p>
 * 通用业务实现类
 * 要货
 * </p>
 *
 * <AUTHOR>
 * @date 2025-03-10 14:57:47
 * @create [2025-03-10 14:57:47] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BasePurchaseManagerImpl extends SuperManagerImpl<BasePurchaseMapper, BasePurchase> implements BasePurchaseManager {

    private final BasePurchaseMapper basePurchaseMapper;

    @Override
    public Boolean addNum(Long id, Integer num) {
        return basePurchaseMapper.addNum(id, num) > 0;
    }

    @Override
    public Boolean reduceNum(Long id, Integer num) {
        return basePurchaseMapper.reduceNum(id, num) > 0;
    }
}


