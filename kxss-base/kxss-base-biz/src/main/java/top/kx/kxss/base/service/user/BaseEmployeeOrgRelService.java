package top.kx.kxss.base.service.user;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.base.entity.user.BaseEmployeeOrgRel;
import top.kx.kxss.base.vo.query.user.BaseEmployeeOrgRelPageQuery;
import top.kx.kxss.base.vo.result.user.BaseEmployeeOrgRelResultVO;
import top.kx.kxss.base.vo.save.user.BaseEmployeeOrgRelSaveVO;
import top.kx.kxss.base.vo.update.user.BaseEmployeeOrgRelUpdateVO;

import java.util.List;

/**
 * <p>
 * 业务接口
 * 员工所在部门
 * </p>
 *
 * <AUTHOR>
 * @date 2021-10-18
 */
public interface BaseEmployeeOrgRelService extends SuperService<Long, BaseEmployeeOrgRel, BaseEmployeeOrgRelSaveVO, BaseEmployeeOrgRelUpdateVO, BaseEmployeeOrgRelPageQuery, BaseEmployeeOrgRelResultVO> {
    /**
     * 根据员工id查询员工的机构id
     *
     * @param employeeId 员工id
     * @return
     */
    List<Long> findOrgIdListByEmployeeId(Long employeeId);
}
