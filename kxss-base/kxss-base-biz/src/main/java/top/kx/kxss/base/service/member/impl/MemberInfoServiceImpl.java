package top.kx.kxss.base.service.member.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.base.entity.member.MemberInfo;
import top.kx.kxss.base.manager.member.MemberInfoManager;
import top.kx.kxss.base.service.member.MemberInfoService;
import top.kx.kxss.base.vo.query.member.MemberInfoPageQuery;
import top.kx.kxss.base.vo.result.member.MemberInfoResultVO;
import top.kx.kxss.base.vo.save.member.MemberInfoSaveVO;
import top.kx.kxss.base.vo.update.member.MemberInfoUpdateVO;
import top.kx.kxss.base.vo.update.member.MemberUpdateVO;
import top.kx.kxss.model.enumeration.base.CodeIdentifyEnum;
import top.kx.kxss.system.service.tenant.DefUserService;
import top.kx.kxss.system.vo.update.tenant.DefUserUpdateVO;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <p>
 * 业务实现类
 * 会员信息
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-24 18:03:33
 * @create [2023-05-24 18:03:33] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
//@Transactional(readOnly = true)
public class MemberInfoServiceImpl extends SuperServiceImpl<MemberInfoManager, Long, MemberInfo, MemberInfoSaveVO,
        MemberInfoUpdateVO, MemberInfoPageQuery, MemberInfoResultVO> implements MemberInfoService {

    @Resource
    private DefUserService defUserService;

    @Override
    public MemberInfo getByUserId(Long userId) {
        return superManager.getOne(Wraps.<MemberInfo>lbQ().eq(MemberInfo::getUserId, userId));
    }

    @Override
    public String getCode() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String time = LocalDateTime.now().format(formatter);
        MemberInfo memberInfo = superManager.getOne(Wraps.<MemberInfo>lbQ().like(MemberInfo::getCode, time).orderByDesc(MemberInfo::getCode).last("limit 1"));
        if (memberInfo == null) {
            return CodeIdentifyEnum.MEMBER_CODE.getCode() + time + "000001";
        }
        long num = Long.parseLong(memberInfo.getCode().replaceAll(CodeIdentifyEnum.MEMBER_CODE.getCode(), "")) + 1;
        String code = String.format("%012d", num);
        code = CodeIdentifyEnum.MEMBER_CODE.getCode() + code;
        if (checkCode(code, null)) {
            return getCode();
        }
        return code;
    }

    @Override
    public Boolean checkCode(String code, Long id) {
        return superManager.count(Wraps.<MemberInfo>lbQ().ne(MemberInfo::getId, id).eq(MemberInfo::getCode, code)) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateMember(MemberUpdateVO updateVO) {
        if (ObjectUtil.isNull(updateVO.getId())) {
            MemberInfo byUserId = getByUserId(ContextUtil.getUserId());
            ArgumentAssert.notNull(byUserId, "参数异常id");
            updateVO.setId(byUserId.getId());
        }
        if (ObjectUtil.isNotNull(updateVO.getAvatarFile())) {
            updateVO.setAvatarId(updateVO.getAvatarFile().getId());
        }
        MemberInfoUpdateVO memberInfoUpdateVO = BeanUtil.copyProperties(updateVO, MemberInfoUpdateVO.class);
        //更新用户信息
        updateById(memberInfoUpdateVO);
        //更新用户信息
        DefUserUpdateVO defUserUpdateVO = DefUserUpdateVO.builder()
                .id(ContextUtil.getUserId())
                .sex(updateVO.getSex())
                .nickName(updateVO.getName())
                .build();
        defUserService.updateById(defUserUpdateVO);
        return true;
    }
}


