package top.kx.kxss.base.mapper.system;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import top.kx.basic.base.mapper.SuperMapper;
import top.kx.kxss.base.entity.system.BaseRoleResourceRel;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * 角色的资源
 * </p>
 *
 * <AUTHOR>
 * @date 2021-10-18
 */
@Repository
public interface BaseRoleResourceRelMapper extends SuperMapper<BaseRoleResourceRel> {

    /**
     * 根据角色id和角色类别，查询角色拥有的权限
     *
     * @param roleId   角色ID
     * @param category 角色类别
     * @return
     */
    List<BaseRoleResourceRel> findByRoleIdAndCategory(@Param("roleId") Long roleId, @Param("category") String category);


    /**
     * 根据应用id和角色id查询对应的资源ID
     *
     * @param applicationId applicationId
     * @param roleId        roleId
     * @return java.util.List<java.lang.Long>
     * <AUTHOR>
     * @date 2022/10/20 4:06 PM
     * @create [2022/10/20 4:06 PM ] [tangyh] [初始创建]
     */
    List<Long> selectResourceIdByRoleId(@Param("applicationId") Long applicationId, @Param("roleId") Long roleId);
}
