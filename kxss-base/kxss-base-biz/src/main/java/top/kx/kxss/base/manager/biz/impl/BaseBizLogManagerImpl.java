package top.kx.kxss.base.manager.biz.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.base.entity.biz.BaseBizLog;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.base.manager.biz.BaseBizLogManager;
import top.kx.kxss.base.mapper.biz.BaseBizLogMapper;

/**
 * <p>
 * 通用业务实现类
 * 防伪码操作日志
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-29 14:02:40
 * @create [2023-05-29 14:02:40] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BaseBizLogManagerImpl extends SuperManagerImpl<BaseBizLogMapper, BaseBizLog> implements BaseBizLogManager {

}


