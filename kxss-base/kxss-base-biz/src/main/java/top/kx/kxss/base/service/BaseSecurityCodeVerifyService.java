package top.kx.kxss.base.service;

import top.kx.basic.base.service.SuperService;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.base.entity.BaseSecurityCodeVerify;
import top.kx.kxss.base.vo.save.BaseSecurityCodeVerifySaveVO;
import top.kx.kxss.base.vo.update.BaseSecurityCodeVerifyUpdateVO;
import top.kx.kxss.base.vo.result.BaseSecurityCodeVerifyResultVO;
import top.kx.kxss.base.vo.query.BaseSecurityCodeVerifyPageQuery;


/**
 * <p>
 * 业务接口
 * 防伪验证信息
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-06 16:18:00
 * @create [2025-06-06 16:18:00] [yan] [代码生成器生成]
 */
public interface BaseSecurityCodeVerifyService extends SuperService<Long, BaseSecurityCodeVerify, BaseSecurityCodeVerifySaveVO,
    BaseSecurityCodeVerifyUpdateVO, BaseSecurityCodeVerifyPageQuery, BaseSecurityCodeVerifyResultVO> {

    BaseSecurityCodeVerify getOne(LbQueryWrap<BaseSecurityCodeVerify> wrap);
}


