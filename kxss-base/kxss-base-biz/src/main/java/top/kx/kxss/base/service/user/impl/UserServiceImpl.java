package top.kx.kxss.base.service.user.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.base.entity.distributor.BaseDistributor;
import top.kx.kxss.base.entity.user.BaseEmployee;
import top.kx.kxss.base.manager.distributor.BaseDistributorManager;
import top.kx.kxss.base.service.user.BaseEmployeeService;
import top.kx.kxss.base.service.user.UserService;
import top.kx.kxss.base.vo.update.user.EmployeeUpdateVO;
import top.kx.kxss.base.vo.update.user.UserPasswordUpdateVO;
import top.kx.kxss.base.vo.update.user.UserSelfUpdateVO;
import top.kx.kxss.system.service.tenant.DefUserService;
import top.kx.kxss.system.vo.update.tenant.DefUserBaseInfoUpdateVO;
import top.kx.kxss.system.vo.update.tenant.DefUserPasswordUpdateVO;

import java.util.Objects;

/**
 * <p>
 * 业务实现类
 * 员工
 * </p>
 *
 * <AUTHOR>
 * @date 2021-10-18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {

    private final DefUserService defUserService;
    private final BaseEmployeeService baseEmployeeService;
    private final BaseDistributorManager baseDistributorManager;

    @Override
    public Boolean updateSelf(UserSelfUpdateVO updateVO) {
        DefUserBaseInfoUpdateVO baseInfoUpdateVO = BeanPlusUtil.toBean(updateVO, DefUserBaseInfoUpdateVO.class);
        baseInfoUpdateVO.setId(ContextUtil.getUserId());
        Boolean baseInfo = defUserService.updateBaseInfo(baseInfoUpdateVO);
        BaseEmployee baseEmployee = baseEmployeeService.getById(ContextUtil.getEmployeeId());
        if (Objects.nonNull(baseEmployee)) {
            EmployeeUpdateVO updateEmp = BeanPlusUtil.toBean(updateVO, EmployeeUpdateVO.class);
            updateEmp.setRealName(updateVO.getNickName());
            if (Objects.nonNull(updateVO.getAvatarFile())) {
                baseEmployee.setAvatarId(updateVO.getAvatarFile().getId());
            }
            baseEmployeeService.updateEmployee(updateEmp);
        }
        BaseDistributor baseDistributor = baseDistributorManager.getOne(Wraps.<BaseDistributor>lbQ().eq(BaseDistributor::getUserId, ContextUtil.getUserId()).last("limit 1"));
        if (Objects.nonNull(baseDistributor)) {
            baseDistributor.setName(updateVO.getNickName());
            baseDistributor.setSex(updateVO.getSex());
            baseDistributorManager.updateById(baseDistributor);
        }
        return baseInfo;
    }

    @Override
    public Boolean updatePassword(UserPasswordUpdateVO updateVO) {
        return defUserService.updatePassword(DefUserPasswordUpdateVO.builder().id(ContextUtil.getUserId())
                .password(updateVO.getPassword())
                .oldPassword(updateVO.getOldPassword())
                .confirmPassword(updateVO.getOldPassword())
                .build());
    }


}
