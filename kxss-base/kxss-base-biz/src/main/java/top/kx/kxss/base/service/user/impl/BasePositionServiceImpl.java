package top.kx.kxss.base.service.user.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.base.entity.user.BasePosition;
import top.kx.kxss.base.manager.user.BasePositionManager;
import top.kx.kxss.base.service.user.BasePositionService;
import top.kx.kxss.base.vo.query.user.BasePositionPageQuery;
import top.kx.kxss.base.vo.result.user.BasePositionResultVO;
import top.kx.kxss.base.vo.save.user.BasePositionSaveVO;
import top.kx.kxss.base.vo.update.user.BasePositionUpdateVO;

import java.io.Serializable;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * <p>
 * 业务实现类
 * 岗位
 * </p>
 *
 * <AUTHOR>
 * @date 2021-10-18
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)

public class BasePositionServiceImpl extends SuperServiceImpl<BasePositionManager, Long, BasePosition, BasePositionSaveVO, BasePositionUpdateVO, BasePositionPageQuery, BasePositionResultVO> implements BasePositionService {
    @Override
    public Map<Serializable, Object> findByIds(Set<Serializable> ids) {
        return superManager.findByIds(ids.stream().map(Convert::toLong).collect(Collectors.toSet()));
    }

    @Override
    public boolean check(String name, Long id) {
        ArgumentAssert.notEmpty(name, "请填写名称");
        LbQueryWrap<BasePosition> wrap = Wraps.<BasePosition>lbQ()
                .eq(BasePosition::getName, name).ne(BasePosition::getId, id);
        return superManager.count(wrap) > 0;
    }

    @Override
    protected BasePosition saveBefore(BasePositionSaveVO saveVO) {
        ArgumentAssert.isFalse(check(saveVO.getName(), null), StrUtil.format("岗位[{}]已经存在", saveVO.getName()));
        return super.saveBefore(saveVO);
    }

    @Override
    protected BasePosition updateBefore(BasePositionUpdateVO updateVO) {
        ArgumentAssert.isFalse(check(updateVO.getName(), updateVO.getId()), StrUtil.format("岗位[{}]已经存在", updateVO.getName()));
        return super.updateBefore(updateVO);
    }
}
