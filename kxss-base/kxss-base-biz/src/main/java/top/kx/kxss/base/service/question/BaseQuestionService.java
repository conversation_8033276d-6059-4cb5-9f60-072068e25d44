package top.kx.kxss.base.service.question;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.base.entity.question.BaseQuestion;
import top.kx.kxss.base.vo.save.question.BaseQuestionSaveVO;
import top.kx.kxss.base.vo.update.question.BaseQuestionUpdateVO;
import top.kx.kxss.base.vo.result.question.BaseQuestionResultVO;
import top.kx.kxss.base.vo.query.question.BaseQuestionPageQuery;


/**
 * <p>
 * 业务接口
 * 常见问题
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-26 16:24:10
 * @create [2023-05-26 16:24:10] [dou] [代码生成器生成]
 */
public interface BaseQuestionService extends SuperService<Long, BaseQuestion, BaseQuestionSaveVO,
    BaseQuestionUpdateVO, BaseQuestionPageQuery, BaseQuestionResultVO> {

    /**
     * 修改状态
     * @param id
     * @param state
     * @return
     */
    Boolean updateState(Long id, Boolean state);
}


