package top.kx.kxss.msg.strategy.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import top.kx.kxss.msg.strategy.MsgStrategy;
import top.kx.kxss.msg.strategy.domain.MsgParam;
import top.kx.kxss.msg.strategy.domain.MsgResult;
import top.kx.kxss.msg.entity.ExtendMsg;
import top.kx.kxss.msg.service.ExtendMsgService;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/7/11 0011 10:29
 */
public class TestMsgStrategyImpl implements MsgStrategy {
    private static final Logger log = LoggerFactory.getLogger(TestMsgStrategyImpl.class);

    @Resource
    private ExtendMsgService extendMsgService;

    @Override
    public MsgResult exec(MsgParam msgParam) {
        System.out.println(" 请开始你的接口逻辑 ");

        ExtendMsg a = extendMsgService.getById(msgParam.getExtendMsg().getId());
        log.info("a {}", a);

        return MsgResult.builder().result("保存成功").build();
    }
}
