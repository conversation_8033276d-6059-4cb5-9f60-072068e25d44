package top.kx.kxss.base.manager.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.base.entity.BaseSecurityCodeAttribute;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.base.manager.BaseSecurityCodeAttributeManager;
import top.kx.kxss.base.mapper.BaseSecurityCodeAttributeMapper;

/**
 * <p>
 * 通用业务实现类
 * 防伪信息规格属性
 * </p>
 *
 * <AUTHOR>
 * @date 2025-02-26 16:17:06
 * @create [2025-02-26 16:17:06] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BaseSecurityCodeAttributeManagerImpl extends SuperManagerImpl<BaseSecurityCodeAttributeMapper, BaseSecurityCodeAttribute> implements BaseSecurityCodeAttributeManager {

}


