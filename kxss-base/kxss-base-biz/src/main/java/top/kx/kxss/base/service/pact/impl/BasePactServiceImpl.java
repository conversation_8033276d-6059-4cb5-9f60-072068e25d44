package top.kx.kxss.base.service.pact.impl;

import cn.hutool.core.bean.BeanUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.kxss.base.entity.pact.BasePact;
import top.kx.kxss.base.manager.pact.BasePactManager;
import top.kx.kxss.base.service.pact.BasePactService;
import top.kx.kxss.base.vo.query.pact.BasePactPageQuery;
import top.kx.kxss.base.vo.result.pact.BasePactResultVO;
import top.kx.kxss.base.vo.result.pact.PactResultVO;
import top.kx.kxss.base.vo.save.pact.BasePactSaveVO;
import top.kx.kxss.base.vo.update.pact.BasePactUpdateVO;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 业务实现类
 * 协议配置
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-25 17:21:04
 * @create [2023-05-25 17:21:04] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
@Transactional(readOnly = true)
public class BasePactServiceImpl extends SuperServiceImpl<BasePactManager, Long, BasePact, BasePactSaveVO,
        BasePactUpdateVO, BasePactPageQuery, BasePactResultVO> implements BasePactService {


    @Override
    public List<PactResultVO> getListByType(String type) {
        List<BasePact> list = superManager.list(Wraps.<BasePact>lbQ().eq(BasePact::getType, type).eq(BasePact::getState, true)
                .orderByAsc(BasePact::getSortValue));
        return list.stream().map(v -> {
            return BeanUtil.copyProperties(v, PactResultVO.class);
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateState(Long id, Boolean state) {
        BasePact build = BasePact.builder().state(state).build();
        build.setId(id);
        return superManager.updateById(build);
    }
}


