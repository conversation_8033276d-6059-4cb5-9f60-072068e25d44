package top.kx.kxss.base.service.lottery.impl;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.entity.SuperEntity;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.cache.lock.DistributedLock;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.basic.utils.CodeUtils;
import top.kx.kxss.base.entity.BaseSecurityCode;
import top.kx.kxss.base.entity.lottery.*;
import top.kx.kxss.base.entity.prize.DefPrize;
import top.kx.kxss.base.manager.BaseSecurityCodeManager;
import top.kx.kxss.base.manager.lottery.LotteryActivityManager;
import top.kx.kxss.base.service.lottery.*;
import top.kx.kxss.base.service.prize.DefPrizeService;
import top.kx.kxss.base.vo.query.lottery.LotteryActivityPageQuery;
import top.kx.kxss.base.vo.result.lottery.LotteryActivityPrizeResultVO;
import top.kx.kxss.base.vo.result.lottery.LotteryActivityResultVO;
import top.kx.kxss.base.vo.result.lottery.LotteryChanceResultVO;
import top.kx.kxss.base.vo.result.prize.DefPrizeResultVO;
import top.kx.kxss.base.vo.save.lottery.LotteryActivityPrizeSaveVO;
import top.kx.kxss.base.vo.save.lottery.LotteryActivitySaveVO;
import top.kx.kxss.base.vo.save.lottery.LotteryChanceSaveVO;
import top.kx.kxss.base.vo.save.lottery.LotteryRecordSaveVO;
import top.kx.kxss.base.vo.update.lottery.LotteryActivityPrizeUpdateVO;
import top.kx.kxss.base.vo.update.lottery.LotteryActivityStatusUpdateVO;
import top.kx.kxss.base.vo.update.lottery.LotteryActivityUpdateVO;
import top.kx.kxss.base.vo.update.lottery.LotteryChanceUpdateVO;
import top.kx.kxss.base.vo.update.prize.DefPrizeUpdateVO;
import top.kx.kxss.base.vo.update.prize.PrizeStockUpdateVO;
import top.kx.kxss.model.enumeration.base.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

/**
 * <p>
 * 业务实现类
 * 抽奖活动表
 * </p>
 *
 * <AUTHOR>
 * @date 2025-09-15 17:23:11
 * @create [2025-09-15 17:23:11] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
//@Transactional(readOnly = true)
public class LotteryActivityServiceImpl extends SuperServiceImpl<LotteryActivityManager, Long, LotteryActivity, LotteryActivitySaveVO,
        LotteryActivityUpdateVO, LotteryActivityPageQuery, LotteryActivityResultVO> implements LotteryActivityService {

    private final LotteryActivityPrizeService lotteryActivityPrizeService;
    private final LotteryActivityBatchService lotteryActivityBatchService;
    private final DefPrizeService defPrizeService;
    private final LotteryChanceService lotteryChanceService;
    private final LotteryRecordService lotteryRecordService;
    private final BaseSecurityCodeManager baseSecurityCodeManager;
    private final EchoService echoService;
    @Autowired
    private DistributedLock distributedLock;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public LotteryActivity save(LotteryActivitySaveVO lotteryActivitySaveVO) {
        lotteryActivitySaveVO.setCode(getCode());
        if (lotteryActivitySaveVO.getIsOnline()) {
            lotteryActivitySaveVO.setStatus(LotteryActivityStatusEnum.IN_PROGRESS.getCode());
        }
        if (Objects.isNull(lotteryActivitySaveVO.getStatus())) {
            lotteryActivitySaveVO.setStatus(LotteryActivityStatusEnum.UNSTARTED.getCode());
        }
        lotteryActivitySaveVO.setEmployeeId(ContextUtil.getEmployeeId());
        LotteryActivity save = super.save(lotteryActivitySaveVO);
        List<LotteryActivityPrizeSaveVO> prizeSaveVOList = lotteryActivitySaveVO.getPrizeList();
        if (CollUtil.isNotEmpty(prizeSaveVOList)) {
            List<LotteryActivityPrize> saveVOList = BeanPlusUtil.toBeanList(prizeSaveVOList, LotteryActivityPrize.class);
            saveVOList.forEach(s -> {
                s.setActivityId(save.getId());
                s.setReceivedNum(0);
            });
            lotteryActivityPrizeService.saveBatch(saveVOList);
        }
        List<String> batchSaveVOList = lotteryActivitySaveVO.getBatchList();
        if (CollUtil.isNotEmpty(batchSaveVOList)) {
            List<LotteryActivityBatch> saveVOList = batchSaveVOList.stream().map(s -> LotteryActivityBatch.builder().activityId(save.getId()).batchCode(s).build()).collect(Collectors.toList());
            lotteryActivityBatchService.saveBatch(saveVOList);
        }
        return save;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public LotteryActivity updateById(LotteryActivityUpdateVO lotteryActivityUpdateVO) {
        // 不修改状态
        lotteryActivityUpdateVO.setStatus(null);
        LotteryActivity activity = this.getById(lotteryActivityUpdateVO.getId());
        ArgumentAssert.isTrue(Objects.nonNull(activity), "活动不存在");
        LotteryActivity lotteryActivity = super.updateById(lotteryActivityUpdateVO);
        // 只允许未开始的才能修改奖品
        if (CollUtil.isNotEmpty(lotteryActivityUpdateVO.getPrizeList()) && StringUtils.equals(activity.getStatus(), LotteryActivityStatusEnum.UNSTARTED.getCode())) {
            lotteryActivityPrizeService.remove(Wraps.<LotteryActivityPrize>lbQ().eq(LotteryActivityPrize::getActivityId, lotteryActivityUpdateVO.getId()));
            List<LotteryActivityPrize> saveVOList = BeanPlusUtil.toBeanList(lotteryActivityUpdateVO.getPrizeList(), LotteryActivityPrize.class);
            saveVOList.forEach(s -> {
                s.setActivityId(lotteryActivityUpdateVO.getId());
                s.setReceivedNum(0);
            });
            lotteryActivityPrizeService.saveBatch(saveVOList);
        }
        if (CollUtil.isNotEmpty(lotteryActivityUpdateVO.getBatchList()) && StringUtils.equals(activity.getStatus(), LotteryActivityStatusEnum.UNSTARTED.getCode())) {
            lotteryActivityBatchService.remove(Wraps.<LotteryActivityBatch>lbQ().eq(LotteryActivityBatch::getActivityId, lotteryActivityUpdateVO.getId()));
            List<LotteryActivityBatch> saveVOList = lotteryActivityUpdateVO.getBatchList().stream().map(s -> LotteryActivityBatch.builder().activityId(lotteryActivityUpdateVO.getId()).batchCode(s).build()).collect(Collectors.toList());
            lotteryActivityBatchService.saveBatch(saveVOList);
        }
        return lotteryActivity;
    }


    @Override
    public Boolean updateStatus(LotteryActivityStatusUpdateVO updateVO) {
        LotteryActivity activity = this.getById(updateVO.getId());
        ArgumentAssert.isTrue(Objects.nonNull(activity), "活动不存在");
        // 已经开水的活动，不能改为未开始
        ArgumentAssert.isTrue(!StringUtils.equals(activity.getStatus(), LotteryActivityStatusEnum.UNSTARTED.getCode()) && StringUtils.equals(updateVO.getStatus(), LotteryActivityStatusEnum.UNSTARTED.getCode()),
                "活动不能改为未开始");
        // 已经结束的活动，状态不允许修改了
        ArgumentAssert.isTrue(StringUtils.equals(activity.getStatus(), LotteryActivityStatusEnum.ENDED.getCode()), "活动已结束，状态不允许修改了");
        return superManager.update(Wraps.<LotteryActivity>lbU().set(LotteryActivity::getStatus, updateVO.getStatus()).eq(LotteryActivity::getId, updateVO.getId()));
    }

    @Override
    public LotteryActivityResultVO getDetail(Long id) {
        LotteryActivity activity = superManager.getById(id);
        if (Objects.isNull(activity)) {
            return null;
        }
        LotteryActivityResultVO resultVO = BeanPlusUtil.toBean(activity, LotteryActivityResultVO.class);
        List<LotteryActivityPrize> prizeList = lotteryActivityPrizeService.list(Wraps.<LotteryActivityPrize>lbQ().eq(LotteryActivityPrize::getActivityId, id));
        if (CollUtil.isNotEmpty(prizeList)) {
            resultVO.setPrizeList(BeanPlusUtil.toBeanList(prizeList, LotteryActivityPrizeResultVO.class));
        }
        List<LotteryActivityBatch> batchList = lotteryActivityBatchService.list(Wraps.<LotteryActivityBatch>lbQ().eq(LotteryActivityBatch::getActivityId, id));
        if (CollUtil.isNotEmpty(batchList)) {
            resultVO.setBatchList(batchList.stream().map(LotteryActivityBatch::getBatchCode).collect(Collectors.toList()));
        }
        // 设置统计信息（从活动表中直接获取，避免查询记录表）
        resultVO.setParticipateCount(activity.getParticipateCount() == null ? 0L : activity.getParticipateCount());
        resultVO.setWinCount(activity.getWinCount() == null ? 0L : activity.getWinCount());
        resultVO.setLotteryCount(activity.getLotteryCount() == null ? 0L : activity.getLotteryCount());
        resultVO.setPrizeCount(activity.getPrizeCount() == null ? 0L : activity.getPrizeCount());
        
        // 计算中奖率
        if (resultVO.getLotteryCount() > 0) {
            BigDecimal winRate = new BigDecimal(resultVO.getPrizeCount())
                    .divide(new BigDecimal(resultVO.getLotteryCount()), 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal(100));
            resultVO.setWinRate(winRate);
        } else {
            resultVO.setWinRate(BigDecimal.ZERO);
        }
        
        echoService.action(resultVO);
        return resultVO;
    }

    @Override
    public LotteryChanceResultVO getChance(LotteryActivityTypeEnum type, String source) {
        ArgumentAssert.isTrue(Objects.nonNull(type), "请选择活动类型");
        ArgumentAssert.isTrue(StringUtils.isNotBlank(source), "请填写来源");
        // 获取生效的抽奖活动
        LotteryActivity lotteryActivity = superManager.getOne(Wraps.<LotteryActivity>lbQ()
                .eq(LotteryActivity::getStatus, LotteryActivityStatusEnum.IN_PROGRESS.getCode())
                .eq(LotteryActivity::getType, type.getCode())
                .orderByDesc(LotteryActivity::getSortValue).last("limit 1"));
        if (Objects.isNull(lotteryActivity)) {
            return null;
        }
        // 判断这个码是不是已经用过了
        long count = lotteryChanceService.count(Wraps.<LotteryChance>lbQ()
                .eq(SuperEntity::getDeleteFlag, 0).eq(LotteryChance::getSource, source).eq(LotteryChance::getActivityType, type));
        if (count > 0) {
            log.info("抽奖机会已存在");
            return null;
        }
        switch (type) {
            case REGISTER:
                BaseSecurityCode securityCode = baseSecurityCodeManager.getOne(Wraps.<BaseSecurityCode>lbQ().eq(BaseSecurityCode::getSecurityCode, source));
                if (Objects.isNull(securityCode)) {
                    return null;
                }
                count = lotteryActivityBatchService.count(Wraps.<LotteryActivityBatch>lbQ()
                        .eq(LotteryActivityBatch::getActivityId, lotteryActivity.getId())
                        .eq(LotteryActivityBatch::getBatchCode, securityCode.getBatchCode()));
                if (count == 0) {
                    return null;
                }
                LotteryChanceSaveVO saveVO = LotteryChanceSaveVO.builder()
                        .activityId(lotteryActivity.getId())
                        .userId(ContextUtil.getUserId())
                        .activityType(type.getCode())
                        .chanceCount(lotteryActivity.getInitialChances())
                        .usedCount(0)
                        .remainingCount(lotteryActivity.getInitialChances())
                        .status("1")
                        .source(source)
                        .build();
                LotteryChance save = lotteryChanceService.save(saveVO);
                LotteryChanceResultVO resultVO = BeanPlusUtil.toBean(save, LotteryChanceResultVO.class);
                resultVO.setActivity(BeanPlusUtil.toBean(lotteryActivity, LotteryActivityResultVO.class));
                List<LotteryActivityPrize> activityPrizeList = lotteryActivityPrizeService.list(Wraps.<LotteryActivityPrize>lbQ()
                        .eq(LotteryActivityPrize::getActivityId, lotteryActivity.getId())
                        .orderByDesc(LotteryActivityPrize::getSortValue));
                List<Long> prizeIds = activityPrizeList.stream().map(LotteryActivityPrize::getPrizeId).distinct().collect(Collectors.toList());
                List<DefPrize> prizeList = defPrizeService.list(Wraps.<DefPrize>lbQ().eq(DefPrize::getId, prizeIds));
                prizeList.forEach(s -> {
                    s.setNum(null);
                    s.setReceivedNum(null);
                });
                resultVO.setPrizeList(BeanPlusUtil.toBeanList(prizeList, DefPrizeResultVO.class));
                return resultVO;
            default:
                log.error("未知的抽奖类型:{}", type);
                return null;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public DefPrizeResultVO lottery(Long id) {
        LotteryChance lotteryChance = lotteryChanceService.getById(id);
        ArgumentAssert.isTrue(Objects.nonNull(lotteryChance), "您没有抽奖机会，无法进行抽奖");
        ArgumentAssert.isTrue(lotteryChance.getRemainingCount() > 0, "您的抽奖机会已经用完，下次再来吧");
        // 全部抽奖
        ArgumentAssert.isTrue(StringUtils.equals(lotteryChance.getStatus(), LotteryChangeStatusEnum.COMPLETE_LOTTERY.getCode()), "请勿重复抽奖");
        
        boolean lock = false;
        try {
            // 使用分布式锁防止并发抽奖
            lock = distributedLock.lock("ACTIVITY_LOTTERY:" + lotteryChance.getActivityId(), 10);
            if (!lock) {
                ArgumentAssert.isTrue(false, "操作太频繁，请慢一些！");
            }
            
            // 重新查询机会状态，防止并发修改
            lotteryChance = lotteryChanceService.getById(id);
            ArgumentAssert.isTrue(Objects.nonNull(lotteryChance), "抽奖机会不存在");
            ArgumentAssert.isTrue(lotteryChance.getRemainingCount() > 0, "您的抽奖机会已经用完，下次再来吧");
            
            // 检查活动状态
            LotteryActivity activity = superManager.getById(lotteryChance.getActivityId());
            ArgumentAssert.isTrue(Objects.nonNull(activity), "抽奖活动已经停止，请下次再来吧");
            ArgumentAssert.isTrue(activity.getStatus().equals(LotteryActivityStatusEnum.IN_PROGRESS.getCode()), "抽奖活动已经结束，请下次再来吧");
            
            // 获取活动奖品配置
            List<LotteryActivityPrize> activityPrizeList = lotteryActivityPrizeService.list(Wraps.<LotteryActivityPrize>lbQ()
                    .eq(LotteryActivityPrize::getActivityId, activity.getId())
                    .orderByDesc(LotteryActivityPrize::getSortValue));
            
            ArgumentAssert.isTrue(CollUtil.isNotEmpty(activityPrizeList), "活动奖品配置不存在");
            
            // 过滤掉库存不足的奖品
            List<LotteryActivityPrize> availablePrizes = filterAvailablePrizes(activityPrizeList);
            ArgumentAssert.isTrue(CollUtil.isNotEmpty(availablePrizes), "所有奖品都已发放完毕，谢谢参与");
            
            // 根据概率选择奖品
            LotteryActivityPrize selectedPrize = selectPrizeByProbability(availablePrizes);
            
            // 获取奖品详细信息
            DefPrize prizeInfo = defPrizeService.getById(selectedPrize.getPrizeId());
            ArgumentAssert.isTrue(Objects.nonNull(prizeInfo), "奖品信息不存在");
            
            // 校验奖品总库存（def_prize表）
            boolean defPrizeStockValid = validateDefPrizeStock(prizeInfo);
            if (!defPrizeStockValid) {
                log.warn("奖品总库存不足，活动ID：{}，奖品ID：{}，奖品名称：{}", 
                        activity.getId(), selectedPrize.getPrizeId(), prizeInfo.getName());
                // 如果总库存不足，给用户一个谢谢参与
                prizeInfo = getThankYouPrize();
            } else {
                // 更新活动奖品库存（lottery_activity_prize表）
                boolean stockUpdated = updatePrizeStock(selectedPrize);
                if (!stockUpdated) {
                    log.warn("活动奖品库存更新失败，可能已被其他用户领取，活动ID：{}，奖品ID：{}", 
                            activity.getId(), selectedPrize.getPrizeId());
                    // 如果活动库存更新失败，给用户一个谢谢参与
                    prizeInfo = getThankYouPrize();
                } else {
                    // 活动库存更新成功，继续更新总库存（def_prize表）
                    // boolean defPrizeStockUpdated = updateDefPrizeStock(prizeInfo);
                    try {
                        defPrizeService.updateStock(PrizeStockUpdateVO.builder()
                                        .prizeId(prizeInfo.getId())
                                        .numType(StockChangeNumTypeEnum.REDUCE.getCode())
                                        .num(prizeInfo.getNum())
                                        .remarks("抽奖")
                                        .type(PrizeStockChangeTypeEnum.PRIZE_CONSUMPTION.getCode())
                                .build());
                    } catch (Exception e) {
                        log.error("奖品总库存更新失败，活动ID：{}，奖品ID：{}",
                                activity.getId(), selectedPrize.getPrizeId(), e);
                        rollbackActivityPrizeStock(selectedPrize);
                        prizeInfo = getThankYouPrize();
                    }
//                    if (!defPrizeStockUpdated) {
//                        log.warn("奖品总库存更新失败，活动ID：{}，奖品ID：{}",
//                                activity.getId(), selectedPrize.getPrizeId());
//                        // 如果总库存更新失败，回滚活动库存，给用户谢谢参与
//                        rollbackActivityPrizeStock(selectedPrize);
//                        prizeInfo = getThankYouPrize();
//                    }
                }
            }
            
            // 更新抽奖机会
            lotteryChance.setUsedCount(lotteryChance.getUsedCount() + 1);
            lotteryChance.setRemainingCount(lotteryChance.getRemainingCount() - 1);
            if (lotteryChance.getRemainingCount() <= 0) {
                lotteryChance.setStatus(LotteryChangeStatusEnum.COMPLETE_LOTTERY.getCode()); // 全部抽奖
            } else if (lotteryChance.getUsedCount() > 0) {
                lotteryChance.setStatus(LotteryChangeStatusEnum.PARTIAL_LOTTERY.getCode()); // 部分抽奖
            }
            lotteryChanceService.updateById(BeanPlusUtil.toBean(lotteryChance, LotteryChanceUpdateVO.class));
            
            // 创建抽奖记录
            createLotteryRecord(lotteryChance, prizeInfo);
            
            // 更新活动统计信息
            updateActivityStatistics(activity.getId(), lotteryChance.getUserId(), 
                    !PrizeTypeEnum.THANK_YOU.getCode().equals(prizeInfo.getType()));
            
            log.info("用户抽奖完成，用户ID：{}，活动ID：{}，奖品ID：{}，奖品名称：{}", 
                    lotteryChance.getUserId(), lotteryChance.getActivityId(), prizeInfo.getId(), prizeInfo.getName());
            
            // 返回奖品信息
            DefPrizeResultVO resultVO = BeanPlusUtil.toBean(prizeInfo, DefPrizeResultVO.class);
            // 隐藏库存信息，避免泄露
            resultVO.setNum(null);
            resultVO.setReceivedNum(null);
            
            return resultVO;
            
        } finally {
            if (lock) {
                distributedLock.releaseLock("ACTIVITY_LOTTERY:" + lotteryChance.getActivityId());
            }
        }
    }
    
    /**
     * 过滤掉库存不足的奖品
     */
    private List<LotteryActivityPrize> filterAvailablePrizes(List<LotteryActivityPrize> activityPrizeList) {
        return activityPrizeList.stream()
                .filter(prize -> {
                    // 如果没有设置库存限制，认为可用
                    if (prize.getNum() == null || prize.getNum() <= 0) {
                        return true;
                    }
                    // 检查已发放数量是否小于库存限制
                    return prize.getReceivedNum() == null || prize.getReceivedNum() < prize.getNum();
                })
                .collect(Collectors.toList());
    }
    
    /**
     * 根据概率选择奖品
     */
    private LotteryActivityPrize selectPrizeByProbability(List<LotteryActivityPrize> availablePrizes) {
        if (availablePrizes == null || availablePrizes.isEmpty()) {
            return null;
        }

        // 计算总概率
        BigDecimal totalProbability = BigDecimal.ZERO;
        for (LotteryActivityPrize prize : availablePrizes) {
            if (prize.getProbability() != null) {
                totalProbability = totalProbability.add(prize.getProbability());
            }
        }

        // 随机生成一个 [0, totalProbability) 的值
        BigDecimal randomValue = BigDecimal.valueOf(ThreadLocalRandom.current().nextDouble())
                .multiply(totalProbability);

        // 遍历累加概率，找到落点
        BigDecimal cumulative = BigDecimal.ZERO;
        for (LotteryActivityPrize prize : availablePrizes) {
            if (prize.getProbability() == null) {
                continue;
            }
            cumulative = cumulative.add(prize.getProbability());
            if (randomValue.compareTo(cumulative) < 0) {
                return prize;
            }
        }

        // 如果没有选中， 就返回概率最大的一个
        return availablePrizes.stream().max(Comparator.comparing(LotteryActivityPrize::getProbability)).orElse(null);
    }
    
    /**
     * 更新奖品库存
     */
    private boolean updatePrizeStock(LotteryActivityPrize selectedPrize) {
        LotteryActivityPrize updatePrize = new LotteryActivityPrize();
        updatePrize.setId(selectedPrize.getId());
        updatePrize.setReceivedNum((selectedPrize.getReceivedNum() == null ? 0 : selectedPrize.getReceivedNum()) + 1);

        LotteryActivityPrizeUpdateVO updateVO = BeanPlusUtil.toBean(updatePrize, LotteryActivityPrizeUpdateVO.class);
        LotteryActivityPrize updated = lotteryActivityPrizeService.updateById(updateVO);
        boolean success = updated != null;

        if (success) {
            // 更新成功，同步更新内存中的对象
            selectedPrize.setReceivedNum(updatePrize.getReceivedNum());
            return true;
        } else {
            log.warn("奖品库存更新失败，可能已被其他用户领取，奖品ID：{}", selectedPrize.getId());
            return false;
        }
    }
    
    /**
     * 获取谢谢参与奖品
     */
    private DefPrize getThankYouPrize() {
        // 这里应该根据你的业务逻辑获取谢谢参与奖品
        // 可能是一个固定的奖品ID，或者从配置中获取
        DefPrize thankYouPrize = defPrizeService.getOne(Wraps.<DefPrize>lbQ().eq(DefPrize::getType, 1).eq(SuperEntity::getDeleteFlag, PrizeTypeEnum.THANK_YOU.getCode())
                .eq(DefPrize::getStatus, true).last("limit 1"));
        if (Objects.nonNull(thankYouPrize)) {
            return thankYouPrize;
        }
//        thankYouPrize = new DefPrize();
//        thankYouPrize.setId(-1L); // 使用特殊ID表示谢谢参与
//        thankYouPrize.setName("谢谢参与");
//        thankYouPrize.setType(PrizeTypeEnum.THANK_YOU.getCode()); // 1=谢谢参与
//        thankYouPrize.setValueAmount(BigDecimal.ZERO);
//        thankYouPrize.setThreshold(BigDecimal.ZERO);
        return thankYouPrize;
    }
    
    /**
     * 创建抽奖记录
     * 注意：这里应该关联抽奖机会ID，确保抽奖记录和抽奖机会的对应关系
     */
    private void createLotteryRecord(LotteryChance lotteryChance, DefPrize prizeInfo) {
        try {
            log.info("创建抽奖记录，抽奖机会ID：{}，用户ID：{}，活动ID：{}，奖品ID：{}", 
                    lotteryChance.getId(), lotteryChance.getUserId(), lotteryChance.getActivityId(), prizeInfo.getId());
            
            // 创建抽奖记录，关键是要关联抽奖机会ID
            LotteryRecordSaveVO recordSaveVO = LotteryRecordSaveVO.builder()
                    .chanceId(lotteryChance.getId())  // 关联抽奖机会ID
                    .activityId(lotteryChance.getActivityId())
                    .userId(lotteryChance.getUserId())
                    .prizeId(prizeInfo.getId())
                    .prizeName(prizeInfo.getName())
                    .prizeType(prizeInfo.getType())
                    .valueAmount(prizeInfo.getValueAmount())
                    .threshold(prizeInfo.getThreshold())
                    .couponCode(prizeInfo.getCouponCode())
                    .status(StringUtils.equals(prizeInfo.getType(), PrizeTypeEnum.THANK_YOU.getCode()) ? LotteryRecordStatusEnum.UNLUCKY.getCode() : LotteryRecordStatusEnum.LUCKY.getCode()) // 1=谢谢参与时为0，其他为1
                    .remarks("用户抽奖记录，关联机会ID：" + lotteryChance.getId())
                    .build();
            
            LotteryRecord savedRecord = lotteryRecordService.save(recordSaveVO);
            
            log.info("抽奖记录创建成功，抽奖机会ID：{}，记录ID：{}", lotteryChance.getId(), savedRecord.getId());
            
        } catch (Exception e) {
            log.error("创建抽奖记录失败，抽奖机会ID：{}，用户ID：{}，活动ID：{}，奖品ID：{}", 
                    lotteryChance.getId(), lotteryChance.getUserId(), lotteryChance.getActivityId(), prizeInfo.getId(), e);
            // 抽奖记录创建失败不应该影响抽奖结果，只记录日志
            // 但可以考虑重试机制或者异步处理
        }
    }
    
    /**
     * 校验奖品总库存（def_prize表）
     *
     * @param prizeInfo 奖品信息
     * @return true=库存充足，false=库存不足
     */
    private boolean validateDefPrizeStock(DefPrize prizeInfo) {
        // 如果是谢谢参与奖品，不需要校验库存
        if (PrizeTypeEnum.THANK_YOU.getCode().equals(prizeInfo.getType())) {
            return true;
        }
        
        // 检查总库存是否充足
        Integer totalStock = prizeInfo.getNum();
        Integer usedStock = prizeInfo.getReceivedNum() == null ? 0 : prizeInfo.getReceivedNum();
        
        if (totalStock == null || totalStock <= 0) {
            log.warn("奖品总库存为0或未设置，奖品ID：{}，奖品名称：{}", prizeInfo.getId(), prizeInfo.getName());
            return false;
        }
        
        if (usedStock >= totalStock) {
            log.warn("奖品总库存已用完，奖品ID：{}，奖品名称：{}，总库存：{}，已使用：{}", 
                    prizeInfo.getId(), prizeInfo.getName(), totalStock, usedStock);
            return false;
        }
        
        return true;
    }
    
    /**
     * 更新奖品总库存（def_prize表）
     *
     * @param prizeInfo 奖品信息
     * @return true=更新成功，false=更新失败
     */
    public boolean updateDefPrizeStock(DefPrize prizeInfo) {
        // 没有匹配上的奖品ID，返回失败
        if (Objects.isNull(prizeInfo.getId())) {
            return false;
        }
        boolean lock = false;
        try {
            lock = distributedLock.lock("PRIZE_STOCK:" + prizeInfo.getId(), 0);
            if (!lock) {
                ArgumentAssert.isTrue(false, "操作太频繁，请慢一些！");
            }
            if (Objects.isNull(prizeInfo.getId())) {
                return false;
            }
            
            // 使用乐观锁更新总库存
            DefPrize updatePrize = new DefPrize();
            updatePrize.setId(prizeInfo.getId());
            updatePrize.setReceivedNum((prizeInfo.getReceivedNum() == null ? 0 : prizeInfo.getReceivedNum()) + 1);
            
            // 这里需要根据你的DefPrizeService的update方法来实现
            // 可能需要使用条件更新来防止并发问题
            DefPrize updated = defPrizeService.updateById(BeanPlusUtil.toBean(updatePrize, DefPrizeUpdateVO.class));
            if (updated != null) {
                // 更新成功，同步更新内存中的对象
                prizeInfo.setReceivedNum(updatePrize.getReceivedNum());
                log.info("奖品总库存更新成功，奖品ID：{}，奖品名称：{}，已使用：{}", 
                        prizeInfo.getId(), prizeInfo.getName(), updatePrize.getReceivedNum());
                return true;
            } else {
                log.warn("奖品总库存更新失败，可能已被其他用户领取，奖品ID：{}", prizeInfo.getId());
                return false;
            }
        } catch (Exception e) {
            log.error("更新奖品总库存失败，奖品ID：{}", prizeInfo.getId(), e);
            return false;
        } finally {
            if (lock) {
                distributedLock.releaseLock("PRIZE_STOCK:" + prizeInfo.getId());
            }
        }
    }
    
    /**
     * 回滚活动奖品库存（当总库存更新失败时）
     *
     * @param selectedPrize 选中的活动奖品
     */
    private void rollbackActivityPrizeStock(LotteryActivityPrize selectedPrize) {
        try {
            // 回滚活动奖品库存
            LotteryActivityPrize rollbackPrize = new LotteryActivityPrize();
            rollbackPrize.setId(selectedPrize.getId());
            rollbackPrize.setReceivedNum((selectedPrize.getReceivedNum() == null ? 0 : selectedPrize.getReceivedNum()) - 1);
            
            lotteryActivityPrizeService.updateById(BeanPlusUtil.toBean(rollbackPrize, LotteryActivityPrizeUpdateVO.class));
            
            // 同步更新内存中的对象
            selectedPrize.setReceivedNum(rollbackPrize.getReceivedNum());
            
            log.info("活动奖品库存回滚成功，奖品ID：{}，已使用：{}", selectedPrize.getId(), rollbackPrize.getReceivedNum());
        } catch (Exception e) {
            log.error("活动奖品库存回滚失败，奖品ID：{}", selectedPrize.getId(), e);
        }
    }
    
    /**
     * 更新活动统计信息
     *
     * @param activityId 活动ID
     * @param userId 用户ID
     * @param isWin 是否中奖
     */
    private void updateActivityStatistics(Long activityId, Long userId, boolean isWin) {
        try {
            // 获取当前活动信息
            LotteryActivity activity = superManager.getById(activityId);
            if (activity == null) {
                log.warn("活动不存在，无法更新统计信息，活动ID：{}", activityId);
                return;
            }
            
            // 初始化统计字段（如果为null）
            if (activity.getParticipateCount() == null) {
                activity.setParticipateCount(0);
            }
            if (activity.getWinCount() == null) {
                activity.setWinCount(0);
            }
            if (activity.getLotteryCount() == null) {
                activity.setLotteryCount(0);
            }
            if (activity.getPrizeCount() == null) {
                activity.setPrizeCount(0);
            }
            
            // 更新统计信息
            activity.setLotteryCount(activity.getLotteryCount() + 1); // 抽奖次数+1
            
            if (isWin) {
                activity.setPrizeCount(activity.getPrizeCount() + 1); // 中奖次数+1
            }
            
            // 检查是否是新参与用户（第一次抽奖）
            boolean isNewParticipant = lotteryRecordService.count(Wraps.<LotteryRecord>lbQ()
                    .eq(LotteryRecord::getActivityId, activityId)
                    .eq(LotteryRecord::getUserId, userId)) == 1;
            
            if (isNewParticipant) {
                activity.setParticipateCount(activity.getParticipateCount() + 1); // 参与人次+1
                
                if (isWin) {
                    activity.setWinCount(activity.getWinCount() + 1); // 中奖人次+1
                }
            }
            
            // 保存更新
            superManager.updateById(activity);
            
            log.info("活动统计信息更新成功，活动ID：{}，抽奖次数：{}，中奖次数：{}，参与人次：{}，中奖人次：{}", 
                    activityId, activity.getLotteryCount(), activity.getPrizeCount(), 
                    activity.getParticipateCount(), activity.getWinCount());
            
        } catch (Exception e) {
            log.error("更新活动统计信息失败，活动ID：{}，用户ID：{}", activityId, userId, e);
            // 统计更新失败不影响抽奖结果
        }
    }


    public String getCode() {
        String code = CodeUtils.generateCode(CodeIdentifyEnum.PRIZE_CODE.getCode(), true, CodeUtils.YYYY_MM_DD, 6);
        if (checkCode(code, null)) {
            return getCode();
        }
        return code;
    }

    public Boolean checkCode(String code, Long id) {
        return superManager.count(Wraps.<LotteryActivity>lbQ()
                .eq(LotteryActivity::getCode, code)
                .ne(SuperEntity::getId, id)) > 0;
    }
}


