package top.kx.kxss.base.service.system.impl;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.kxss.base.entity.system.BaseRoleResourceRel;
import top.kx.kxss.base.manager.system.BaseRoleResourceRelManager;
import top.kx.kxss.base.service.system.BaseRoleResourceRelService;
import top.kx.kxss.base.vo.query.system.BaseRoleResourceRelPageQuery;
import top.kx.kxss.base.vo.result.system.BaseRoleResourceRelResultVO;
import top.kx.kxss.base.vo.save.system.BaseRoleResourceRelSaveVO;
import top.kx.kxss.base.vo.update.system.BaseRoleResourceRelUpdateVO;


/**
 * <p>
 * 业务实现类
 * 角色的资源
 * </p>
 *
 * <AUTHOR>
 * @date 2021-10-18
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)

public class BaseRoleResourceRelServiceImpl extends SuperServiceImpl<BaseRoleResourceRelManager, Long, BaseRoleResourceRel,
        BaseRoleResourceRelSaveVO, BaseRoleResourceRelUpdateVO, BaseRoleResourceRelPageQuery, BaseRoleResourceRelResultVO>
        implements BaseRoleResourceRelService {
}
