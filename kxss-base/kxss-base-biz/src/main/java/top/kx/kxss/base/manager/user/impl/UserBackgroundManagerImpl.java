package top.kx.kxss.base.manager.user.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.base.entity.user.UserBackground;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.base.manager.user.UserBackgroundManager;
import top.kx.kxss.base.mapper.user.UserBackgroundMapper;

/**
 * <p>
 * 通用业务实现类
 * 用户背景图
 * </p>
 *
 * <AUTHOR>
 * @date 2025-04-19 16:25:46
 * @create [2025-04-19 16:25:46] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class UserBackgroundManagerImpl extends SuperManagerImpl<UserBackgroundMapper, UserBackground> implements UserBackgroundManager {

}


