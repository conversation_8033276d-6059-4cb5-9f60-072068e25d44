package top.kx.kxss.base.manager.member;

import top.kx.basic.base.manager.SuperManager;
import top.kx.basic.interfaces.echo.LoadService;
import top.kx.kxss.base.entity.member.MemberInfo;

/**
 * <p>
 * 通用业务接口
 * 会员信息
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-24 18:03:33
 * @create [2023-05-24 18:03:33] [dou] [代码生成器生成]
 */
public interface MemberInfoManager extends SuperManager<MemberInfo>, LoadService {

}


