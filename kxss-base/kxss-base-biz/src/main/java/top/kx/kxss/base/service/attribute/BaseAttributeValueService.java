package top.kx.kxss.base.service.attribute;

import top.kx.basic.base.service.SuperService;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.base.entity.attribute.BaseAttributeValue;
import top.kx.kxss.base.vo.save.attribute.BaseAttributeValueSaveVO;
import top.kx.kxss.base.vo.update.attribute.BaseAttributeValueUpdateVO;
import top.kx.kxss.base.vo.result.attribute.BaseAttributeValueResultVO;
import top.kx.kxss.base.vo.query.attribute.BaseAttributeValuePageQuery;


/**
 * <p>
 * 业务接口
 * 商品基础属性值
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-16 18:44:16
 * @create [2025-06-16 18:44:16] [yan] [代码生成器生成]
 */
public interface BaseAttributeValueService extends SuperService<Long, BaseAttributeValue, BaseAttributeValueSaveVO,
    BaseAttributeValueUpdateVO, BaseAttributeValuePageQuery, BaseAttributeValueResultVO> {

    void remove(LbQueryWrap<BaseAttributeValue> eq);
}


