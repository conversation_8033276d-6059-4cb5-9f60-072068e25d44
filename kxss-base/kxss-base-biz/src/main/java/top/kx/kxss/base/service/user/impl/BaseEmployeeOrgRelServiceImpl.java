package top.kx.kxss.base.service.user.impl;

import cn.hutool.core.convert.Convert;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.base.entity.user.BaseEmployeeOrgRel;
import top.kx.kxss.base.manager.user.BaseEmployeeOrgRelManager;
import top.kx.kxss.base.service.user.BaseEmployeeOrgRelService;
import top.kx.kxss.base.vo.query.user.BaseEmployeeOrgRelPageQuery;
import top.kx.kxss.base.vo.result.user.BaseEmployeeOrgRelResultVO;
import top.kx.kxss.base.vo.save.user.BaseEmployeeOrgRelSaveVO;
import top.kx.kxss.base.vo.update.user.BaseEmployeeOrgRelUpdateVO;

import java.util.List;

/**
 * <p>
 * 业务实现类
 * 员工所在部门
 * </p>
 *
 * <AUTHOR>
 * @date 2021-10-18
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)

public class BaseEmployeeOrgRelServiceImpl extends SuperServiceImpl<BaseEmployeeOrgRelManager, Long, BaseEmployeeOrgRel, BaseEmployeeOrgRelSaveVO, BaseEmployeeOrgRelUpdateVO, BaseEmployeeOrgRelPageQuery, BaseEmployeeOrgRelResultVO> implements BaseEmployeeOrgRelService {
    @Override
    public List<Long> findOrgIdListByEmployeeId(Long employeeId) {
        ArgumentAssert.notNull(employeeId, "员工id不能为空");
        return superManager.listObjs(Wraps.<BaseEmployeeOrgRel>lbQ()
                .select(BaseEmployeeOrgRel::getOrgId)
                .eq(BaseEmployeeOrgRel::getEmployeeId, employeeId), Convert::toLong);
    }
}
