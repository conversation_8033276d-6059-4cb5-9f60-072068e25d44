package top.kx.kxss.base.service.pact;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.base.entity.pact.BasePact;
import top.kx.kxss.base.vo.query.pact.BasePactPageQuery;
import top.kx.kxss.base.vo.result.pact.BasePactResultVO;
import top.kx.kxss.base.vo.result.pact.PactResultVO;
import top.kx.kxss.base.vo.save.pact.BasePactSaveVO;
import top.kx.kxss.base.vo.update.pact.BasePactUpdateVO;

import java.util.List;


/**
 * <p>
 * 业务接口
 * 协议配置
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-25 17:21:04
 * @create [2023-05-25 17:21:04] [dou] [代码生成器生成]
 */
public interface BasePactService extends SuperService<Long, BasePact, BasePactSaveVO,
    BasePactUpdateVO, BasePactPageQuery, BasePactResultVO> {

    /**
     * 根据类型查询数据
     * @param type
     * @return
     */
    List<PactResultVO> getListByType(String type);
    /**
     * 修改状态
     * @param id
     * @param state
     * @return
     */
    Boolean updateState(Long id, Boolean state);

}


