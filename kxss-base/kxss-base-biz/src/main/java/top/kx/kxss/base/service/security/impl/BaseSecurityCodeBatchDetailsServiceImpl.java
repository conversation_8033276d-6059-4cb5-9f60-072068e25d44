package top.kx.kxss.base.service.security.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.IdUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.entity.SuperEntity;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.update.LbUpdateWrap;
import top.kx.kxss.base.entity.BaseSecurityCode;
import top.kx.kxss.base.entity.security.BaseSecurityCodeBatch;
import top.kx.kxss.base.entity.security.BaseSecurityCodeBatchDetails;
import top.kx.kxss.base.manager.BaseSecurityCodeManager;
import top.kx.kxss.base.manager.security.BaseSecurityCodeBatchDetailsManager;
import top.kx.kxss.base.manager.security.BaseSecurityCodeBatchManager;
import top.kx.kxss.base.service.security.BaseSecurityCodeBatchDetailsService;
import top.kx.kxss.base.vo.query.security.BaseSecurityCodeBatchDetailsPageQuery;
import top.kx.kxss.base.vo.result.security.BaseSecurityCodeBatchDetailsResultVO;
import top.kx.kxss.base.vo.save.security.BaseSecurityCodeBatchDetailsSaveVO;
import top.kx.kxss.base.vo.update.security.BaseSecurityCodeBatchDetailsUpdateVO;
import top.kx.kxss.base.vo.update.security.BaseSecurityCodeBatchUpdateVO;
import top.kx.kxss.model.enumeration.base.SecurityCodeBatchImportEnum;
import top.kx.kxss.model.enumeration.base.SecurityCodeBatchStatusEnum;
import top.kx.kxss.model.enumeration.base.SecurityCodeStatusEnum;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 业务实现类
 * 防伪码批次-详情
 * </p>
 *
 * <AUTHOR>
 * @date 2025-04-25 17:45:12
 * @create [2025-04-25 17:45:12] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BaseSecurityCodeBatchDetailsServiceImpl extends SuperServiceImpl<BaseSecurityCodeBatchDetailsManager, Long, BaseSecurityCodeBatchDetails, BaseSecurityCodeBatchDetailsSaveVO,
        BaseSecurityCodeBatchDetailsUpdateVO, BaseSecurityCodeBatchDetailsPageQuery, BaseSecurityCodeBatchDetailsResultVO> implements BaseSecurityCodeBatchDetailsService {

    private final BaseSecurityCodeBatchManager baseSecurityCodeBatchManager;
    private final BaseSecurityCodeManager baseSecurityCodeManager;

    @Override
    public void saveBatch(BaseSecurityCodeBatch batch) {
        if (Objects.isNull(batch.getIsSeries())) {
            baseSecurityCodeBatchManager.update(Wraps.<BaseSecurityCodeBatch>lbU()
                    .set(BaseSecurityCodeBatch::getState, SecurityCodeBatchStatusEnum.GENERATE_FAIL.getCode())
                    .set(BaseSecurityCodeBatch::getStateDesc, "生成失败-未指定是否子母码")
                    .eq(BaseSecurityCodeBatch::getId, batch.getId()));
            return;
        }
        try {
            saveBatchDetails(batch);
            // 修改生成状态
            baseSecurityCodeBatchManager.update(Wraps.<BaseSecurityCodeBatch>lbU()
                    .set(BaseSecurityCodeBatch::getState, SecurityCodeBatchStatusEnum.GENERATED.getCode())
                    .set(BaseSecurityCodeBatch::getStateDesc, "")
                    .eq(BaseSecurityCodeBatch::getId, batch.getId()));

        } catch (Exception e) {
            log.error("生成防伪码失败", e);
            String fullError = e.getClass().getSimpleName() + ": " + e.getMessage();
            //String rootCause = Objects.requireNonNull(NestedExceptionUtils.getRootCause(e)).getMessage();
            baseSecurityCodeBatchManager.update(Wraps.<BaseSecurityCodeBatch>lbU()
                    .set(BaseSecurityCodeBatch::getState, SecurityCodeBatchStatusEnum.GENERATE_FAIL.getCode())
                    .set(BaseSecurityCodeBatch::getStateDesc, fullError)
                    .eq(BaseSecurityCodeBatch::getId, batch.getId()));

        }

    }

    /**
     * 保存批次详情
     *
     * @param batch
     */
    private void saveBatchDetails(BaseSecurityCodeBatch batch) {
        if (batch.getIsSeries()) {
            BaseSecurityCode bigCodeBatchDetails = baseSecurityCodeManager.getOne(Wraps.<BaseSecurityCode>lbQ().isNotNull(BaseSecurityCode::getBigCode).orderByDesc(BaseSecurityCode::getBigCode).last("limit 1"));
            long startNum = (Objects.nonNull(bigCodeBatchDetails) && LocalDateTimeUtil.isSameDay(bigCodeBatchDetails.getCreatedTime(), LocalDateTimeUtil.now())) ? Long.parseLong(bigCodeBatchDetails.getBigCode().substring(9)) : 0;
            //long bigCodeSize = batch.getNum() / batch.getSeriesRatio();
            long bigCodeSize = batch.getNum();
            List<String> bigCodeList = getBigCode("kxp" + LocalDateTimeUtil.format(LocalDateTimeUtil.now(), "yyyyMMdd"), bigCodeSize, startNum);
            BaseSecurityCode baseSecurityCode = baseSecurityCodeManager.getOne(Wraps.<BaseSecurityCode>lbQ().orderByDesc(BaseSecurityCode::getSecurityCode).last("limit 1"));
            long start = (Objects.nonNull(baseSecurityCode) && LocalDateTimeUtil.isSameDay(baseSecurityCode.getCreatedTime(), LocalDateTimeUtil.now())) ? Long.parseLong(baseSecurityCode.getSecurityCode().substring(6)) : 0;
            List<String> securityCodeList = getSecurityCode(batch.getNum().intValue() * (int) bigCodeSize, start);
            for (String bigCode : bigCodeList) {
                if (securityCodeList.size() >= batch.getSeriesRatio()) {
                    List<String> codeList = securityCodeList.subList(0, batch.getSeriesRatio());
                    saveBigBatchDetails(batch, bigCode, codeList);
                    securityCodeList.removeAll(codeList);
                } else {
                    saveBigBatchDetails(batch, bigCode, securityCodeList);
                }
            }
        } else {
            BaseSecurityCode baseSecurityCode = baseSecurityCodeManager.getOne(Wraps.<BaseSecurityCode>lbQ().orderByDesc(BaseSecurityCode::getSecurityCode).last("limit 1"));
            long start = (Objects.nonNull(baseSecurityCode) && LocalDateTimeUtil.isSameDay(baseSecurityCode.getCreatedTime(), LocalDateTimeUtil.now())) ? Long.parseLong(baseSecurityCode.getSecurityCode().substring(6)) : 0;
            // 每次生成1000个，批量导入， 剩余的最后一次导入
            long totalCount = batch.getNum() / 1000;
            long endSize = batch.getNum() % 1000;
            List<String> securityCodeList = getSecurityCode(batch.getNum().intValue(), start);
            if (totalCount > 0) {
                for (int i = 0; i < totalCount; i++) {
                    List<String> codeList = securityCodeList.subList(0, batch.getSeriesRatio());
                    securityCodeList.removeAll(codeList);
                    saveBatchDetails(batch, codeList);
                }
            }
            if (endSize > 0) {
                saveBatchDetails(batch, securityCodeList);
            }
        }
    }

    private void saveBigBatchDetails(BaseSecurityCodeBatch batch, String bigCode, List<String> securityCodeList) {
        List<BaseSecurityCodeBatchDetails> batchDetailsList = securityCodeList.stream().map(securityCode -> BaseSecurityCodeBatchDetails.builder()
                .batchId(batch.getId())
                .securityCode(securityCode)
                .bigSecurityCode(bigCode)
                .url(StringUtils.isNotBlank(batch.getLinkUrl()) ? batch.getLinkUrl() + securityCode : null)
                .bigUrl(StringUtils.isNotBlank(batch.getBigLinkUrl()) ? batch.getBigLinkUrl() + bigCode : null)
                .linkType(batch.getLinkType())
                .bigLinkType(batch.getLinkType())
                .build()).collect(Collectors.toList());
        superManager.saveBatch(batchDetailsList);
    }

    private void saveBatchDetails(BaseSecurityCodeBatch batch, List<String> securityCodeList) {
        List<BaseSecurityCodeBatchDetails> batchDetailsList = securityCodeList.stream().map(securityCode -> BaseSecurityCodeBatchDetails.builder()
                .batchId(batch.getId())
                .securityCode(securityCode)
                .bigSecurityCode("-")
                .url(StringUtils.isNotBlank(batch.getLinkUrl()) ? batch.getLinkUrl() + securityCode : null)
                .bigUrl(null)
                .linkType(batch.getLinkType())
                .bigLinkType(null)
                .build()).collect(Collectors.toList());
        superManager.saveBatch(batchDetailsList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBatch(BaseSecurityCodeBatch batch, BaseSecurityCodeBatchUpdateVO batchUpdateVO) {
        long count = superManager.count(Wraps.<BaseSecurityCodeBatchDetails>lbQ().eq(BaseSecurityCodeBatchDetails::getBatchId, batch.getId()).eq(SuperEntity::getDeleteFlag, 0));
        // 新增时新增失败
        if (count == 0 || Objects.equals(count, batchUpdateVO.getNum())) {
            superManager.remove(Wraps.<BaseSecurityCodeBatchDetails>lbQ().eq(BaseSecurityCodeBatchDetails::getBatchId, batch.getId()));
            saveBatch(baseSecurityCodeBatchManager.getById(batch.getId()));
            return;
        }
        // 判断数量和是否子母码，是否发生改变
        if (!Objects.equals(batch.getNum(), batchUpdateVO.getNum()) || !Objects.equals(batch.getIsSeries(), batchUpdateVO.getIsSeries()) || !Objects.equals(batch.getSeriesRatio(), batchUpdateVO.getSeriesRatio())) {
            superManager.remove(Wraps.<BaseSecurityCodeBatchDetails>lbQ().eq(BaseSecurityCodeBatchDetails::getBatchId, batch.getId()));
            saveBatch(baseSecurityCodeBatchManager.getById(batch.getId()));
            return;
        }
        LbUpdateWrap<BaseSecurityCodeBatchDetails> wrap = Wraps.<BaseSecurityCodeBatchDetails>lbU();
        if (!StringUtils.equals(batchUpdateVO.getLinkUrl(), batch.getLinkUrl())) {
            wrap.setSql("replace(url, '" + batch.getLinkUrl() + "', '" + batchUpdateVO.getLinkUrl() + "')");
        }
        if (!StringUtils.equals(batchUpdateVO.getBigLinkUrl(), batch.getBigLinkUrl())) {
            wrap.setSql("replace(bigUrl, '" + batch.getBigLinkUrl() + "', '" + batchUpdateVO.getBigLinkUrl() + "')");
        }
        wrap.set(BaseSecurityCodeBatchDetails::getLinkType, batch.getLinkType());
        wrap.set(BaseSecurityCodeBatchDetails::getBigLinkType, batch.getBigLinkType());
        wrap.eq(BaseSecurityCodeBatchDetails::getBatchId, batch.getId());
        superManager.update(wrap);
        baseSecurityCodeBatchManager.update(Wraps.<BaseSecurityCodeBatch>lbU()
                .set(BaseSecurityCodeBatch::getState, SecurityCodeBatchStatusEnum.GENERATED.getCode())
                .set(BaseSecurityCodeBatch::getStateDesc, "")
                .eq(BaseSecurityCodeBatch::getId, batch.getId()));
        // 处理重复的内容
        handleRepeatCode(batch.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void importSecurityCode(Long batchId) {
        // 判断有没有商品
        BaseSecurityCodeBatch manager = baseSecurityCodeBatchManager.getById(batchId);
        baseSecurityCodeManager.remove(Wraps.<BaseSecurityCode>lbQ().eq(BaseSecurityCode::getBatchId, batchId));

        // 处理重复的防伪码
        List<String> bigRepeatCodeList = superManager.getBigRepeatCode(batchId);
        List<String> repeatCodeList = superManager.getRepeatCode(batchId);
        // 判断有没有重复的
        if (CollectionUtils.isNotEmpty(bigRepeatCodeList) || CollectionUtils.isNotEmpty(repeatCodeList)) {
            baseSecurityCodeBatchManager.update(Wraps.<BaseSecurityCodeBatch>lbU()
                    .set(BaseSecurityCodeBatch::getImportState, SecurityCodeBatchImportEnum.IMPORT_FAIL.getCode())
                    .set(BaseSecurityCodeBatch::getImportStateDesc, "防伪码存在重复， 请编辑后重新导入")
                    .eq(BaseSecurityCodeBatch::getId, batchId));
            return;
        }

        // 每次500条， 循环导入
        try {
            // 查询所有大码
            List<BaseSecurityCodeBatchDetailsResultVO> bigCodeList = superManager.getAllBigCode(batchId);
            if (CollUtil.isNotEmpty(bigCodeList)) {
                BaseSecurityCode baseSecurityCode = baseSecurityCodeManager.getOne(Wraps.<BaseSecurityCode>lbQ().orderByDesc(BaseSecurityCode::getCode).last("limit 1"));
                long start = (Objects.nonNull(baseSecurityCode) && LocalDateTimeUtil.isSameDay(baseSecurityCode.getCreatedTime(), LocalDateTimeUtil.now())) ? Long.parseLong(baseSecurityCode.getCode().substring(6)) : 0;
                List<String> codeList = getCode((int) bigCodeList.size(), start);
                List<BaseSecurityCode> securityCodeList = new ArrayList<>();
                for (int i = 0; i < bigCodeList.size(); i++) {
                    BaseSecurityCodeBatchDetailsResultVO batchDetails = bigCodeList.get(i);
                    securityCodeList.add(BaseSecurityCode.builder()
                            .code(codeList.get(i))
                            .batchId(batchDetails.getBatchId())
                            .batchCode(manager.getBatchCode())
                            .securityCode(batchDetails.getBigSecurityCode())
                            .bigCode("-")
                            .billDate(Objects.nonNull(manager.getProductId()) ? LocalDateTime.now() : null)
                            .url(batchDetails.getBigUrl())
                            .linkType(batchDetails.getBigLinkType())
                            .bindUser(Objects.nonNull(manager.getProductId()) ? ContextUtil.getEmployeeId() : null)
                            .productId(manager.getProductId())
                            .status(Objects.nonNull(manager.getProductId()) ? SecurityCodeStatusEnum.BIND.getCode() : SecurityCodeStatusEnum.IMPORT.getCode())
                            .selectNum(0)
                            .build());
                }
                baseSecurityCodeManager.saveBatch(securityCodeList);
            }
            long count = superManager.count(Wraps.<BaseSecurityCodeBatchDetails>lbQ().eq(BaseSecurityCodeBatchDetails::getBatchId, batchId));
            BaseSecurityCode baseSecurityCode = baseSecurityCodeManager.getOne(Wraps.<BaseSecurityCode>lbQ().orderByDesc(BaseSecurityCode::getCode).last("limit 1"));
            long start = (Objects.nonNull(baseSecurityCode) && LocalDateTimeUtil.isSameDay(baseSecurityCode.getCreatedTime(), LocalDateTimeUtil.now())) ? Long.parseLong(baseSecurityCode.getCode().substring(6)) : 0;
            List<String> codeList = getCode((int) count, start);

            while (true) {
                if (codeList.isEmpty()) {
                    break;
                }
                // codeList 获取前500条数据， 删除 codeList中 获取的前500条
                List<String> codes = codeList.subList(0, Math.min(codeList.size(), 500));

                List<BaseSecurityCodeBatchDetails> batchDetailsList = superManager.list(Wraps.<BaseSecurityCodeBatchDetails>lbQ()
                        .eq(BaseSecurityCodeBatchDetails::getBatchId, batchId).last("limit 500"));
                if (batchDetailsList.isEmpty()) {
                    break;
                }
                // 批量导入
                List<BaseSecurityCode> securityCodeList = new ArrayList<>();
                for (int i = 0; i < batchDetailsList.size(); i++) {
                    BaseSecurityCodeBatchDetails batchDetails = batchDetailsList.get(i);
                    securityCodeList.add(BaseSecurityCode.builder()
                            .code(codes.get(i))
                            .batchId(batchDetails.getBatchId())
                            .batchCode(manager.getBatchCode())
                            .securityCode(batchDetails.getSecurityCode())
                            .bigCode(batchDetails.getBigSecurityCode())
                            .billDate(Objects.nonNull(manager.getProductId()) ? LocalDateTime.now() : null)
                            .url(batchDetails.getUrl())
                            .linkType(batchDetails.getLinkType())
                            .bindUser(Objects.nonNull(manager.getProductId()) ? ContextUtil.getEmployeeId() : null)
                            .productId(manager.getProductId())
                            .status(Objects.nonNull(manager.getProductId()) ? SecurityCodeStatusEnum.BIND.getCode() : SecurityCodeStatusEnum.IMPORT.getCode())
                            .selectNum(0)
                            .build());

                }
                codeList.removeAll(codes);
                baseSecurityCodeManager.saveBatch(securityCodeList);
            }
            // 修改状态为已经导入
            baseSecurityCodeBatchManager.update(Wraps.<BaseSecurityCodeBatch>lbU()
                    .set(BaseSecurityCodeBatch::getImportState, SecurityCodeBatchImportEnum.IMPORTED.getCode())
                    .set(BaseSecurityCodeBatch::getImportStateDesc, "")
                    .eq(BaseSecurityCodeBatch::getId, batchId));
        } catch (Exception e) {
            log.error("导入失败,失败原因", e);
            String fullError = e.getClass().getSimpleName() + ": " + e.getMessage();
            //String rootCause = Objects.requireNonNull(NestedExceptionUtils.getRootCause(e)).getMessage();
            // 导入失败， 删除导入的内容
            baseSecurityCodeBatchManager.update(Wraps.<BaseSecurityCodeBatch>lbU()
                    .set(BaseSecurityCodeBatch::getImportState, SecurityCodeBatchImportEnum.IMPORT_FAIL.getCode())
                    .set(BaseSecurityCodeBatch::getImportStateDesc, fullError)
                    .eq(BaseSecurityCodeBatch::getId, batchId));
            baseSecurityCodeManager.remove(Wraps.<BaseSecurityCode>lbQ().eq(BaseSecurityCode::getBatchId, batchId));
        }
    }

    /**
     * 处理重复的生成码
     *
     * @param batchId
     */
    public void handleRepeatCode(Long batchId) {

        List<String> bigRepeatCodeList = superManager.getBigRepeatCode(batchId);
        List<String> repeatCodeList = superManager.getRepeatCode(batchId);
        // 判断有没有重复的
        if (repeatCodeList.isEmpty() && bigRepeatCodeList.isEmpty()) {
            return;
        }
        baseSecurityCodeBatchManager.update(Wraps.<BaseSecurityCodeBatch>lbU()
                .set(BaseSecurityCodeBatch::getState, SecurityCodeBatchStatusEnum.GENERATING.getCode())
                .set(BaseSecurityCodeBatch::getStateDesc, "")
                .eq(BaseSecurityCodeBatch::getId, batchId));
        if (repeatCodeList.size() >= 500 || bigRepeatCodeList.size() >= 500) {
            superManager.remove(Wraps.<BaseSecurityCodeBatchDetails>lbQ().eq(BaseSecurityCodeBatchDetails::getBatchId, batchId));
            BaseSecurityCodeBatch manager = baseSecurityCodeBatchManager.getById(batchId);
            saveBatch(manager);
            baseSecurityCodeBatchManager.update(Wraps.<BaseSecurityCodeBatch>lbU()
                    .set(BaseSecurityCodeBatch::getState, SecurityCodeBatchStatusEnum.GENERATED.getCode())
                    .set(BaseSecurityCodeBatch::getStateDesc, "")
                    .eq(BaseSecurityCodeBatch::getId, batchId));
            return;
        }
        if (CollUtil.isNotEmpty(bigRepeatCodeList)) {
            BaseSecurityCodeBatch batch = baseSecurityCodeBatchManager.getById(batchId);
            if (Objects.nonNull(batch) && batch.getIsSeries()) {
                List<BaseSecurityCodeBatchDetails> batchDetailsList = superManager.list(Wraps.<BaseSecurityCodeBatchDetails>lbQ().eq(BaseSecurityCodeBatchDetails::getBatchId, batchId)
                        .in(BaseSecurityCodeBatchDetails::getBigSecurityCode, bigRepeatCodeList));
                List<String> sourceBigCodeList = batchDetailsList.stream().map(BaseSecurityCodeBatchDetails::getSecurityCode).distinct().collect(Collectors.toList());
                superManager.remove(Wraps.<BaseSecurityCodeBatchDetails>lbQ().eq(BaseSecurityCodeBatchDetails::getBatchId, batchId).in(BaseSecurityCodeBatchDetails::getSecurityCode, sourceBigCodeList));
                batch.setNum((long) batch.getSeriesRatio() * sourceBigCodeList.size());
                saveBatch(batch);
                baseSecurityCodeBatchManager.update(Wraps.<BaseSecurityCodeBatch>lbU()
                        .set(BaseSecurityCodeBatch::getState, SecurityCodeBatchStatusEnum.GENERATED.getCode())
                        .set(BaseSecurityCodeBatch::getStateDesc, "")
                        .eq(BaseSecurityCodeBatch::getId, batchId));
            }
        }
        if (CollUtil.isNotEmpty(repeatCodeList)) {
            BaseSecurityCodeBatch batch = baseSecurityCodeBatchManager.getById(batchId);
            if (Objects.nonNull(batch) && batch.getIsSeries()) {
                List<BaseSecurityCodeBatchDetails> batchDetailsList = superManager.list(Wraps.<BaseSecurityCodeBatchDetails>lbQ().eq(BaseSecurityCodeBatchDetails::getBatchId, batchId).in(BaseSecurityCodeBatchDetails::getSecurityCode, repeatCodeList));
                List<String> sourceBigCodeList = batchDetailsList.stream().map(BaseSecurityCodeBatchDetails::getBigSecurityCode).distinct().collect(Collectors.toList());
                superManager.remove(Wraps.<BaseSecurityCodeBatchDetails>lbQ().eq(BaseSecurityCodeBatchDetails::getBatchId, batchId).in(BaseSecurityCodeBatchDetails::getBigSecurityCode, sourceBigCodeList));
                batch.setNum((long) batch.getSeriesRatio() * repeatCodeList.size());
            } else {
                superManager.remove(Wraps.<BaseSecurityCodeBatchDetails>lbQ().eq(BaseSecurityCodeBatchDetails::getBatchId, batchId).in(BaseSecurityCodeBatchDetails::getSecurityCode, repeatCodeList));
                batch.setNum((long) repeatCodeList.size());
            }
            saveBatch(batch);
            baseSecurityCodeBatchManager.update(Wraps.<BaseSecurityCodeBatch>lbU()
                    .set(BaseSecurityCodeBatch::getState, SecurityCodeBatchStatusEnum.GENERATED.getCode())
                    .set(BaseSecurityCodeBatch::getStateDesc, "")
                    .eq(BaseSecurityCodeBatch::getId, batchId));
        }

    }


    /**
     * 获取大码集合
     *
     * @param prefix
     * @param totalNum
     * @return
     */
    protected static List<String> getBigCode(String prefix, long totalNum, long startNum) {
        List<String> data = new ArrayList<>();
        for (int i = 1; i <= totalNum; i++) {
            data.add(getBigCodeStr(data, prefix, startNum));
        }
        return data;
    }

    protected static String getBigCodeStr(List<String> users, String prefix, long startValue) {
        int digit = 8;
        int num = (int) ((Math.random() * (99999999 - startValue) + startValue));
        String format = String.format("%0" + digit + "d", num);
        String concat = prefix.concat(format);
        if (users.contains(concat)) {
            return getBigCodeStr(users, prefix, startValue);
        }
        return concat;
    }


    /**
     * 生成序列号
     *
     * @param j          生成数量
     * @param startValue 指定开始数值(随机值的开始值)
     * @return
     */
    protected static List<String> getCode(int j, long startValue) {
        List<String> users = new ArrayList<>();
        for (int i = 0; i < j; i++) {
            users.add(getCodeStr(users, startValue));
        }
        return users;
    }

    protected static String getCodeStr(List<String> users, long startValue) {
        int digit = 7;
        int num = (int) ((Math.random() * (9999999 - startValue) + startValue));
        String format = String.format("%0" + digit + "d", num);
        String concat = DateUtil.format(new Date(), "yyMMdd").concat(format);
        if (users.contains(concat)) {
            return getCodeStr(users, startValue);
        }
        return concat;
    }


    protected static List<String> getSecurityCode(int j, long startValue) {
        List<String> securityCodeList = new ArrayList<>();
        String pre = DateUtil.format(new Date(), "ddMMyy");
        for (int i = 0; i < j; i++) {
            String str = IdUtil.getSnowflake().nextIdStr();
            int random7 = (int) ((Math.random() * (9999999 - startValue) + startValue));
            String substring = str.substring(str.length() - 10);
            substring = substring.concat(String.valueOf(random7));
            substring = substring.substring(substring.length() - 12);
            if (!securityCodeList.contains(pre.concat(substring))) {
                securityCodeList.add(pre.concat(substring));
            }
        }
        return securityCodeList;
    }


}


