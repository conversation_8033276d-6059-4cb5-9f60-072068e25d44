package top.kx.kxss.base.service.biz.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.jackson.JsonUtil;
import top.kx.basic.log.util.AddressUtil;
import top.kx.kxss.base.entity.biz.BaseBizLog;
import top.kx.kxss.base.manager.biz.BaseBizLogManager;
import top.kx.kxss.base.service.biz.BaseBizLogService;
import top.kx.kxss.base.vo.query.biz.BaseBizLogPageQuery;
import top.kx.kxss.base.vo.result.biz.BaseBizLogResultVO;
import top.kx.kxss.base.vo.save.biz.BaseBizLogSaveVO;
import top.kx.kxss.base.vo.save.biz.BizLogSaveVO;
import top.kx.kxss.base.vo.update.biz.BaseBizLogUpdateVO;
import top.kx.kxss.model.enumeration.base.SecurityCodeStatusEnum;
import top.kx.kxss.model.enumeration.base.SecurityCodeTypeEnum;

import javax.servlet.http.HttpServletRequest;
import java.util.stream.Collectors;

/**
 * <p>
 * 业务实现类
 * 防伪码操作日志
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-29 14:02:40
 * @create [2023-05-29 14:02:40] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BaseBizLogServiceImpl extends SuperServiceImpl<BaseBizLogManager, Long, BaseBizLog, BaseBizLogSaveVO,
        BaseBizLogUpdateVO, BaseBizLogPageQuery, BaseBizLogResultVO> implements BaseBizLogService {


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveLog(BizLogSaveVO saveVO) {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();
        String tempIp = ServletUtil.getClientIP(request);
        String tempLocation = AddressUtil.getRegion(tempIp);
        save(BaseBizLogSaveVO.builder()
                .content(JsonUtil.toJson(saveVO.getSecurityCode())).status(saveVO.getStatus())
                .desc(saveVO.getDesc()).createdOrgId(ContextUtil.getCurrentCompanyId())
                .type(StrUtil.isNotBlank(saveVO.getType()) ? saveVO.getType() : SecurityCodeTypeEnum.HANDINPUT.getCode())
                .status(StrUtil.isNotBlank(saveVO.getStatus()) ? saveVO.getStatus() : SecurityCodeStatusEnum.SELECT.getCode())
                .securityCode(saveVO.getSecurityCode().getSecurityCode())
                .location(tempLocation).build());

    }

    @Override
    public void saveBatchLog(BizLogSaveVO saveVO) {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();
        String tempIp = ServletUtil.getClientIP(request);
        String tempLocation = AddressUtil.getRegion(tempIp);
        saveBatch(saveVO.getSecurityCodeList().stream().map(s-> BaseBizLog.builder()
                .content(JsonUtil.toJson(s)).status(saveVO.getStatus())
                .desc(saveVO.getDesc()).createdOrgId(ContextUtil.getCurrentCompanyId())
                .type(StrUtil.isNotBlank(saveVO.getType()) ? saveVO.getType() : SecurityCodeTypeEnum.HANDINPUT.getCode())
                .status(StrUtil.isNotBlank(saveVO.getStatus()) ? saveVO.getStatus() : SecurityCodeStatusEnum.SELECT.getCode())
                .securityCode(s.getSecurityCode())
                .location(tempLocation).build()).collect(Collectors.toList()));

    }
}


