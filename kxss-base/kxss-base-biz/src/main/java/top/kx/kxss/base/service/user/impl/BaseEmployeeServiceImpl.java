package top.kx.kxss.base.service.user.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.entity.SuperEntity;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.base.service.impl.SuperCacheServiceImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.base.entity.user.BaseEmployee;
import top.kx.kxss.base.entity.user.BaseEmployeeOrgRel;
import top.kx.kxss.base.entity.user.BaseEmployeeRoleRel;
import top.kx.kxss.base.manager.user.BaseEmployeeManager;
import top.kx.kxss.base.manager.user.BaseEmployeeOrgRelManager;
import top.kx.kxss.base.manager.user.BaseEmployeeRoleRelManager;
import top.kx.kxss.base.service.user.BaseEmployeeService;
import top.kx.kxss.base.vo.query.user.BaseEmployeePageQuery;
import top.kx.kxss.base.vo.result.user.BaseEmployeeResultVO;
import top.kx.kxss.base.vo.save.user.BaseEmployeeRoleRelSaveVO;
import top.kx.kxss.base.vo.save.user.BaseEmployeeSaveVO;
import top.kx.kxss.base.vo.update.user.BaseEmployeeUpdateVO;
import top.kx.kxss.base.vo.update.user.EmployeeUpdateVO;
import top.kx.kxss.common.cache.base.user.EmployeeOrgCacheKeyBuilder;
import top.kx.kxss.common.cache.base.user.EmployeeRoleCacheKeyBuilder;
import top.kx.kxss.common.constant.BizConstant;
import top.kx.kxss.common.constant.RoleConstant;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 业务实现类
 * 员工
 * </p>
 *
 * <AUTHOR>
 * @date 2021-10-18
 */
@Slf4j
@Service
@RequiredArgsConstructor
//@Transactional(readOnly = true)

public class BaseEmployeeServiceImpl extends SuperCacheServiceImpl<BaseEmployeeManager, Long, BaseEmployee,
        BaseEmployeeSaveVO, BaseEmployeeUpdateVO, BaseEmployeePageQuery, BaseEmployeeResultVO> implements BaseEmployeeService {
    private final BaseEmployeeRoleRelManager baseEmployeeRoleRelManager;
    private final BaseEmployeeOrgRelManager baseEmployeeOrgRelManager;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatch(Collection<BaseEmployee> entityList) {
        return superManager.saveBatch(entityList);
    }

    @Override
    public IPage<BaseEmployeeResultVO> findPageResultVO(PageParams<BaseEmployeePageQuery> params) {
        IPage<BaseEmployee> page = params.buildPage(BaseEmployee.class);
        BaseEmployeePageQuery model = params.getModel();
        LbQueryWrap<BaseEmployee> wrap = Wraps.lbQ();
        wrap.like(BaseEmployee::getRealName, model.getRealName())
                .in(BaseEmployee::getPositionStatus, model.getPositionStatus())
                .in(BaseEmployee::getPositionId, model.getPositionId())
                .eq(BaseEmployee::getActiveStatus, model.getActiveStatus())
                .eq(BaseEmployee::getState, model.getState())
                .eq(SuperEntity::getDeleteFlag, 0)
                .in(BaseEmployee::getUserId, model.getUserIdList())
                .inSql(CollUtil.isNotEmpty(model.getOrgIdList()), BaseEmployee::getId,
                        " select eor.employee_id from base_employee_org_rel eor where eor.employee_id = e.id " +
                                "  and eor.org_id in ( " + StrUtil.join(",", model.getOrgIdList()) + " )  ")
        ;
        if (Objects.nonNull(model.getIsDistributor())) {
            if (model.getIsDistributor()) {
                // 查询知识经销商的员工信息
                wrap.inSql(BaseEmployee::getId, "select distinct berr.employee_id from base_employee_role_rel berr left join base_role br on berr.role_id = br.id where berr.delete_flag = 0 and br.delete_flag = 0 and br.code = 'DISTRIBUTOR'");
            } else {
                // 没有角色或者角色不只是经销商的员工
                wrap.and(s -> {
                    s.notExists("SELECT 1 FROM base_employee_role_rel berr WHERE berr.employee_id = e.id AND berr.delete_flag = 0")
                            .or()
                            .exists("SELECT 1 FROM base_employee_role_rel berr JOIN base_role br ON berr.role_id = br.id AND br.delete_flag = 0 WHERE berr.delete_flag = 0 AND berr.employee_id = e.id AND br.code != 'DISTRIBUTOR'");
                });
            }
        }

        if (StrUtil.equalsAny(model.getScope(), BizConstant.SCOPE_BIND, BizConstant.SCOPE_UN_BIND) && model.getRoleId() != null) {
            String sql = " select err.employee_id from base_employee_role_rel err where err.employee_id = e.id \n" +
                    "  and err.role_id =   " + model.getRoleId();
            if (BizConstant.SCOPE_BIND.equals(model.getScope())) {
                wrap.inSql(BaseEmployee::getId, sql);
            } else {
                wrap.notInSql(BaseEmployee::getId, sql);
            }
        }

        return superManager.selectPageResultVO(page, wrap);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Long> saveEmployeeRole(BaseEmployeeRoleRelSaveVO saveVO) {
        if (saveVO.getFlag() == null) {
            saveVO.setFlag(true);
        }

        baseEmployeeRoleRelManager.remove(Wraps.<BaseEmployeeRoleRel>lbQ().eq(BaseEmployeeRoleRel::getEmployeeId, saveVO.getEmployeeId())
                .in(BaseEmployeeRoleRel::getRoleId, saveVO.getRoleIdList()));

        if (saveVO.getFlag() && CollUtil.isNotEmpty(saveVO.getRoleIdList())) {
            List<BaseEmployeeRoleRel> list = saveVO.getRoleIdList().stream()
                    .map(roleId -> BaseEmployeeRoleRel.builder()
                            .roleId(roleId).employeeId(saveVO.getEmployeeId())
                            .build()).collect(Collectors.toList());
            baseEmployeeRoleRelManager.saveBatch(list);
        }

        cacheOps.del(EmployeeRoleCacheKeyBuilder.build(saveVO.getEmployeeId()));
        return findEmployeeRoleByEmployeeId(saveVO.getEmployeeId());
    }

    @Override
    public List<Long> findEmployeeRoleByEmployeeId(Long employeeId) {
        return baseEmployeeRoleRelManager.listObjs(Wrappers.<BaseEmployeeRoleRel>lambdaQuery()
                        .select(BaseEmployeeRoleRel::getRoleId)
                        .eq(BaseEmployeeRoleRel::getEmployeeId, employeeId),
                Convert::toLong
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseEmployee save(BaseEmployeeSaveVO saveVO) {
        if (ObjectUtil.isNotNull(saveVO.getAvatarFile())) {
            saveVO.setAvatarId(saveVO.getAvatarFile().getId());
        }
        BaseEmployee baseEmployee = BeanUtil.toBean(saveVO, BaseEmployee.class);
        baseEmployee.setCreatedOrgId(ContextUtil.getCurrentDeptId());
        superManager.save(baseEmployee);
        List<Long> orgIdList = saveVO.getOrgIdList();
        saveEmployeeOrg(baseEmployee, orgIdList);
        return baseEmployee;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseEmployee updateById(BaseEmployeeUpdateVO updateVO) {
        if (ObjectUtil.isNotNull(updateVO.getAvatarFile())) {
            updateVO.setAvatarId(updateVO.getAvatarFile().getId());
        }
        BaseEmployee baseEmployee = BeanUtil.toBean(updateVO, BaseEmployee.class);
        superManager.updateById(baseEmployee);
        List<Long> orgIdList = updateVO.getOrgIdList();

        saveEmployeeOrg(baseEmployee, orgIdList);
        return baseEmployee;
    }

    private void saveEmployeeOrg(BaseEmployee baseEmployee, List<Long> orgIdList) {
        baseEmployeeOrgRelManager.removeByEmployeeId(baseEmployee.getId());
        if (CollUtil.isNotEmpty(orgIdList)) {
            List<BaseEmployeeOrgRel> eoList = orgIdList.stream().map(orgId ->
                    BaseEmployeeOrgRel.builder()
                            .employeeId(baseEmployee.getId())
                            .orgId(orgId)
                            .build()).collect(Collectors.toList());
            baseEmployeeOrgRelManager.saveBatch(eoList);
        }

        cacheOps.del(EmployeeOrgCacheKeyBuilder.build(baseEmployee.getId()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByIds(Collection<Long> idList) {
        boolean flag = superManager.removeByIds(idList);
        baseEmployeeOrgRelManager.removeByEmployeeIds(idList);
        baseEmployeeRoleRelManager.removeByEmployeeIds(idList);
        return flag;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatchBaseEmployeeAndRole(List<BaseEmployee> employeeList) {
        ArgumentAssert.notEmpty(employeeList, "员工列表不能为空");
        superManager.saveBatch(employeeList);

        List<Long> employeeIdList = employeeList.stream().map(BaseEmployee::getId).collect(Collectors.toList());
        return baseEmployeeRoleRelManager.bindRole(employeeIdList, RoleConstant.TENANT_ADMIN);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateById(BaseEmployee baseEmployee) {
        return superManager.updateById(baseEmployee);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateAllById(BaseEmployee baseEmployee) {
        return superManager.updateAllById(baseEmployee);
    }

    @Override
    public BaseEmployee getEmployeeByUser(Long userId) {
        return superManager.getEmployeeByUser(userId);
    }

    @Override
    public List<BaseEmployeeResultVO> listEmployeeByUserId(Long userId) {
        return superManager.listEmployeeByUserId(userId);
    }

    @Override
    public Map<Serializable, Object> findByIds(Set<Serializable> ids) {
        return superManager.findByIds(ids.stream().map(Convert::toLong).collect(Collectors.toSet()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateEmployee(EmployeeUpdateVO updateVO) {
        if (ObjectUtil.isNotNull(updateVO.getAvatarFile())) {
            updateVO.setAvatarId(updateVO.getAvatarFile().getId());
        }
        Long employeeId = ContextUtil.getEmployeeId();
        BaseEmployee employee = superManager.getById(employeeId);
        ArgumentAssert.notNull(employee, "员工不存在");
        employee.setRealName(updateVO.getRealName());
        employee.setAvatarId(updateVO.getAvatarId());
        return superManager.updateById(employee);
    }
}
