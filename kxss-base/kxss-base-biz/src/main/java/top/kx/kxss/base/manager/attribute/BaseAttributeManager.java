package top.kx.kxss.base.manager.attribute;

import top.kx.basic.base.manager.SuperManager;
import top.kx.basic.interfaces.echo.LoadService;
import top.kx.kxss.base.entity.attribute.BaseAttribute;

/**
 * <p>
 * 通用业务接口
 * 商品基础属性
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-16 18:44:16
 * @create [2025-06-16 18:44:16] [yan] [代码生成器生成]
 */
public interface BaseAttributeManager extends SuperManager<BaseAttribute>, LoadService {

}


