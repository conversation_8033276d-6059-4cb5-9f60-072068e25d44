package top.kx.kxss.base.manager;

import top.kx.basic.base.manager.SuperManager;
import top.kx.kxss.base.entity.BaseSecurityCodeAttributeDetails;

/**
 * <p>
 * 通用业务接口
 * 防伪信息规格属性
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-17 10:23:59
 * @create [2025-06-17 10:23:59] [yan] [代码生成器生成]
 */
public interface BaseSecurityCodeAttributeDetailsManager extends SuperManager<BaseSecurityCodeAttributeDetails> {

}


