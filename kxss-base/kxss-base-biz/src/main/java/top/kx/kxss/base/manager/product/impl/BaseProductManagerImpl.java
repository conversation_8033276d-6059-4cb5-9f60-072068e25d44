package top.kx.kxss.base.manager.product.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.manager.impl.SuperCacheManagerImpl;
import top.kx.basic.model.cache.CacheKeyBuilder;
import top.kx.basic.utils.CollHelper;
import top.kx.kxss.base.entity.product.BaseProduct;
import top.kx.kxss.base.manager.product.BaseProductManager;
import top.kx.kxss.base.mapper.product.BaseProductMapper;
import top.kx.kxss.common.cache.base.user.ProductCacheKeyBuilder;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 通用业务实现类
 * 商品表
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-25 13:52:40
 * @create [2023-05-25 13:52:40] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BaseProductManagerImpl extends SuperCacheManagerImpl<BaseProductMapper, BaseProduct> implements BaseProductManager {


    @Transactional(readOnly = true)
    @Override
    public Map<Serializable, Object> findByIds(Set<Serializable> ids) {
        List<BaseProduct> list = findByIds(ids, null).stream().filter(Objects::nonNull).collect(Collectors.toList());
        return CollHelper.uniqueIndex(list, BaseProduct::getId, BaseProduct::getName);
    }

    @Override
    protected CacheKeyBuilder cacheKeyBuilder() {
        return new ProductCacheKeyBuilder();
    }

}


