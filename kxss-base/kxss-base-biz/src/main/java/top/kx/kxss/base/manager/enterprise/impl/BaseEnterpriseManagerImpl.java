package top.kx.kxss.base.manager.enterprise.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.manager.impl.SuperCacheManagerImpl;
import top.kx.basic.model.cache.CacheKeyBuilder;
import top.kx.basic.utils.CollHelper;
import top.kx.kxss.base.entity.enterprise.BaseEnterprise;
import top.kx.kxss.base.manager.enterprise.BaseEnterpriseManager;
import top.kx.kxss.base.mapper.enterprise.BaseEnterpriseMapper;
import top.kx.kxss.common.cache.base.enterprise.EnterpriseCacheKeyBuilder;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 通用业务实现类
 * 企业
 * </p>
 *
 * <AUTHOR>
 * @date 2025-04-25 17:45:11
 * @create [2025-04-25 17:45:11] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BaseEnterpriseManagerImpl extends SuperCacheManagerImpl<BaseEnterpriseMapper, BaseEnterprise> implements BaseEnterpriseManager {

    @Override
    protected CacheKeyBuilder cacheKeyBuilder() {
        return new EnterpriseCacheKeyBuilder();
    }

    @Transactional(readOnly = true)
    @Override
    public Map<Serializable, Object> findByIds(Set<Serializable> ids) {
        List<BaseEnterprise> list = findByIds(ids, null).stream().filter(Objects::nonNull).collect(Collectors.toList());
        return CollHelper.uniqueIndex(list, BaseEnterprise::getId, BaseEnterprise::getName);
    }
}


