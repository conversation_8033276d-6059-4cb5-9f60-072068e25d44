package top.kx.kxss.base.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.kxss.base.service.BaseSecurityCodeAttributeDetailsService;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.kxss.base.manager.BaseSecurityCodeAttributeDetailsManager;
import top.kx.kxss.base.entity.BaseSecurityCodeAttributeDetails;
import top.kx.kxss.base.vo.save.BaseSecurityCodeAttributeDetailsSaveVO;
import top.kx.kxss.base.vo.update.BaseSecurityCodeAttributeDetailsUpdateVO;
import top.kx.kxss.base.vo.result.BaseSecurityCodeAttributeDetailsResultVO;
import top.kx.kxss.base.vo.query.BaseSecurityCodeAttributeDetailsPageQuery;

/**
 * <p>
 * 业务实现类
 * 防伪信息规格属性
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-17 10:23:59
 * @create [2025-06-17 10:23:59] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
@Transactional(readOnly = true)
public class BaseSecurityCodeAttributeDetailsServiceImpl extends SuperServiceImpl<BaseSecurityCodeAttributeDetailsManager, Long, BaseSecurityCodeAttributeDetails, BaseSecurityCodeAttributeDetailsSaveVO,
    BaseSecurityCodeAttributeDetailsUpdateVO, BaseSecurityCodeAttributeDetailsPageQuery, BaseSecurityCodeAttributeDetailsResultVO> implements BaseSecurityCodeAttributeDetailsService {


}


