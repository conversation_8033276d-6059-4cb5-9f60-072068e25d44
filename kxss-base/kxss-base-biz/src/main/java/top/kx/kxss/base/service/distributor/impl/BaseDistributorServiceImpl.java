package top.kx.kxss.base.service.distributor.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.entity.SuperEntity;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.basic.utils.TreeUtil;
import top.kx.kxss.base.entity.distributor.BaseDistributor;
import top.kx.kxss.base.entity.purchase.BasePurchase;
import top.kx.kxss.base.entity.system.BaseRole;
import top.kx.kxss.base.entity.user.BaseEmployee;
import top.kx.kxss.base.manager.distributor.BaseDistributorManager;
import top.kx.kxss.base.manager.purchase.BasePurchaseManager;
import top.kx.kxss.base.service.distributor.BaseDistributorService;
import top.kx.kxss.base.service.system.BaseRoleService;
import top.kx.kxss.base.service.user.BaseEmployeeService;
import top.kx.kxss.base.vo.query.distributor.BaseDistributorPageQuery;
import top.kx.kxss.base.vo.result.distributor.BaseDistributorResultVO;
import top.kx.kxss.base.vo.result.distributor.BaseDistributorTreeResultVO;
import top.kx.kxss.base.vo.save.distributor.BaseDistributorSaveVO;
import top.kx.kxss.base.vo.save.system.RoleEmployeeSaveVO;
import top.kx.kxss.base.vo.save.user.BaseEmployeeSaveVO;
import top.kx.kxss.base.vo.update.distributor.BaseDistributorUpdateVO;
import top.kx.kxss.common.constant.RoleConstant;
import top.kx.kxss.model.enumeration.base.ActiveStatusEnum;
import top.kx.kxss.model.enumeration.base.EmployeeTypeEnum;
import top.kx.kxss.system.entity.tenant.DefUser;
import top.kx.kxss.system.service.tenant.DefUserService;
import top.kx.kxss.system.vo.save.tenant.DefUserSaveVO;
import top.kx.kxss.system.vo.update.tenant.UserMobileUpdateVO;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 业务实现类
 * 耗材经销商信息
 * </p>
 *
 * <AUTHOR>
 * @date 2025-03-07 15:45:07
 * @create [2025-03-07 15:45:07] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
//@Transactional(readOnly = true)
public class BaseDistributorServiceImpl extends SuperServiceImpl<BaseDistributorManager, Long, BaseDistributor, BaseDistributorSaveVO,
        BaseDistributorUpdateVO, BaseDistributorPageQuery, BaseDistributorResultVO> implements BaseDistributorService {

    private final DefUserService defUserService;
    private final BaseEmployeeService baseEmployeeService;
    private final EchoService echoService;
    private final BaseRoleService baseRoleService;
    private final BasePurchaseManager basePurchaseManager;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseDistributor save(BaseDistributorSaveVO baseDistributorSaveVO) {
        long count = superManager.count(Wraps.<BaseDistributor>lbQ().eq(BaseDistributor::getCode, baseDistributorSaveVO.getCode()));
        ArgumentAssert.isTrue(count <= 0, "经销商编码不能重复");
        long mobileCount = superManager.count(Wraps.<BaseDistributor>lbQ()
                .eq(BaseDistributor::getDeleteFlag, 0)
                .eq(BaseDistributor::getMobile, baseDistributorSaveVO.getMobile()));
        ArgumentAssert.isTrue(mobileCount<= 0, "经销商手机号已经存在，不允许重复添加");
        if (!baseDistributorSaveVO.getIsDistributor()) {
            DefUser defUser = defUserService.getByMobile(baseDistributorSaveVO.getMobile());
            if (Objects.isNull(defUser)) {
                DefUser save = defUserService.save(DefUserSaveVO.builder()
                        .username(baseDistributorSaveVO.getMobile())
                        .sex(baseDistributorSaveVO.getSex())
                        .state(true).username(baseDistributorSaveVO.getMobile())
                        .nickName(baseDistributorSaveVO.getName()).mobile(baseDistributorSaveVO.getMobile())
                        .build());
                addDistributorEmp(baseDistributorSaveVO, save);
                baseDistributorSaveVO.setUserId(save.getId());
            } else {
                BaseEmployee employeeByUser = baseEmployeeService.getEmployeeByUser(defUser.getId());
                if (Objects.isNull(employeeByUser)) {
                    addDistributorEmp(baseDistributorSaveVO, defUser);
                }
                baseDistributorSaveVO.setUserId(defUser.getId());
            }
            baseDistributorSaveVO.setLevel(1);
            baseDistributorSaveVO.setParentId(0L);
        } else {
            BaseDistributor parentDistributor = superManager.getOne(Wraps.<BaseDistributor>lbQ()
                    .eq(BaseDistributor::getDeleteFlag, 0)
                    .eq(BaseDistributor::getUserId, ContextUtil.getUserId())
                    .eq(BaseDistributor::getLevel, 1)
                    .orderByDesc(BaseDistributor::getCreatedTime).last("limit 1"));
            ArgumentAssert.isTrue(Objects.nonNull(parentDistributor), "您不是一级经销商，无法添加下级经销商");
            baseDistributorSaveVO.setUserId(null);
            baseDistributorSaveVO.setLevel(parentDistributor.getLevel() + 1);
            baseDistributorSaveVO.setParentId(parentDistributor.getId());
        }
        return super.save(baseDistributorSaveVO);
    }

    @Override
    public BaseDistributor updateById(BaseDistributorUpdateVO baseDistributorUpdateVO) {
        baseDistributorUpdateVO.setLevel(null);
        if (StringUtils.isNotBlank(baseDistributorUpdateVO.getCode())) {
            long count = superManager.count(Wraps.<BaseDistributor>lbQ().eq(BaseDistributor::getCode, baseDistributorUpdateVO.getCode()).ne(BaseDistributor::getId, baseDistributorUpdateVO.getId()));
            ArgumentAssert.isTrue(count <= 0, "经销商编码不能重复");
        }
        if (StringUtils.isNotBlank(baseDistributorUpdateVO.getMobile())) {
            long mobileCount = superManager.count(Wraps.<BaseDistributor>lbQ()
                    .eq(BaseDistributor::getDeleteFlag, 0)
                    .ne(BaseDistributor::getId, baseDistributorUpdateVO.getId())
                    .eq(BaseDistributor::getMobile, baseDistributorUpdateVO.getMobile()));
            ArgumentAssert.isTrue(mobileCount<= 0, "经销商手机号已经存在，不允许重复添加");
        }
        // 判断有没有修改手机号， 如果有的话， 需要修改登录的手机号
        BaseDistributor distributor = superManager.getById(baseDistributorUpdateVO.getId());
        // 判断员工ID是否存在， 如果没有的话，需要创建员工
        if (Objects.isNull(distributor.getUserId())) {
            ArgumentAssert.isTrue(StringUtils.isNotBlank(baseDistributorUpdateVO.getMobile()), "请填写手机号");
            DefUser defUser = defUserService.getByMobile(baseDistributorUpdateVO.getMobile());
            if (Objects.isNull(defUser)) {
                DefUser save = defUserService.save(DefUserSaveVO.builder()
                        .username(distributor.getMobile())
                        .sex(StringUtils.isNotBlank(baseDistributorUpdateVO.getSex()) ? baseDistributorUpdateVO.getSex() : distributor.getSex())
                        .state(true).username(baseDistributorUpdateVO.getMobile())
                        .nickName(StringUtils.isNotBlank(baseDistributorUpdateVO.getName()) ? baseDistributorUpdateVO.getName() : distributor.getName())
                        .mobile(baseDistributorUpdateVO.getMobile())
                        .build());
                addDistributorEmp(BaseDistributorSaveVO.builder()
                        .name(StringUtils.isNotBlank(baseDistributorUpdateVO.getName()) ? baseDistributorUpdateVO.getName() : distributor.getName())
                        .mobile(baseDistributorUpdateVO.getMobile())
                        .sex(StringUtils.isNotBlank(baseDistributorUpdateVO.getSex()) ? baseDistributorUpdateVO.getSex() : distributor.getSex())
                        .build(), save);
                baseDistributorUpdateVO.setUserId(save.getId());
            } else {
                BaseEmployee employeeByUser = baseEmployeeService.getEmployeeByUser(defUser.getId());
                if (Objects.isNull(employeeByUser)) {
                    addDistributorEmp(BaseDistributorSaveVO.builder()
                            .name(StringUtils.isNotBlank(baseDistributorUpdateVO.getName()) ? baseDistributorUpdateVO.getName() : distributor.getName())
                            .mobile(baseDistributorUpdateVO.getMobile())
                            .sex(StringUtils.isNotBlank(baseDistributorUpdateVO.getSex()) ? baseDistributorUpdateVO.getSex() : distributor.getSex())
                            .build(), defUser);
                }
                baseDistributorUpdateVO.setUserId(defUser.getId());
            }
        } else {
            if (StringUtils.isNotBlank(baseDistributorUpdateVO.getMobile()) && !StringUtils.equals(distributor.getMobile(), baseDistributorUpdateVO.getMobile())) {
                defUserService.updateMobile(UserMobileUpdateVO.builder().mobile(baseDistributorUpdateVO.getMobile()).id(distributor.getUserId()).build());
            }
        }
        return super.updateById(baseDistributorUpdateVO);
    }

    /**
     * 添加经销商员工
     *
     * @param baseDistributorSaveVO
     * @param defUser
     */
    private void addDistributorEmp(BaseDistributorSaveVO baseDistributorSaveVO, DefUser defUser) {
        BaseEmployeeSaveVO saveVO = new BaseEmployeeSaveVO();
        saveVO.setUserId(defUser.getId());
        saveVO.setRealName(baseDistributorSaveVO.getName());
        saveVO.setSex(baseDistributorSaveVO.getSex());
        saveVO.setUsername(baseDistributorSaveVO.getMobile());
        saveVO.setMobile(baseDistributorSaveVO.getMobile());
        saveVO.setActiveStatus(ActiveStatusEnum.ACTIVATED.getCode());
        saveVO.setIsDefault(true);
        saveVO.setType(EmployeeTypeEnum.Distributor.getCode());
        BaseEmployee baseEmployee = baseEmployeeService.save(saveVO);
        BaseRole baseRole = baseRoleService.getOneByCode(RoleConstant.DISTRIBUTOR);
        ArgumentAssert.isTrue(Objects.nonNull(baseRole), "请连续管理员维护经销商角色");
        baseRoleService.saveRoleEmployee(RoleEmployeeSaveVO.builder().flag(true)
                .roleId(baseRole.getId())
                .employeeIdList(Lists.newArrayList(baseEmployee.getId())).build());
    }

    @Override
    public BaseDistributorResultVO getByUserId(Long userId) {
        BaseDistributor baseDistributor = superManager.getOne(Wraps.<BaseDistributor>lbQ()
                .eq(BaseDistributor::getDeleteFlag, 0)
                .eq(BaseDistributor::getUserId, userId)
                .orderByDesc(BaseDistributor::getCreatedTime).last("limit 1"));
        return BeanPlusUtil.toBean(baseDistributor, BaseDistributorResultVO.class);
    }

    @Override
    public Boolean refreshUserId() {
        List<BaseDistributor> baseDistributorList = superManager.list(Wraps.<BaseDistributor>lbQ()
                .eq(SuperEntity::getDeleteFlag, 0)
                .eq(BaseDistributor::getParentId, 0)
                .isNull(BaseDistributor::getUserId));
        if (CollUtil.isEmpty(baseDistributorList)) {
            return false;
        }
        baseDistributorList.forEach(item -> {
            DefUser defUser = defUserService.getByMobile(item.getMobile());
            if (Objects.nonNull(defUser)) {
                item.setUserId(defUser.getId());
                BaseEmployee employeeByUser = baseEmployeeService.getEmployeeByUser(defUser.getId());
                if (Objects.isNull(employeeByUser)) {
                    addDistributorEmp(BeanPlusUtil.toBean(item, BaseDistributorSaveVO.class), defUser);
                }
            } else {
                DefUser save = defUserService.save(DefUserSaveVO.builder()
                        .sex(item.getSex())
                        .state(true).username(item.getMobile())
                        .nickName(item.getName()).mobile(item.getMobile())
                        .build());
                addDistributorEmp(BeanPlusUtil.toBean(item, BaseDistributorSaveVO.class), save);
                item.setUserId(save.getId());
            }
        });
        return superManager.updateBatchById(baseDistributorList);
    }

    @Override
    public List<BaseDistributorTreeResultVO> tree(BaseDistributorPageQuery pageQuery) {
        LbQueryWrap<BaseDistributor> wrap = Wraps.<BaseDistributor>lbQ()
                .eq(SuperEntity::getDeleteFlag, 0)
                .like(StringUtils.isNotBlank(pageQuery.getName()), BaseDistributor::getName, pageQuery.getName())
                .like(StringUtils.isNotBlank(pageQuery.getMobile()), BaseDistributor::getMobile, pageQuery.getMobile())
                .like(StringUtils.isNotBlank(pageQuery.getCode()), BaseDistributor::getCode, pageQuery.getCode())
                .like(StringUtils.isNotBlank(pageQuery.getShopName()), BaseDistributor::getShopName, pageQuery.getShopName());

        if (StringUtils.isNotBlank(pageQuery.getKeyword())) {
            wrap.and(e -> e.like(BaseDistributor::getCode, pageQuery.getKeyword())
                    .or()
                    .like(BaseDistributor::getShopName, pageQuery.getKeyword())
                    .or()
                    .like(BaseDistributor::getName, pageQuery.getKeyword())
                    .or()
                    .like(BaseDistributor::getMobile, pageQuery.getKeyword())
            );
        }
        List<BaseDistributor> list = superManager.list(wrap);
        List<BaseDistributorTreeResultVO> treeList = BeanUtil.copyToList(list, BaseDistributorTreeResultVO.class);
        echoService.action(treeList);
        return TreeUtil.buildTree(treeList);
    }

    @Override
    public Boolean remove(Long id) {
        long count = basePurchaseManager.count(Wraps.<BasePurchase>lbQ().eq(SuperEntity::getDeleteFlag, 0).eq(BasePurchase::getDistributorId, id));
        ArgumentAssert.isTrue(count <= 0, "存在拿货记录，经销商不允许删除");
        return superManager.removeById(id);
    }
}


