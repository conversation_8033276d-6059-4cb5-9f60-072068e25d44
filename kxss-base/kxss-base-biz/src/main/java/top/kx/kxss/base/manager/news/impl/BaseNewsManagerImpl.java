package top.kx.kxss.base.manager.news.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.base.entity.news.BaseNews;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.base.manager.news.BaseNewsManager;
import top.kx.kxss.base.mapper.news.BaseNewsMapper;

/**
 * <p>
 * 通用业务实现类
 * 新闻资讯
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-25 16:12:08
 * @create [2023-05-25 16:12:08] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BaseNewsManagerImpl extends SuperManagerImpl<BaseNewsMapper, BaseNews> implements BaseNewsManager {

}


