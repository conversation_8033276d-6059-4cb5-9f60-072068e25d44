package top.kx.kxss.base.manager.dealers.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.manager.impl.SuperCacheManagerImpl;
import top.kx.basic.model.cache.CacheKeyBuilder;
import top.kx.basic.utils.CollHelper;
import top.kx.kxss.base.entity.dealers.BaseDealers;
import top.kx.kxss.base.manager.dealers.BaseDealersManager;
import top.kx.kxss.base.mapper.dealers.BaseDealersMapper;
import top.kx.kxss.common.cache.base.user.DealersCacheKeyBuilder;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 通用业务实现类
 * 经销商信息
 * </p>
 *
 * <AUTHOR>
 * @date 2024-12-20 10:47:35
 * @create [2024-12-20 10:47:35] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BaseDealersManagerImpl extends SuperCacheManagerImpl<BaseDealersMapper, BaseDealers> implements BaseDealersManager {
    @Transactional(readOnly = true)
    @Override
    public Map<Serializable, Object> findByIds(Set<Serializable> ids) {
        List<BaseDealers> list = findByIds(ids, null).stream().filter(Objects::nonNull).collect(Collectors.toList());
        return CollHelper.uniqueIndex(list, BaseDealers::getId, BaseDealers::getBallRoomName);
    }

    @Override
    protected CacheKeyBuilder cacheKeyBuilder() {
        return new DealersCacheKeyBuilder();
    }

}


