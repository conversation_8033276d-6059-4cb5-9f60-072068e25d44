package top.kx.kxss.base.service.security.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.base.entity.security.BaseSecurityCodeBatch;
import top.kx.kxss.base.entity.security.BaseSecurityCodeBatchDetails;
import top.kx.kxss.base.event.SecurityCodeBatchImportEvent;
import top.kx.kxss.base.event.SecurityCodeBatchSaveEvent;
import top.kx.kxss.base.event.SecurityCodeBatchUpdateEvent;
import top.kx.kxss.base.manager.security.BaseSecurityCodeBatchDetailsManager;
import top.kx.kxss.base.manager.security.BaseSecurityCodeBatchManager;
import top.kx.kxss.base.service.download.BaseDownloadRecordsService;
import top.kx.kxss.base.service.security.BaseSecurityCodeBatchService;
import top.kx.kxss.base.vo.query.security.BaseSecurityCodeBatchPageQuery;
import top.kx.kxss.base.vo.result.security.BaseSecurityCodeBatchDetailsExportResultVO;
import top.kx.kxss.base.vo.result.security.BaseSecurityCodeBatchResultVO;
import top.kx.kxss.base.vo.save.download.BaseDownloadRecordsSaveVO;
import top.kx.kxss.base.vo.save.security.BaseSecurityCodeBatchSaveVO;
import top.kx.kxss.base.vo.update.security.BaseSecurityCodeBatchUpdateVO;
import top.kx.kxss.model.enumeration.base.DownloadSourceEnum;
import top.kx.kxss.model.enumeration.base.SecurityCodeBatchImportEnum;
import top.kx.kxss.model.enumeration.base.SecurityCodeBatchStatusEnum;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 业务实现类
 * 防伪码批次
 * </p>
 *
 * <AUTHOR>
 * @date 2025-04-25 17:45:12
 * @create [2025-04-25 17:45:12] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
//@Transactional(readOnly = true)
public class BaseSecurityCodeBatchServiceImpl extends SuperServiceImpl<BaseSecurityCodeBatchManager, Long, BaseSecurityCodeBatch, BaseSecurityCodeBatchSaveVO,
        BaseSecurityCodeBatchUpdateVO, BaseSecurityCodeBatchPageQuery, BaseSecurityCodeBatchResultVO> implements BaseSecurityCodeBatchService {

    private final ApplicationEventPublisher eventPublisher;
    private final BaseSecurityCodeBatchDetailsManager baseSecurityCodeBatchDetailsManager;
    private final BaseDownloadRecordsService baseDownloadRecordsService;

    @Override
    public BaseSecurityCodeBatch save(BaseSecurityCodeBatchSaveVO baseSecurityCodeBatchSaveVO) {
        if (Objects.nonNull(baseSecurityCodeBatchSaveVO.getIsSeries()) && baseSecurityCodeBatchSaveVO.getIsSeries()) {
            ArgumentAssert.isTrue(Objects.nonNull(baseSecurityCodeBatchSaveVO.getSeriesRatio()) && baseSecurityCodeBatchSaveVO.getSeriesRatio() > 0,
                    "系列码生成时，系列码比例不能为空且大于0");
            ArgumentAssert.isTrue(baseSecurityCodeBatchSaveVO.getNum() % baseSecurityCodeBatchSaveVO.getSeriesRatio() == 0,
                    "系列码生成时，生成数量必须是系列码比例的整数倍");

        }
        BaseSecurityCodeBatch batch = BeanPlusUtil.toBean(baseSecurityCodeBatchSaveVO, BaseSecurityCodeBatch.class);
        batch.setState(SecurityCodeBatchStatusEnum.GENERATING.getCode());
        batch.setImportState(SecurityCodeBatchImportEnum.NOT_IMPORT.getCode());
        superManager.save(batch);
        eventPublisher.publishEvent(new SecurityCodeBatchSaveEvent(this, batch));
        return batch;
    }


    @Override
    public BaseSecurityCodeBatch updateById(BaseSecurityCodeBatchUpdateVO baseSecurityCodeBatchUpdateVO) {
        BaseSecurityCodeBatch batch = superManager.getById(baseSecurityCodeBatchUpdateVO.getId());
        ArgumentAssert.isTrue(!SecurityCodeBatchStatusEnum.GENERATING.getCode().equals(batch.getState()), "生成中的批次不允许修改");
        ArgumentAssert.isTrue(!SecurityCodeBatchImportEnum.IMPORTING.getCode().equals(batch.getImportState()), "已经导入的批次不允许修改");
        ArgumentAssert.isTrue(!SecurityCodeBatchImportEnum.IMPORTED.getCode().equals(batch.getImportState()), "导入中的批次不允许修改");
        BaseSecurityCodeBatch baseSecurityCodeBatch = BeanPlusUtil.copyProperties(baseSecurityCodeBatchUpdateVO, BaseSecurityCodeBatch.class);
        baseSecurityCodeBatch.setState(SecurityCodeBatchStatusEnum.GENERATING.getCode());
        superManager.updateById(baseSecurityCodeBatch);
        eventPublisher.publishEvent(new SecurityCodeBatchUpdateEvent(this, batch, baseSecurityCodeBatchUpdateVO));
        return baseSecurityCodeBatch;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean importSecurityCode(Long batchId) {
        BaseSecurityCodeBatch manager = superManager.getById(batchId);
        ArgumentAssert.isTrue(
                !SecurityCodeBatchImportEnum.IMPORTING.getCode().equals(manager.getImportState()),
                "正在导入中，请勿重复导入"
        );
        ArgumentAssert.isTrue(
                !SecurityCodeBatchImportEnum.IMPORTED.getCode().equals(manager.getImportState()),
                "已导入完成，请勿重复操作"
        );
        BaseSecurityCodeBatch baseSecurityCodeBatch = new BaseSecurityCodeBatch();
        baseSecurityCodeBatch.setId(batchId);
        baseSecurityCodeBatch.setImportState(SecurityCodeBatchImportEnum.IMPORTING.getCode());
        boolean b = superManager.updateById(baseSecurityCodeBatch);
        if (b) {
            eventPublisher.publishEvent(new SecurityCodeBatchImportEvent(this, batchId));
        }
        return b;
    }

    @Override
    public List<BaseSecurityCodeBatchDetailsExportResultVO> exportSecurityCode(Long batchId) {
        List<BaseSecurityCodeBatchDetails> batchDetailsList = baseSecurityCodeBatchDetailsManager.list(Wraps.<BaseSecurityCodeBatchDetails>lbQ().eq(BaseSecurityCodeBatchDetails::getBatchId, batchId));
        List<BaseSecurityCodeBatchDetailsExportResultVO> exportResultVO = BeanPlusUtil.toBeanList(batchDetailsList, BaseSecurityCodeBatchDetailsExportResultVO.class);
        baseDownloadRecordsService.save(BaseDownloadRecordsSaveVO.builder()
                .sourceId(batchId)
                .sourceType(DownloadSourceEnum.BATCH.getCode())
                .employeeId(ContextUtil.getEmployeeId())
                .url(null)
                .num(exportResultVO.size())
                .build());
        return exportResultVO;
    }
}


