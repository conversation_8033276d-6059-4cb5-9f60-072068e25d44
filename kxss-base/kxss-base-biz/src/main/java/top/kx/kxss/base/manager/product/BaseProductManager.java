package top.kx.kxss.base.manager.product;

import top.kx.basic.base.manager.SuperManager;
import top.kx.basic.interfaces.echo.LoadService;
import top.kx.kxss.base.entity.product.BaseProduct;

/**
 * <p>
 * 通用业务接口
 * 商品表
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-25 13:52:40
 * @create [2023-05-25 13:52:40] [dou] [代码生成器生成]
 */
public interface BaseProductManager extends SuperManager<BaseProduct>, LoadService {

}


