package top.kx.kxss.file.utils;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import top.kx.basic.exception.BizException;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

import static top.kx.basic.exception.code.ExceptionCode.BASE_VALID_PARAM;

/**
 * 文件安全工具类
 * 用于检查上传文件的安全性，防止恶意文件上传
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
public class FileSecurityUtil {

    /**
     * 危险文件后缀黑名单
     * 包含可执行文件、脚本文件、系统文件等危险类型
     */
    private static final Set<String> DANGEROUS_EXTENSIONS = new HashSet<>(Arrays.asList(
            // 可执行文件
            "exe", "bat", "cmd", "com", "pif", "scr", "vbs", "vbe", "js", "jse", "wsf", "wsh",
            "msi", "msp", "scf", "lnk", "inf", "reg", "dll", "sys", "drv",
            // 脚本文件
            "jsp", "php", "asp", "aspx", "cer", "csr", "jsp", "jspx", "jsw", "jsv",
            "cfm", "cfml", "cgi", "pl", "py", "rb", "sh", "ps1", "psm1",
            // Java相关
            "jar", "war", "ear", "class",
            // 其他危险文件
            "hta", "application", "gadget", "msc", "mst", "paf", "u3p", "vb", "vbscript"
    ));

    /**
     * 允许的安全文件后缀白名单
     * 包含常见的图片、文档、音视频等安全文件类型
     */
    private static final Set<String> ALLOWED_EXTENSIONS = new HashSet<>(Arrays.asList(
            // 图片文件
            "jpg", "jpeg", "png", "gif", "bmp", "webp", "svg", "ico", "tiff", "tif",
            // 文档文件
            "pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "txt", "rtf", "odt", "ods", "odp",
            // 音频文件
            "mp3", "wav", "flac", "aac", "ogg", "wma", "m4a",
            // 视频文件
            "mp4", "avi", "mkv", "mov", "wmv", "flv", "webm", "m4v", "3gp",
            // 压缩文件
            "zip", "rar", "7z", "tar", "gz", "bz2",
            // 其他常见文件
            "csv", "json", "xml", "log"
    ));

    /**
     * 检查文件名是否安全
     * 
     * @param fileName 文件名
     * @throws BizException 如果文件不安全则抛出异常
     */
    public static void checkFileSecurity(String fileName) {
        if (StrUtil.isBlank(fileName)) {
            throw BizException.wrap(BASE_VALID_PARAM.build("文件名不能为空"));
        }

        // 获取文件后缀名，转换为小写
        String extension = FilenameUtils.getExtension(fileName);
        if (StrUtil.isBlank(extension)) {
            throw BizException.wrap(BASE_VALID_PARAM.build("文件必须包含后缀名"));
        }
        
        extension = extension.toLowerCase();
        
        // 检查是否为危险文件类型
        if (DANGEROUS_EXTENSIONS.contains(extension)) {
            log.warn("检测到危险文件类型上传尝试: {}, 后缀: {}", fileName, extension);
            throw BizException.wrap(BASE_VALID_PARAM.build(
                String.format("不允许上传 .%s 类型的文件，该文件类型存在安全风险", extension)
            ));
        }
        
        // 检查是否为允许的文件类型
        if (!ALLOWED_EXTENSIONS.contains(extension)) {
            log.warn("检测到不支持的文件类型上传尝试: {}, 后缀: {}", fileName, extension);
            throw BizException.wrap(BASE_VALID_PARAM.build(
                String.format("不支持 .%s 类型的文件，请上传支持的文件格式", extension)
            ));
        }
        
        log.debug("文件安全检查通过: {}, 后缀: {}", fileName, extension);
    }

    /**
     * 检查文件后缀是否安全（不抛出异常，返回布尔值）
     * 
     * @param fileName 文件名
     * @return true表示安全，false表示不安全
     */
    public static boolean isFileSafe(String fileName) {
        try {
            checkFileSecurity(fileName);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取文件后缀名（小写）
     * 
     * @param fileName 文件名
     * @return 文件后缀名
     */
    public static String getFileExtension(String fileName) {
        if (StrUtil.isBlank(fileName)) {
            return "";
        }
        String extension = FilenameUtils.getExtension(fileName);
        return StrUtil.isBlank(extension) ? "" : extension.toLowerCase();
    }

    /**
     * 获取支持的文件类型列表（用于前端显示）
     * 
     * @return 支持的文件后缀列表
     */
    public static Set<String> getSupportedExtensions() {
        return new HashSet<>(ALLOWED_EXTENSIONS);
    }

    /**
     * 获取危险文件类型列表
     * 
     * @return 危险文件后缀列表
     */
    public static Set<String> getDangerousExtensions() {
        return new HashSet<>(DANGEROUS_EXTENSIONS);
    }
}