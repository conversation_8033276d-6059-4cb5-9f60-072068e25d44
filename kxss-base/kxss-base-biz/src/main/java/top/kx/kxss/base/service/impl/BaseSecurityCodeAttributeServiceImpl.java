package top.kx.kxss.base.service.impl;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.base.entity.BaseSecurityCode;
import top.kx.kxss.base.entity.BaseSecurityCodeAttribute;
import top.kx.kxss.base.entity.BaseSecurityCodeAttributeDetails;
import top.kx.kxss.base.entity.attribute.BaseAttribute;
import top.kx.kxss.base.entity.product.BaseProduct;
import top.kx.kxss.base.manager.BaseSecurityCodeAttributeDetailsManager;
import top.kx.kxss.base.manager.BaseSecurityCodeAttributeManager;
import top.kx.kxss.base.manager.BaseSecurityCodeManager;
import top.kx.kxss.base.manager.product.BaseProductManager;
import top.kx.kxss.base.service.BaseSecurityCodeAttributeService;
import top.kx.kxss.base.service.attribute.BaseAttributeService;
import top.kx.kxss.base.vo.query.BaseSecurityCodeAttributePageQuery;
import top.kx.kxss.base.vo.result.BaseSecurityCodeAttributeDetailsResultVO;
import top.kx.kxss.base.vo.result.BaseSecurityCodeAttributeResultVO;
import top.kx.kxss.base.vo.save.BaseSecurityCodeAddAttributeVO;
import top.kx.kxss.base.vo.save.BaseSecurityCodeAttributeSaveVO;
import top.kx.kxss.base.vo.update.BaseSecurityCodeAttributeUpdateVO;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 业务实现类
 * 防伪信息规格属性
 * </p>
 *
 * <AUTHOR>
 * @date 2025-02-26 16:17:06
 * @create [2025-02-26 16:17:06] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
//@Transactional(readOnly = true)
public class BaseSecurityCodeAttributeServiceImpl extends SuperServiceImpl<BaseSecurityCodeAttributeManager, Long, BaseSecurityCodeAttribute, BaseSecurityCodeAttributeSaveVO,
    BaseSecurityCodeAttributeUpdateVO, BaseSecurityCodeAttributePageQuery, BaseSecurityCodeAttributeResultVO> implements BaseSecurityCodeAttributeService {

    private final BaseSecurityCodeManager baseSecurityCodeManager;
    private final BaseAttributeService baseAttributeService;
    private final BaseProductManager baseProductManager;
    private final BaseSecurityCodeAttributeDetailsManager baseSecurityCodeAttributeDetailsManager;

    @Override
    public BaseSecurityCodeAttributeResultVO saveAttribute(BaseSecurityCodeAddAttributeVO saveVO) {
        // 先查询，如果存在，就走更新接口
        BaseSecurityCodeAttribute baseSecurityCodeAttribute = superManager.getOne(Wraps.<BaseSecurityCodeAttribute>lbQ()
                .eq(BaseSecurityCodeAttribute::getSecurityCode, saveVO.getSecurityCode()).last("limit 1"));
        if (Objects.nonNull(baseSecurityCodeAttribute)) {
            super.updateById(BeanPlusUtil.toBean(baseSecurityCodeAttribute, BaseSecurityCodeAttributeUpdateVO.class));
            addAttributeDetails(saveVO, baseSecurityCodeAttribute);
            return BeanPlusUtil.toBean(baseSecurityCodeAttribute, BaseSecurityCodeAttributeResultVO.class);
        }

        BaseSecurityCode securityCode = baseSecurityCodeManager.getOne(Wraps.<BaseSecurityCode>lbQ().eq(BaseSecurityCode::getSecurityCode, saveVO.getSecurityCode()).last("limit 1"));
        ArgumentAssert.isTrue(Objects.nonNull(securityCode), "防伪码不存在");
        ArgumentAssert.isTrue(Objects.nonNull(securityCode.getProductId()), "防伪码未绑定商品");
        BaseProduct product = baseProductManager.getById(securityCode.getProductId());
        ArgumentAssert.isTrue(Objects.nonNull(product), "商品信息不存在");

        BaseSecurityCodeAttributeSaveVO attributeSaveVO = BaseSecurityCodeAttributeSaveVO.builder()
                .productId(product.getId())
                .securityCode(securityCode.getSecurityCode())
                .securityCodeId(securityCode.getId())
                .createdOrgId(ContextUtil.getCurrentCompanyId())
                .build();
        BaseSecurityCodeAttribute attribute = save(attributeSaveVO);
        addAttributeDetails(saveVO, attribute);
        return BeanPlusUtil.toBean(attribute, BaseSecurityCodeAttributeResultVO.class);
    }

    @Override
    public BaseSecurityCodeAttributeResultVO updateAttribute(BaseSecurityCodeAddAttributeVO saveVO) {
        BaseSecurityCodeAttribute attribute = superManager.getOne(Wraps.<BaseSecurityCodeAttribute>lbQ()
                .eq(BaseSecurityCodeAttribute::getSecurityCode, saveVO.getSecurityCode()).last("limit 1"));
        ArgumentAssert.isTrue(Objects.nonNull(attribute), "防伪码规格属性不存在, 无法修改");
        super.updateById(BeanPlusUtil.toBean(attribute, BaseSecurityCodeAttributeUpdateVO.class));
        addAttributeDetails(saveVO, attribute);
        return BeanPlusUtil.toBean(attribute, BaseSecurityCodeAttributeResultVO.class);
    }

    @Override
    public BaseSecurityCodeAttributeResultVO getDetail(Long id) {
        BaseSecurityCodeAttribute attribute = superManager.getById(id);
        ArgumentAssert.isTrue(Objects.nonNull(attribute), "防伪码规格属性未设置");
        BaseSecurityCodeAttributeResultVO resultVO = BeanPlusUtil.toBean(attribute, BaseSecurityCodeAttributeResultVO.class);
        List<BaseSecurityCodeAttributeDetails> attributeDetailsList = baseSecurityCodeAttributeDetailsManager.list(Wraps.<BaseSecurityCodeAttributeDetails>lbQ().eq(BaseSecurityCodeAttributeDetails::getCodeAttributeId, attribute.getId()));
        List<BaseSecurityCodeAttributeDetailsResultVO> detailsResultVOS = BeanPlusUtil.toBeanList(attributeDetailsList, BaseSecurityCodeAttributeDetailsResultVO.class);
        if (CollUtil.isNotEmpty(detailsResultVOS)) {
            List<Long> attributeIds = detailsResultVOS.stream().map(BaseSecurityCodeAttributeDetailsResultVO::getAttributeId).distinct().collect(Collectors.toList());
            List<BaseAttribute> baseAttributeList = baseAttributeService.listByIds(attributeIds);
            Map<Long, BaseAttribute> attributeMap = baseAttributeList.stream().collect(Collectors.toMap(BaseAttribute::getId, item -> item));
            detailsResultVOS.forEach(item -> {
                BaseAttribute attribute1 = attributeMap.get(item.getAttributeId());
                if (Objects.nonNull(attribute1)) {
                    item.setAttributeName(attribute1.getName());
                    item.setAttributeUnit(attribute1.getUnit());
                    item.setSortValue(attribute1.getSortValue());
                }
            });
            // 倒叙
            detailsResultVOS.sort((o1, o2) -> o2.getSortValue().compareTo(o1.getSortValue()));
        }
        resultVO.setAttributeDetailsResultVOList(detailsResultVOS);
        return resultVO;
    }

    @Override
    public List<BaseSecurityCodeAttributeDetailsResultVO> getAttributeDetailList(Long securityCodeId) {
        BaseSecurityCodeAttribute attribute = superManager.getOne(Wraps.<BaseSecurityCodeAttribute>lbQ()
                .eq(BaseSecurityCodeAttribute::getSecurityCodeId, securityCodeId).last("limit 1"));
        if (Objects.isNull(attribute)) {
            return Collections.emptyList();
        }
        List<BaseSecurityCodeAttributeDetails> attributeDetailsList = baseSecurityCodeAttributeDetailsManager.list(Wraps.<BaseSecurityCodeAttributeDetails>lbQ().eq(BaseSecurityCodeAttributeDetails::getCodeAttributeId, attribute.getId()));
        if (CollUtil.isEmpty(attributeDetailsList)) {
            return Collections.emptyList();
        }
        List<BaseSecurityCodeAttributeDetailsResultVO> detailsResultVOS = BeanPlusUtil.toBeanList(attributeDetailsList, BaseSecurityCodeAttributeDetailsResultVO.class);
        List<Long> attributeIds = detailsResultVOS.stream().map(BaseSecurityCodeAttributeDetailsResultVO::getAttributeId).distinct().collect(Collectors.toList());
        List<BaseAttribute> baseAttributeList = baseAttributeService.listByIds(attributeIds);
        Map<Long, BaseAttribute> attributeMap = baseAttributeList.stream().collect(Collectors.toMap(BaseAttribute::getId, item -> item));
        detailsResultVOS.forEach(item -> {
            BaseAttribute attribute1 = attributeMap.get(item.getAttributeId());
            if (Objects.nonNull(attribute1)) {
                item.setAttributeName(attribute1.getName());
                item.setAttributeUnit(attribute1.getUnit());
                item.setSortValue(attribute1.getSortValue());
            }
        });
        // 倒叙
        detailsResultVOS.sort((o1, o2) -> o2.getSortValue().compareTo(o1.getSortValue()));
        return detailsResultVOS;
    }

    /**
     * 添加属性详情
     * @param saveVO
     * @param attribute
     */
    private void addAttributeDetails(BaseSecurityCodeAddAttributeVO saveVO, BaseSecurityCodeAttribute attribute) {
        baseSecurityCodeAttributeDetailsManager.remove(Wraps.<BaseSecurityCodeAttributeDetails>lbQ().eq(BaseSecurityCodeAttributeDetails::getCodeAttributeId, attribute.getId()));
        if (CollUtil.isNotEmpty(saveVO.getDetailsSaveVOList())) {
            List<BaseSecurityCodeAttributeDetails> attributeDetailsList = saveVO.getDetailsSaveVOList().stream().map(s ->
                    BaseSecurityCodeAttributeDetails.builder()
                            .codeAttributeId(attribute.getId())
                            .attributeId(s.getAttributeId())
                            .attributeValue(s.getAttributeValue())
                            .build()).collect(Collectors.toList());
            baseSecurityCodeAttributeDetailsManager.saveBatch(attributeDetailsList);
        }
    }
}


