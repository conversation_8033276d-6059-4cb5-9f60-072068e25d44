package top.kx.kxss.base.manager.dealers;

import top.kx.basic.base.manager.SuperManager;
import top.kx.basic.interfaces.echo.LoadService;
import top.kx.kxss.base.entity.dealers.BaseDealers;

/**
 * <p>
 * 通用业务接口
 * 经销商信息
 * </p>
 *
 * <AUTHOR>
 * @date 2024-12-20 10:47:35
 * @create [2024-12-20 10:47:35] [dou] [代码生成器生成]
 */
public interface BaseDealersManager extends SuperManager<BaseDealers>, LoadService {

}


