package top.kx.kxss.base.event;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import top.kx.kxss.base.service.security.BaseSecurityCodeBatchDetailsService;

@Slf4j
@Component
public class SecurityCodeBatchSaveListener {

    private final BaseSecurityCodeBatchDetailsService securityCodeBatchDetailsService;

    public SecurityCodeBatchSaveListener(BaseSecurityCodeBatchDetailsService securityCodeBatchDetailsService) {
        this.securityCodeBatchDetailsService = securityCodeBatchDetailsService;
    }

    @Async
    @EventListener
    public void handleSecurityCodeBatchSave(SecurityCodeBatchSaveEvent event) {
        log.info("开始异步处理防伪码批次详情生成, batchId={}", event.getBatch().getId());
        securityCodeBatchDetailsService.saveBatch(event.getBatch());
    }
}