package top.kx.kxss.base.manager.attribute.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.base.entity.attribute.BaseAttributeValue;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.base.manager.attribute.BaseAttributeValueManager;
import top.kx.kxss.base.mapper.attribute.BaseAttributeValueMapper;

/**
 * <p>
 * 通用业务实现类
 * 商品基础属性值
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-16 18:44:16
 * @create [2025-06-16 18:44:16] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BaseAttributeValueManagerImpl extends SuperManagerImpl<BaseAttributeValueMapper, BaseAttributeValue> implements BaseAttributeValueManager {

}


