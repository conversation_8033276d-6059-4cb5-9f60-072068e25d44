package top.kx.kxss.base.manager.enterprise;

import top.kx.basic.base.manager.SuperCacheManager;
import top.kx.basic.interfaces.echo.LoadService;
import top.kx.kxss.base.entity.enterprise.BaseEnterprise;

/**
 * <p>
 * 通用业务接口
 * 企业
 * </p>
 *
 * <AUTHOR>
 * @date 2025-04-25 17:45:11
 * @create [2025-04-25 17:45:11] [yan] [代码生成器生成]
 */
public interface BaseEnterpriseManager extends SuperCacheManager<BaseEnterprise>, LoadService {

}


