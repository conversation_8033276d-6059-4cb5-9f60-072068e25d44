package top.kx.kxss.base.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;
import top.kx.kxss.base.entity.security.BaseSecurityCodeBatch;

@Getter
public class SecurityCodeBatchSaveEvent extends ApplicationEvent {
    private final BaseSecurityCodeBatch batch;

    public SecurityCodeBatchSaveEvent(Object source, BaseSecurityCodeBatch batch) {
        super(source);
        this.batch = batch;
    }
}