package top.kx.kxss.base.service.biz;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.base.entity.biz.BaseBizLog;
import top.kx.kxss.base.vo.query.biz.BaseBizLogPageQuery;
import top.kx.kxss.base.vo.result.biz.BaseBizLogResultVO;
import top.kx.kxss.base.vo.save.biz.BaseBizLogSaveVO;
import top.kx.kxss.base.vo.save.biz.BizLogSaveVO;
import top.kx.kxss.base.vo.update.biz.BaseBizLogUpdateVO;


/**
 * <p>
 * 业务接口
 * 防伪码操作日志
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-29 14:02:40
 * @create [2023-05-29 14:02:40] [dou] [代码生成器生成]
 */
public interface BaseBizLogService extends SuperService<Long, BaseBizLog, BaseBizLogSaveVO,
    BaseBizLogUpdateVO, BaseBizLogPageQuery, BaseBizLogResultVO> {

    /**
     * 新增日志
     * @param saveVO
     */
    void saveLog(BizLogSaveVO saveVO);

    void saveBatchLog(BizLogSaveVO saveVO);
}


