package top.kx.kxss.msg.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.base.service.SuperService;
import top.kx.kxss.msg.entity.ExtendNotice;
import top.kx.kxss.msg.vo.query.ExtendNoticePageQuery;
import top.kx.kxss.msg.vo.result.ExtendNoticeResultVO;
import top.kx.kxss.msg.vo.save.ExtendNoticeSaveVO;
import top.kx.kxss.msg.vo.update.ExtendNoticeUpdateVO;

import java.util.List;


/**
 * <p>
 * 业务接口
 * 通知表
 * </p>
 *
 * <AUTHOR>
 * @date 2022-07-04 15:51:37
 * @create [2022-07-04 15:51:37] [zuihou] [代码生成器生成]
 */
public interface ExtendNoticeService extends SuperService<Long, ExtendNotice, ExtendNoticeSaveVO,
        ExtendNoticeUpdateVO, ExtendNoticePageQuery, ExtendNoticeResultVO> {

    /**
     * 分页查询
     *
     * @param page
     * @param params
     * @return
     */
    IPage<ExtendNoticeResultVO> page(IPage<ExtendNoticeResultVO> page, PageParams<ExtendNoticePageQuery> params);

    /**
     * 标记 已读
     *
     * @param noticeIds
     * @param employeeId
     * @return
     */
    Boolean mark(List<Long> noticeIds, Long employeeId);

    /**
     * 删除我的通知
     *
     * @param noticeIds
     * @return
     */
    Boolean deleteMyNotice(List<Long> noticeIds);
}


