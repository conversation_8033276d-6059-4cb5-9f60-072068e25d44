package top.kx.kxss.base.service.user;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.base.entity.user.BaseEmployeeRoleRel;
import top.kx.kxss.base.vo.query.user.BaseEmployeeRoleRelPageQuery;
import top.kx.kxss.base.vo.result.user.BaseEmployeeRoleRelResultVO;
import top.kx.kxss.base.vo.save.user.BaseEmployeeRoleRelSaveVO;
import top.kx.kxss.base.vo.update.user.BaseEmployeeRoleRelUpdateVO;

import java.util.List;

/**
 * <p>
 * 业务接口
 * 员工的角色
 * </p>
 *
 * <AUTHOR>
 * @date 2021-10-18
 */
public interface BaseEmployeeRoleRelService extends SuperService<Long, BaseEmployeeRoleRel, BaseEmployeeRoleRelSaveVO, BaseEmployeeRoleRelUpdateVO, BaseEmployeeRoleRelPageQuery, BaseEmployeeRoleRelResultVO> {
    /**
     * 给员工绑定指定的角色
     *
     * @param employeeIdList 员工
     * @param code           角色编码
     * @return
     */
    boolean bindRole(List<Long> employeeIdList, String code);

    /**
     * 解绑指定角色
     *
     * @param employeeIdList
     * @param code
     * @return
     */
    boolean unBindRole(List<Long> employeeIdList, String code);
}
