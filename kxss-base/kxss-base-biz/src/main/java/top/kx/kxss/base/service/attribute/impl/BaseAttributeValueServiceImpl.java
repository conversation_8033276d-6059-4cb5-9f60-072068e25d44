package top.kx.kxss.base.service.attribute.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.base.entity.attribute.BaseAttributeValue;
import top.kx.kxss.base.manager.attribute.BaseAttributeValueManager;
import top.kx.kxss.base.service.attribute.BaseAttributeValueService;
import top.kx.kxss.base.vo.query.attribute.BaseAttributeValuePageQuery;
import top.kx.kxss.base.vo.result.attribute.BaseAttributeValueResultVO;
import top.kx.kxss.base.vo.save.attribute.BaseAttributeValueSaveVO;
import top.kx.kxss.base.vo.update.attribute.BaseAttributeValueUpdateVO;

/**
 * <p>
 * 业务实现类
 * 商品基础属性值
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-16 18:44:16
 * @create [2025-06-16 18:44:16] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
//@Transactional(readOnly = true)
public class BaseAttributeValueServiceImpl extends SuperServiceImpl<BaseAttributeValueManager, Long, BaseAttributeValue, BaseAttributeValueSaveVO,
    BaseAttributeValueUpdateVO, BaseAttributeValuePageQuery, BaseAttributeValueResultVO> implements BaseAttributeValueService {


    @Override
    public void remove(LbQueryWrap<BaseAttributeValue> eq) {
        superManager.remove(eq);
    }
}


