package top.kx.kxss.base.service.password;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.base.entity.password.BasePasswordConfig;
import top.kx.kxss.base.vo.query.password.BasePasswordConfigPageQuery;
import top.kx.kxss.base.vo.result.password.BasePasswordConfigResultVO;
import top.kx.kxss.base.vo.save.password.BasePasswordConfigSaveVO;
import top.kx.kxss.base.vo.update.password.BasePasswordConfigUpdateVO;
import top.kx.kxss.base.vo.update.password.BasePasswordUpdateVO;

/**
 * <p>
 * 密码验证配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface BasePasswordConfigService extends SuperService<Long, BasePasswordConfig, BasePasswordConfigSaveVO, BasePasswordConfigUpdateVO, BasePasswordConfigPageQuery, BasePasswordConfigResultVO> {

    /**
     * 根据业务代码获取密码配置
     *
     * @param businessCode 业务代码
     * @return 密码配置
     */
    BasePasswordConfig getByBusinessCode(String businessCode);

    /**
     * 验证密码是否正确
     *
     * @param businessCode 业务代码
     * @param password     输入的密码
     * @return 验证结果
     */
    boolean validatePassword(String businessCode, String password);

    /**
     * 检查业务是否需要密码验证
     *
     * @param businessCode 业务代码
     * @return 是否需要验证
     */
    boolean needPasswordValidation(String businessCode);

    Boolean updatePassword(BasePasswordUpdateVO updateVO);
}