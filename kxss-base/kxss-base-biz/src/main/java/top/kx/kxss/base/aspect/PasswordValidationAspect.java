package top.kx.kxss.base.aspect;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.base.service.password.BasePasswordConfigService;
import top.kx.kxss.common.annotation.PasswordValidation;
import top.kx.kxss.common.dto.PasswordValidationVO;
import top.kx.kxss.model.enumeration.base.PasswordBusinessEnum;

import java.lang.reflect.Method;

/**
 * 密码验证切面处理类
 * 用于处理带有@PasswordValidation注解的方法
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class PasswordValidationAspect {

    private final BasePasswordConfigService basePasswordConfigService;

    @Around("@annotation(top.kx.kxss.common.annotation.PasswordValidation)")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        PasswordValidation annotation = method.getAnnotation(PasswordValidation.class);

        if (annotation == null) {
            return joinPoint.proceed();
        }

        PasswordBusinessEnum businessEnum = annotation.businessCode();
        String businessCode = businessEnum.getCode();
        String description = annotation.description();

        log.info("开始密码验证，业务代码：{}，描述：{}", businessCode, description);

        // 检查是否需要密码验证
        if (!basePasswordConfigService.needPasswordValidation(businessCode)) {
            log.info("业务代码 {} 未配置密码验证，跳过验证", businessCode);
            return joinPoint.proceed();
        }

        // 获取方法参数
        Object[] args = joinPoint.getArgs();
        String inputPassword = null;

        // 查找PasswordValidationDTO类型的参数
        for (Object arg : args) {
            if (arg instanceof PasswordValidationVO) {
                PasswordValidationVO dto = (PasswordValidationVO) arg;
                inputPassword = dto.getPassword();
                break;
            }
        }

        ArgumentAssert.isTrue(StringUtils.hasText(inputPassword), "密码验证失败：未提供密码参数");
//        if (!StringUtils.hasText(inputPassword)) {
//            log.warn("密码验证失败：未提供密码参数");
//            return R.fail("密码验证失败：未提供密码");
//        }

        // 验证密码
        boolean isValid = basePasswordConfigService.validatePassword(businessCode, inputPassword);
        ArgumentAssert.isTrue(isValid, "密码验证失败：密码不正确");
//        if (!isValid) {
//            log.warn("密码验证失败：业务代码 {}，密码不正确", businessCode);
//            return R.fail("密码验证失败：密码不正确");
//        }

        log.info("密码验证成功，业务代码：{}", businessCode);
        return joinPoint.proceed();
    }
}
