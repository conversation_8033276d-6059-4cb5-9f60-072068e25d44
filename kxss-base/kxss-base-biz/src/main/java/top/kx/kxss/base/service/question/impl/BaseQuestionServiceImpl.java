package top.kx.kxss.base.service.question.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.kxss.base.entity.question.BaseQuestion;
import top.kx.kxss.base.manager.question.BaseQuestionManager;
import top.kx.kxss.base.service.question.BaseQuestionService;
import top.kx.kxss.base.vo.query.question.BaseQuestionPageQuery;
import top.kx.kxss.base.vo.result.question.BaseQuestionResultVO;
import top.kx.kxss.base.vo.save.question.BaseQuestionSaveVO;
import top.kx.kxss.base.vo.update.question.BaseQuestionUpdateVO;

/**
 * <p>
 * 业务实现类
 * 常见问题
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-26 16:24:10
 * @create [2023-05-26 16:24:10] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
@Transactional(readOnly = true)
public class BaseQuestionServiceImpl extends SuperServiceImpl<BaseQuestionManager, Long, BaseQuestion, BaseQuestionSaveVO,
        BaseQuestionUpdateVO, BaseQuestionPageQuery, BaseQuestionResultVO> implements BaseQuestionService {


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateState(Long id, Boolean state) {
        BaseQuestion build = BaseQuestion.builder().state(state).build();
        build.setId(id);
        return superManager.updateById(build);
    }
}


