package top.kx.kxss.base.service.user;

import com.baomidou.mybatisplus.core.metadata.IPage;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.base.service.SuperCacheService;
import top.kx.kxss.base.entity.user.BaseEmployee;
import top.kx.kxss.base.vo.query.user.BaseEmployeePageQuery;
import top.kx.kxss.base.vo.result.user.BaseEmployeeResultVO;
import top.kx.kxss.base.vo.save.user.BaseEmployeeRoleRelSaveVO;
import top.kx.kxss.base.vo.save.user.BaseEmployeeSaveVO;
import top.kx.kxss.base.vo.update.user.BaseEmployeeUpdateVO;
import top.kx.kxss.base.vo.update.user.EmployeeUpdateVO;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 业务接口
 * 员工
 * </p>
 *
 * <AUTHOR>
 * @date 2021-10-18
 */
public interface BaseEmployeeService extends SuperCacheService<Long, BaseEmployee, BaseEmployeeSaveVO, BaseEmployeeUpdateVO, BaseEmployeePageQuery, BaseEmployeeResultVO> {
    /**
     * 批量保存
     *
     * @param entityList entityList
     * @return boolean
     * <AUTHOR>
     * @date 2022/10/28 4:38 PM
     * @create [2022/10/28 4:38 PM ] [tangyh] [初始创建]
     */
    boolean saveBatch(Collection<BaseEmployee> entityList);

    /**
     * 给员工分配角色
     *
     * @param employeeRoleSaveVO
     * @return
     */
    List<Long> saveEmployeeRole(BaseEmployeeRoleRelSaveVO employeeRoleSaveVO);

    /**
     * 根据员工id查询员工的角色
     *
     * @param employeeId 员工id
     * @return
     */
    List<Long> findEmployeeRoleByEmployeeId(Long employeeId);

    /**
     * 分页查询
     *
     * @param params
     * @return
     */
    IPage<BaseEmployeeResultVO> findPageResultVO(PageParams<BaseEmployeePageQuery> params);


    /**
     * 批量保存 基础库员工和系统角色
     *
     * @param employeeList
     * @return
     */
    boolean saveBatchBaseEmployeeAndRole(List<BaseEmployee> employeeList);

    /**
     * 根据ID修改不为空的字段
     *
     * @param baseEmployee baseEmployee
     * @return boolean
     * <AUTHOR>
     * @date 2022/10/28 9:20 AM
     * @create [2022/10/28 9:20 AM ] [tangyh] [初始创建]
     */
    boolean updateById(BaseEmployee baseEmployee);

    /**
     * 根据ID修改所有的字段
     *
     * @param baseEmployee baseEmployee
     * @return boolean
     * <AUTHOR>
     * @date 2022/10/28 9:20 AM
     * @create [2022/10/28 9:20 AM ] [tangyh] [初始创建]
     */
    boolean updateAllById(BaseEmployee baseEmployee);

    /**
     * 查询指定企业指定用户的员工信息
     *
     * @param userId 用户id
     * @return
     */
    BaseEmployee getEmployeeByUser(Long userId);


    /**
     * 根据用户id查询员工
     *
     * @param userId 用户id
     * @return
     */
    List<BaseEmployeeResultVO> listEmployeeByUserId(Long userId);
    /**
     * 根据id查询待回显参数
     *
     * @param ids 唯一键（可能不是主键ID)
     * @return
     */
    Map<Serializable, Object> findByIds(Set<Serializable> ids);

    /**
     * 修改员工个人信息
     * @param updateVO
     * @return
     */
    Boolean updateEmployee(EmployeeUpdateVO updateVO);

}
