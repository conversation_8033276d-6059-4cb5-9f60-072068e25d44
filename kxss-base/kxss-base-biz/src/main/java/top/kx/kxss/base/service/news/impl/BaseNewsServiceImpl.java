package top.kx.kxss.base.service.news.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.exception.BizException;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.base.entity.news.BaseNews;
import top.kx.kxss.base.manager.news.BaseNewsManager;
import top.kx.kxss.base.service.news.BaseNewsService;
import top.kx.kxss.base.vo.query.news.BaseNewsPageQuery;
import top.kx.kxss.base.vo.result.news.BaseNewsResultVO;
import top.kx.kxss.base.vo.save.news.BaseNewsSaveVO;
import top.kx.kxss.base.vo.update.news.BaseNewsUpdateVO;
import top.kx.kxss.model.enumeration.base.NewsStatusEnum;

/**
 * <p>
 * 业务实现类
 * 新闻资讯
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-25 16:12:08
 * @create [2023-05-25 16:12:08] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BaseNewsServiceImpl extends SuperServiceImpl<BaseNewsManager, Long, BaseNews, BaseNewsSaveVO,
        BaseNewsUpdateVO, BaseNewsPageQuery, BaseNewsResultVO> implements BaseNewsService {


    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseNewsResultVO getInfoById(Long id) {
        BaseNews byId = superManager.getById(id);
        if (ObjectUtil.isNull(byId)) {
            return null;
        }
        byId.setViewCount(byId.getViewCount() + 1);
        superManager.updateById(byId);
        return BeanUtil.copyProperties(byId, BaseNewsResultVO.class);
    }

    @Override
    protected BaseNews saveBefore(BaseNewsSaveVO baseNewsSaveVO) {
        if (ObjectUtil.isNotNull(baseNewsSaveVO.getCoverImageFile())) {
            baseNewsSaveVO.setCoverImage(baseNewsSaveVO.getCoverImageFile().getId());
        }
        if (ObjectUtil.isNull(baseNewsSaveVO.getCoverImage())) {
            throw new BizException("请上传封面图");
        }
        return super.saveBefore(baseNewsSaveVO);
    }

    @Override
    protected BaseNews updateBefore(BaseNewsUpdateVO baseNewsUpdateVO) {
        if (ObjectUtil.isNotNull(baseNewsUpdateVO.getCoverImageFile())) {
            baseNewsUpdateVO.setCoverImage(baseNewsUpdateVO.getCoverImageFile().getId());
        }
        if (ObjectUtil.isNull(baseNewsUpdateVO.getCoverImage())) {
            throw new BizException("请上传封面图");
        }
        return super.updateBefore(baseNewsUpdateVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean revoke(Long id) {
        BaseNews baseNews = superManager.getById(id);
        ArgumentAssert.notNull(baseNews, "资讯不存在！");
        if (!ObjectUtil.equal(baseNews.getStatus(), NewsStatusEnum.RELEASE.getCode())) {
            throw new BizException("改状态无法撤销");
        }
        baseNews.setStatus(NewsStatusEnum.REVOKE.getCode());
        return superManager.updateById(baseNews);
    }
}


