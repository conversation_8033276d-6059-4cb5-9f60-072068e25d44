package top.kx.kxss.msg.service;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.model.entity.system.SysUser;
import top.kx.kxss.msg.entity.DefMsgTemplate;
import top.kx.kxss.msg.entity.ExtendMsg;
import top.kx.kxss.msg.vo.query.ExtendMsgPageQuery;
import top.kx.kxss.msg.vo.result.ExtendMsgResultVO;
import top.kx.kxss.msg.vo.save.ExtendMsgSaveVO;
import top.kx.kxss.msg.vo.update.ExtendMsgPublishVO;
import top.kx.kxss.msg.vo.update.ExtendMsgSendVO;
import top.kx.kxss.msg.vo.update.ExtendMsgUpdateVO;


/**
 * <p>
 * 业务接口
 * 消息
 * </p>
 *
 * <AUTHOR>
 * @date 2022-07-10 11:41:17
 * @create [2022-07-10 11:41:17] [zuihou] [代码生成器生成]
 */
public interface ExtendMsgService extends SuperService<Long, ExtendMsg, ExtendMsgSaveVO,
        ExtendMsgUpdateVO, ExtendMsgPageQuery, ExtendMsgResultVO> {

    /**
     * 发送消息
     *
     * @param data
     * @param msgTemplate
     * @return
     * <AUTHOR>
     * @date 2022/10/28 4:57 PM
     * @create [2022/10/28 4:57 PM ] [tangyh] [初始创建]
     */
    Boolean send(ExtendMsgSendVO data, DefMsgTemplate msgTemplate, SysUser sysUser);

    /**
     * 发布消息
     *
     * @param data
     * @param sysUser
     * @return
     * <AUTHOR>
     * @date 2022/10/28 4:57 PM
     * @create [2022/10/28 4:57 PM ] [tangyh] [初始创建]
     */
    Boolean publish(ExtendMsgPublishVO data, SysUser sysUser);

    /**
     * 查询消息详情
     *
     * @param id
     * @return
     */
    ExtendMsgResultVO getResultById(Long id);
}


