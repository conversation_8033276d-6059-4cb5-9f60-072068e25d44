package top.kx.kxss.base.service.dealers.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.base.entity.dealers.BaseDealers;
import top.kx.kxss.base.manager.dealers.BaseDealersManager;
import top.kx.kxss.base.service.dealers.BaseDealersService;
import top.kx.kxss.base.vo.query.dealers.BaseDealersPageQuery;
import top.kx.kxss.base.vo.result.dealers.BaseDealersResultVO;
import top.kx.kxss.base.vo.save.dealers.BaseDealersSaveVO;
import top.kx.kxss.base.vo.update.dealers.BaseDealersUpdateVO;

/**
 * <p>
 * 业务实现类
 * 经销商信息
 * </p>
 *
 * <AUTHOR>
 * @date 2024-12-20 10:47:35
 * @create [2024-12-20 10:47:35] [dou] [代码生成器生成]
 */

@Slf4j
@RequiredArgsConstructor
@Service
public class BaseDealersServiceImpl extends SuperServiceImpl<BaseDealersManager, Long, BaseDealers, BaseDealersSaveVO,
    BaseDealersUpdateVO, BaseDealersPageQuery, BaseDealersResultVO> implements BaseDealersService {


    @Override
    public BaseDealers getOne(LbQueryWrap<BaseDealers> queryWrap) {
        return superManager.getOne(queryWrap);
    }

    @Override
    public boolean saveOrUpdate(BaseDealers dealers) {
        return superManager.saveOrUpdate(dealers);
    }
}


