package top.kx.kxss.base.manager.print.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.base.entity.print.BasePrintTemplate;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.base.manager.print.BasePrintTemplateManager;
import top.kx.kxss.base.mapper.print.BasePrintTemplateMapper;

/**
 * <p>
 * 通用业务实现类
 * 打印模板
 * </p>
 *
 * <AUTHOR>
 * @date 2025-08-27 10:48:00
 * @create [2025-08-27 10:48:00] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BasePrintTemplateManagerImpl extends SuperManagerImpl<BasePrintTemplateMapper, BasePrintTemplate> implements BasePrintTemplateManager {

}


