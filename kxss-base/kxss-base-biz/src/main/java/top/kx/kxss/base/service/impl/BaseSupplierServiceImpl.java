package top.kx.kxss.base.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.kxss.base.service.BaseSupplierService;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.kxss.base.manager.BaseSupplierManager;
import top.kx.kxss.base.entity.supplier.BaseSupplier;
import top.kx.kxss.base.vo.save.supplier.BaseSupplierSaveVO;
import top.kx.kxss.base.vo.update.supplier.BaseSupplierUpdateVO;
import top.kx.kxss.base.vo.result.supplier.BaseSupplierResultVO;
import top.kx.kxss.base.vo.query.supplier.BaseSupplierPageQuery;

/**
 * <p>
 * 业务实现类
 * 供应商信息信息
 * </p>
 *
 * <AUTHOR>
 * @date 2025-02-28 16:36:35
 * @create [2025-02-28 16:36:35] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
@Transactional(readOnly = true)
public class BaseSupplierServiceImpl extends SuperServiceImpl<BaseSupplierManager, Long, BaseSupplier, BaseSupplierSaveVO,
    BaseSupplierUpdateVO, BaseSupplierPageQuery, BaseSupplierResultVO> implements BaseSupplierService {


}


