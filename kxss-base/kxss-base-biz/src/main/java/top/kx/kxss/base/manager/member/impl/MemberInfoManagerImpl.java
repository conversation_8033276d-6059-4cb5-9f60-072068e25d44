package top.kx.kxss.base.manager.member.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.base.manager.impl.SuperCacheManagerImpl;
import top.kx.basic.model.cache.CacheKeyBuilder;
import top.kx.basic.utils.CollHelper;
import top.kx.kxss.base.entity.member.MemberInfo;
import top.kx.kxss.base.manager.member.MemberInfoManager;
import top.kx.kxss.base.mapper.member.MemberInfoMapper;
import top.kx.kxss.common.cache.tenant.base.MemberInfoCacheKeyBuilder;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 通用业务实现类
 * 会员信息
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-24 18:03:33
 * @create [2023-05-24 18:03:33] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MemberInfoManagerImpl extends SuperCacheManagerImpl<MemberInfoMapper, MemberInfo> implements MemberInfoManager {

    @Override
    protected CacheKeyBuilder cacheKeyBuilder() {
        return new MemberInfoCacheKeyBuilder();
    }

    @Override
    public Map<Serializable, Object> findByIds(Set<Serializable> ids) {
        List<MemberInfo> list = findByIds(ids, null).stream().filter(Objects::nonNull).collect(Collectors.toList());
        return CollHelper.uniqueIndex(list, MemberInfo::getId, MemberInfo::getName);
    }
}


