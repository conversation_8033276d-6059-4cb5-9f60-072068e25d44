package top.kx.kxss.base.service.print;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.base.entity.print.BasePrintTemplate;
import top.kx.kxss.base.vo.save.print.BasePrintTemplateSaveVO;
import top.kx.kxss.base.vo.update.print.BasePrintTemplateUpdateVO;
import top.kx.kxss.base.vo.result.print.BasePrintTemplateResultVO;
import top.kx.kxss.base.vo.query.print.BasePrintTemplatePageQuery;

import java.util.List;


/**
 * <p>
 * 业务接口
 * 打印模板
 * </p>
 *
 * <AUTHOR>
 * @date 2025-08-27 10:48:00
 * @create [2025-08-27 10:48:00] [yan] [代码生成器生成]
 */
public interface BasePrintTemplateService extends SuperService<Long, BasePrintTemplate, BasePrintTemplateSaveVO,
    BasePrintTemplateUpdateVO, BasePrintTemplatePageQuery, BasePrintTemplateResultVO> {

    List<BasePrintTemplateResultVO> getListByProductId(Long productId);

}


