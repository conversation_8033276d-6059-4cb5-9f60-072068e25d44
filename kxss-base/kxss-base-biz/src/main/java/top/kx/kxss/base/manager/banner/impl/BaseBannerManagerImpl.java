package top.kx.kxss.base.manager.banner.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.base.entity.banner.BaseBanner;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.base.manager.banner.BaseBannerManager;
import top.kx.kxss.base.mapper.banner.BaseBannerMapper;

/**
 * <p>
 * 通用业务实现类
 * 轮播图
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-25 15:35:00
 * @create [2023-05-25 15:35:00] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BaseBannerManagerImpl extends SuperManagerImpl<BaseBannerMapper, BaseBanner> implements BaseBannerManager {

}


