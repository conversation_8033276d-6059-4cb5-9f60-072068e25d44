package top.kx.kxss.base.mapper.security;

import org.apache.ibatis.annotations.Param;
import top.kx.basic.base.mapper.SuperMapper;
import top.kx.kxss.base.entity.security.BaseSecurityCodeBatchDetails;
import org.springframework.stereotype.Repository;
import top.kx.kxss.base.vo.result.security.BaseSecurityCodeBatchDetailsResultVO;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * 防伪码批次-详情
 * </p>
 *
 * <AUTHOR>
 * @date 2025-04-25 17:45:12
 * @create [2025-04-25 17:45:12] [yan] [代码生成器生成]
 */
@Repository
public interface BaseSecurityCodeBatchDetailsMapper extends SuperMapper<BaseSecurityCodeBatchDetails> {

    List<String> getRepeatCode(@Param("batchId") Long batchId);

    List<String> getBigRepeatCode(@Param("batchId") Long batchId);

    List<BaseSecurityCodeBatchDetailsResultVO> getAllBigCode(@Param("batchId") Long batchId);

}


