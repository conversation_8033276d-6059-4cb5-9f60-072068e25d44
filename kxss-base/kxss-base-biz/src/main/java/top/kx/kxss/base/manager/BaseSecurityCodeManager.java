package top.kx.kxss.base.manager;

import top.kx.basic.base.manager.SuperManager;
import top.kx.kxss.base.entity.BaseSecurityCode;

import java.util.List;

/**
 * <p>
 * 通用业务接口
 * 防伪信息
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-25 13:40:18
 * @create [2023-05-25 13:40:18] [dou] [代码生成器生成]
 */
public interface BaseSecurityCodeManager extends SuperManager<BaseSecurityCode> {

    List<String> getSecurityCodeBatch();

}


