package top.kx.kxss.base.service.security;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.base.entity.security.BaseSecurityCodeBatch;
import top.kx.kxss.base.vo.query.security.BaseSecurityCodeBatchPageQuery;
import top.kx.kxss.base.vo.result.security.BaseSecurityCodeBatchDetailsExportResultVO;
import top.kx.kxss.base.vo.result.security.BaseSecurityCodeBatchResultVO;
import top.kx.kxss.base.vo.save.security.BaseSecurityCodeBatchSaveVO;
import top.kx.kxss.base.vo.update.security.BaseSecurityCodeBatchUpdateVO;

import java.util.List;


/**
 * <p>
 * 业务接口
 * 防伪码批次
 * </p>
 *
 * <AUTHOR>
 * @date 2025-04-25 17:45:12
 * @create [2025-04-25 17:45:12] [yan] [代码生成器生成]
 */
public interface BaseSecurityCodeBatchService extends SuperService<Long, BaseSecurityCodeBatch, BaseSecurityCodeBatchSaveVO,
    BaseSecurityCodeBatchUpdateVO, BaseSecurityCodeBatchPageQuery, BaseSecurityCodeBatchResultVO> {

    Boolean importSecurityCode(Long batchId);

    List<BaseSecurityCodeBatchDetailsExportResultVO> exportSecurityCode(Long batchId);

}


