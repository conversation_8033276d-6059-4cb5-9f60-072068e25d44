package top.kx.kxss.msg.strategy.domain.sms;

import cn.hutool.core.util.StrUtil;
import lombok.Data;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.msg.strategy.domain.BaseProperty;

/**
 * <AUTHOR>
 * @date 2022/7/10 0010 18:56
 */
@Data
public class BaiduSmsProperty extends BaseProperty {
    private final static String DEF_END_POINT = "http://smsv3.bj.baidubce.com";
    /**
     * accessKeyId
     */
    private String accessKeyId;
    /**
     * secretKey
     */
    private String secretKey;
    /**
     * 域名
     */
    private String endPoint;

    @Override
    public boolean initAndValid() {
        super.initAndValid();
        if (StrUtil.isEmpty(endPoint)) {
            endPoint = DEF_END_POINT;
        }
        ArgumentAssert.notEmpty(accessKeyId, "accessKeyId 不能为空");
        ArgumentAssert.notEmpty(secretKey, "secretKey 不能为空");
        return true;
    }
}
