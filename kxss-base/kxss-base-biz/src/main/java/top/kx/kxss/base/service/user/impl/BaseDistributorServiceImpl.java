package top.kx.kxss.base.service.user.impl;

//import cn.hutool.core.bean.BeanUtil;
//import cn.hutool.core.collection.CollUtil;
//import cn.hutool.core.util.ObjectUtil;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//import top.kx.basic.database.mybatis.conditions.Wraps;
//import top.kx.kxss.base.biz.user.BaseEmployeeBiz;
//import top.kx.kxss.base.entity.user.BaseEmployee;
//import top.kx.kxss.base.service.user.BaseDistributorService;
//import top.kx.kxss.base.service.user.BaseEmployeeService;
//import top.kx.kxss.base.vo.query.user.BaseEmployeePageQuery;
//import top.kx.kxss.base.vo.result.user.BaseEmployeeResultVO;
//import top.kx.kxss.base.vo.save.user.BaseDistributorSaveVO;
//import top.kx.kxss.base.vo.save.user.BaseEmployeeSaveVO;
//import top.kx.kxss.model.enumeration.base.EmployeeTypeEnum;
//import top.kx.kxss.system.entity.tenant.DefUser;
//import top.kx.kxss.system.service.tenant.DefUserService;
//
//import java.util.List;
//import java.util.Map;
//import java.util.stream.Collectors;
//
///**
// * <p>
// * 业务实现类
// * 员工
// * </p>
// *
// * <AUTHOR>
// * @date 2021-10-18
// */
//@Slf4j
//@Service
//@RequiredArgsConstructor
//public class BaseDistributorServiceImpl implements BaseDistributorService {
//
//    private final BaseEmployeeBiz baseEmployeeBiz;
//    private final BaseEmployeeService baseEmployeeService;
//    private final DefUserService defUserService;
//
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public BaseEmployeeResultVO save(BaseDistributorSaveVO model) {
//        BaseEmployeeSaveVO saveVO = new BaseEmployeeSaveVO();
//        saveVO.setRealName(model.getName());
//        saveVO.setMobile(model.getMobile());
//        saveVO.setRemarks(model.getRemarks());
//        BaseEmployee employee = baseEmployeeBiz.saveDistributor(saveVO);
//        return BeanUtil.copyProperties(employee, BaseEmployeeResultVO.class);
//    }
//
//    @Override
//    public List<BaseEmployeeResultVO> query(BaseEmployeePageQuery query) {
//        List<BaseEmployee> employeeList = baseEmployeeService.list(Wraps.<BaseEmployee>lbQ()
//                .eq(BaseEmployee::getDeleteFlag, 0)
//                .eq(BaseEmployee::getType, EmployeeTypeEnum.Distributor.getCode()));
//        List<BaseEmployeeResultVO> resultVOList = BeanUtil.copyToList(employeeList, BaseEmployeeResultVO.class);
//        if (CollUtil.isEmpty(resultVOList)) {
//            return resultVOList;
//        }
//        List<Long> userIdList = resultVOList.stream().map(BaseEmployeeResultVO::getUserId)
//                .filter(ObjectUtil::isNotNull).collect(Collectors.toList());
//        Map<Long, DefUser> defUserMap = defUserService.list(Wraps.<DefUser>lbQ()
//                .in(DefUser::getId, userIdList)).stream().collect(Collectors.toMap(DefUser::getId, k -> k));
//
//        for (BaseEmployeeResultVO vo : resultVOList) {
//            if (ObjectUtil.isNotNull(vo.getUserId())) {
//                DefUser defUser = defUserMap.get(vo.getUserId());
//                vo.setMobile(defUser == null ? "" : defUser.getMobile());
//            }
//        }
//        return resultVOList;
//    }
//}
