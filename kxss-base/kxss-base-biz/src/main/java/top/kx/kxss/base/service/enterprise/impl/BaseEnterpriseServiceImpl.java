package top.kx.kxss.base.service.enterprise.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.kxss.base.service.enterprise.BaseEnterpriseService;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.kxss.base.manager.enterprise.BaseEnterpriseManager;
import top.kx.kxss.base.entity.enterprise.BaseEnterprise;
import top.kx.kxss.base.vo.save.enterprise.BaseEnterpriseSaveVO;
import top.kx.kxss.base.vo.update.enterprise.BaseEnterpriseUpdateVO;
import top.kx.kxss.base.vo.result.enterprise.BaseEnterpriseResultVO;
import top.kx.kxss.base.vo.query.enterprise.BaseEnterprisePageQuery;

/**
 * <p>
 * 业务实现类
 * 企业
 * </p>
 *
 * <AUTHOR>
 * @date 2025-04-25 17:45:11
 * @create [2025-04-25 17:45:11] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
@Transactional(readOnly = true)
public class BaseEnterpriseServiceImpl extends SuperServiceImpl<BaseEnterpriseManager, Long, BaseEnterprise, BaseEnterpriseSaveVO,
    BaseEnterpriseUpdateVO, BaseEnterprisePageQuery, BaseEnterpriseResultVO> implements BaseEnterpriseService {


}


