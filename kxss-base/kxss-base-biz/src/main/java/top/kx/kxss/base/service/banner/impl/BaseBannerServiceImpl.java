package top.kx.kxss.base.service.banner.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.kxss.base.entity.banner.BaseBanner;
import top.kx.kxss.base.manager.banner.BaseBannerManager;
import top.kx.kxss.base.service.banner.BaseBannerService;
import top.kx.kxss.base.vo.query.banner.BaseBannerPageQuery;
import top.kx.kxss.base.vo.result.banner.BaseBannerResultVO;
import top.kx.kxss.base.vo.save.banner.BaseBannerSaveVO;
import top.kx.kxss.base.vo.update.banner.BaseBannerUpdateVO;
import top.kx.kxss.file.entity.File;
import top.kx.kxss.file.service.FileService;
import top.kx.kxss.system.entity.system.DefClient;
import top.kx.kxss.system.service.system.DefClientService;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 业务实现类
 * 轮播图
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-25 15:35:00
 * @create [2023-05-25 15:35:00] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
@Transactional(readOnly = true)
public class BaseBannerServiceImpl extends SuperServiceImpl<BaseBannerManager, Long, BaseBanner, BaseBannerSaveVO,
        BaseBannerUpdateVO, BaseBannerPageQuery, BaseBannerResultVO> implements BaseBannerService {

    @Resource
    private FileService fileService;
    @Resource
    private DefClientService defClientService;

    @Override
    public List<BaseBannerResultVO> getBannerListByType(String type) {
        DefClient byClientKey = defClientService.getByClientKey(ContextUtil.getClientId());
        List<BaseBanner> list = superManager.list(Wraps.<BaseBanner>lbQ().eq(BaseBanner::getType, type)
                .eq(BaseBanner::getClientId, byClientKey == null ? -1 : byClientKey.getId())
                .eq(BaseBanner::getState, true)
                .orderByAsc(BaseBanner::getSortValue));
        //文件信息
        List<Long> imageIds = list.stream().map(BaseBanner::getImage).collect(Collectors.toList());
        Map<Long, File> fileMap = CollectionUtil.isNotEmpty(imageIds) ? fileService.list(Wraps.<File>lbQ().in(File::getId, imageIds))
                .stream().collect(Collectors.toMap(File::getId, k -> k)) : new HashMap<>();

        return list.stream().map(v -> {
            BaseBannerResultVO bannerResultVO = BeanUtil.copyProperties(v, BaseBannerResultVO.class);
            Long image = v.getImage();
            if (ObjectUtil.isNotNull(image)) {
                bannerResultVO.setImageFile(fileMap.get(image));
            }
            return bannerResultVO;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateState(Long id, Boolean state) {
        BaseBanner build = BaseBanner.builder().state(state).build();
        build.setId(id);
        return superManager.updateById(build);
    }

    @Override
    protected BaseBanner saveBefore(BaseBannerSaveVO baseBannerSaveVO) {
        if (ObjectUtil.isNotNull(baseBannerSaveVO.getImageFile())) {
            baseBannerSaveVO.setImage(baseBannerSaveVO.getImageFile().getId());
        }
        return super.saveBefore(baseBannerSaveVO);
    }

    @Override
    protected BaseBanner updateBefore(BaseBannerUpdateVO baseBannerUpdateVO) {
        if (ObjectUtil.isNotNull(baseBannerUpdateVO.getImageFile())) {
            baseBannerUpdateVO.setImage(baseBannerUpdateVO.getImageFile().getId());
        }
        return super.updateBefore(baseBannerUpdateVO);
    }
}


