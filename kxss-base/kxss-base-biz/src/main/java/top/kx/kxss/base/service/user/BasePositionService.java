package top.kx.kxss.base.service.user;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.base.entity.user.BasePosition;
import top.kx.kxss.base.vo.query.user.BasePositionPageQuery;
import top.kx.kxss.base.vo.result.user.BasePositionResultVO;
import top.kx.kxss.base.vo.save.user.BasePositionSaveVO;
import top.kx.kxss.base.vo.update.user.BasePositionUpdateVO;

import java.io.Serializable;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 业务接口
 * 岗位
 * </p>
 *
 * <AUTHOR>
 * @date 2021-10-18
 */
public interface BasePositionService extends SuperService<Long, BasePosition, BasePositionSaveVO, BasePositionUpdateVO, BasePositionPageQuery, BasePositionResultVO> {
    /**
     * 检测机构名称是否存在
     *
     * @param name 机构名称
     * @param id   机构id
     * @return
     */
    boolean check(String name, Long id);

    /**
     * 根据id查询待回显参数
     *
     * @param ids 唯一键（可能不是主键ID)
     * @return
     */
    Map<Serializable, Object> findByIds(Set<Serializable> ids);
}
