package top.kx.kxss.base.service.attribute.impl;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.entity.SuperEntity;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.base.entity.BaseSecurityCodeAttributeDetails;
import top.kx.kxss.base.entity.attribute.BaseAttribute;
import top.kx.kxss.base.entity.attribute.BaseAttributeValue;
import top.kx.kxss.base.manager.BaseSecurityCodeAttributeDetailsManager;
import top.kx.kxss.base.manager.attribute.BaseAttributeManager;
import top.kx.kxss.base.service.attribute.BaseAttributeService;
import top.kx.kxss.base.service.attribute.BaseAttributeValueService;
import top.kx.kxss.base.vo.query.attribute.BaseAttributePageQuery;
import top.kx.kxss.base.vo.result.attribute.BaseAttributeResultVO;
import top.kx.kxss.base.vo.save.attribute.BaseAttributeSaveVO;
import top.kx.kxss.base.vo.update.attribute.BaseAttributeUpdateVO;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 业务实现类
 * 商品基础属性
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-16 18:44:16
 * @create [2025-06-16 18:44:16] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
//@Transactional(readOnly = true)
public class BaseAttributeServiceImpl extends SuperServiceImpl<BaseAttributeManager, Long, BaseAttribute, BaseAttributeSaveVO,
    BaseAttributeUpdateVO, BaseAttributePageQuery, BaseAttributeResultVO> implements BaseAttributeService {

    private final BaseAttributeValueService baseAttributeValueService;
    private final BaseSecurityCodeAttributeDetailsManager baseSecurityCodeAttributeDetailsManager;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseAttribute save(BaseAttributeSaveVO baseAttributeSaveVO) {
        baseAttributeSaveVO.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
        BaseAttribute baseAttribute = super.save(baseAttributeSaveVO);
        saveAttributeValue(baseAttributeSaveVO.getValueList(), baseAttribute);
        return baseAttribute;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseAttribute updateById(BaseAttributeUpdateVO baseAttributeUpdateVO) {
        BaseAttribute baseAttribute = super.updateById(baseAttributeUpdateVO);
        baseAttributeValueService.remove(Wraps.<BaseAttributeValue>lbQ().eq(SuperEntity::getDeleteFlag, 0)
                .eq(BaseAttributeValue::getAttributeId, baseAttributeUpdateVO.getId()));
        saveAttributeValue(baseAttributeUpdateVO.getValueList(), baseAttribute);
        return baseAttribute;
    }

    /**
     * 保存属性值
     * @param baseAttributeUpdateVO
     * @param baseAttribute
     */
    private void saveAttributeValue(List<String> baseAttributeUpdateVO, BaseAttribute baseAttribute) {
        if (CollUtil.isNotEmpty(baseAttributeUpdateVO)) {
            List<BaseAttributeValue> attributeValueList = baseAttributeUpdateVO.stream().map(s -> BaseAttributeValue.builder()
                    .value(s)
                    .attributeId(baseAttribute.getId())
                    .createdOrgId(ContextUtil.getCurrentCompanyId())
                    .build()).collect(Collectors.toList());
            baseAttributeValueService.saveBatch(attributeValueList);
        }
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean removeByIds(Collection<Long> idList) {
        ArgumentAssert.isTrue(CollUtil.isNotEmpty(idList), "请选择需要删除的数据");
        // 查询有没有被绑定过
        BaseSecurityCodeAttributeDetails one = baseSecurityCodeAttributeDetailsManager.getOne(Wraps.<BaseSecurityCodeAttributeDetails>lbQ()
                .in(BaseSecurityCodeAttributeDetails::getAttributeId, idList).last("limit 1"));
        ArgumentAssert.isTrue(Objects.isNull(one), "该属性已被绑定，无法删除");
        boolean b = super.removeByIds(idList);
        baseAttributeValueService.remove(Wraps.<BaseAttributeValue>lbQ().eq(SuperEntity::getDeleteFlag, 0)
                .in(BaseAttributeValue::getAttributeId, idList));
        return b;
    }



}


