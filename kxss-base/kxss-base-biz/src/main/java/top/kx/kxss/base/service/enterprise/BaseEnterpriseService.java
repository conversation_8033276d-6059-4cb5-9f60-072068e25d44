package top.kx.kxss.base.service.enterprise;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.base.entity.enterprise.BaseEnterprise;
import top.kx.kxss.base.vo.save.enterprise.BaseEnterpriseSaveVO;
import top.kx.kxss.base.vo.update.enterprise.BaseEnterpriseUpdateVO;
import top.kx.kxss.base.vo.result.enterprise.BaseEnterpriseResultVO;
import top.kx.kxss.base.vo.query.enterprise.BaseEnterprisePageQuery;


/**
 * <p>
 * 业务接口
 * 企业
 * </p>
 *
 * <AUTHOR>
 * @date 2025-04-25 17:45:11
 * @create [2025-04-25 17:45:11] [yan] [代码生成器生成]
 */
public interface BaseEnterpriseService extends SuperService<Long, BaseEnterprise, BaseEnterpriseSaveVO,
    BaseEnterpriseUpdateVO, BaseEnterprisePageQuery, BaseEnterpriseResultVO> {

}


