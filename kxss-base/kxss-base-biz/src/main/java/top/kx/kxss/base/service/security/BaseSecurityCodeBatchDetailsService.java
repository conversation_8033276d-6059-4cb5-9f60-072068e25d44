package top.kx.kxss.base.service.security;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.base.entity.security.BaseSecurityCodeBatch;
import top.kx.kxss.base.entity.security.BaseSecurityCodeBatchDetails;
import top.kx.kxss.base.vo.save.security.BaseSecurityCodeBatchDetailsSaveVO;
import top.kx.kxss.base.vo.update.security.BaseSecurityCodeBatchDetailsUpdateVO;
import top.kx.kxss.base.vo.result.security.BaseSecurityCodeBatchDetailsResultVO;
import top.kx.kxss.base.vo.query.security.BaseSecurityCodeBatchDetailsPageQuery;
import top.kx.kxss.base.vo.update.security.BaseSecurityCodeBatchUpdateVO;


/**
 * <p>
 * 业务接口
 * 防伪码批次-详情
 * </p>
 *
 * <AUTHOR>
 * @date 2025-04-25 17:45:12
 * @create [2025-04-25 17:45:12] [yan] [代码生成器生成]
 */
public interface BaseSecurityCodeBatchDetailsService extends SuperService<Long, BaseSecurityCodeBatchDetails, BaseSecurityCodeBatchDetailsSaveVO,
    BaseSecurityCodeBatchDetailsUpdateVO, BaseSecurityCodeBatchDetailsPageQuery, BaseSecurityCodeBatchDetailsResultVO> {

    /**
     * 根据批次详情生成批次序列号
     * @param batch
     */
    void saveBatch(BaseSecurityCodeBatch batch);


    void updateBatch(BaseSecurityCodeBatch batch, BaseSecurityCodeBatchUpdateVO batchUpdateVO);

    void importSecurityCode(Long batchId);

}


