package top.kx.kxss.base.service;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.base.entity.BaseSecurityCodeAttributeDetails;
import top.kx.kxss.base.vo.save.BaseSecurityCodeAttributeDetailsSaveVO;
import top.kx.kxss.base.vo.update.BaseSecurityCodeAttributeDetailsUpdateVO;
import top.kx.kxss.base.vo.result.BaseSecurityCodeAttributeDetailsResultVO;
import top.kx.kxss.base.vo.query.BaseSecurityCodeAttributeDetailsPageQuery;


/**
 * <p>
 * 业务接口
 * 防伪信息规格属性
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-17 10:23:59
 * @create [2025-06-17 10:23:59] [yan] [代码生成器生成]
 */
public interface BaseSecurityCodeAttributeDetailsService extends SuperService<Long, BaseSecurityCodeAttributeDetails, BaseSecurityCodeAttributeDetailsSaveVO,
    BaseSecurityCodeAttributeDetailsUpdateVO, BaseSecurityCodeAttributeDetailsPageQuery, BaseSecurityCodeAttributeDetailsResultVO> {

}


