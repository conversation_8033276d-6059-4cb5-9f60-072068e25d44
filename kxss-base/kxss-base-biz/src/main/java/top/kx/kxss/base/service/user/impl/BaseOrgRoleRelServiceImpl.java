package top.kx.kxss.base.service.user.impl;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.kxss.base.entity.user.BaseOrgRoleRel;
import top.kx.kxss.base.manager.user.BaseOrgRoleRelManager;
import top.kx.kxss.base.service.user.BaseOrgRoleRelService;
import top.kx.kxss.base.vo.query.user.BaseOrgRoleRelPageQuery;
import top.kx.kxss.base.vo.result.user.BaseOrgRoleRelResultVO;
import top.kx.kxss.base.vo.save.user.BaseOrgRoleRelSaveVO;
import top.kx.kxss.base.vo.update.user.BaseOrgRoleRelUpdateVO;


/**
 * <p>
 * 业务实现类
 * 组织的角色
 * </p>
 *
 * <AUTHOR>
 * @date 2021-10-18
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)

public class BaseOrgRoleRelServiceImpl extends SuperServiceImpl<BaseOrgRoleRelManager, Long, BaseOrgRoleRel, BaseOrgRoleRelSaveVO, BaseOrgRoleRelUpdateVO, BaseOrgRoleRelPageQuery, BaseOrgRoleRelResultVO> implements BaseOrgRoleRelService {

}
