package top.kx.kxss.base.service.user;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.base.entity.user.BaseOrgRoleRel;
import top.kx.kxss.base.vo.query.user.BaseOrgRoleRelPageQuery;
import top.kx.kxss.base.vo.result.user.BaseOrgRoleRelResultVO;
import top.kx.kxss.base.vo.save.user.BaseOrgRoleRelSaveVO;
import top.kx.kxss.base.vo.update.user.BaseOrgRoleRelUpdateVO;

/**
 * <p>
 * 业务接口
 * 组织的角色
 * </p>
 *
 * <AUTHOR>
 * @date 2021-10-18
 */
public interface BaseOrgRoleRelService extends SuperService<Long, BaseOrgRoleRel, BaseOrgRoleRelSaveVO, BaseOrgRoleRelUpdateVO, BaseOrgRoleRelPageQuery, BaseOrgRoleRelResultVO> {

}
