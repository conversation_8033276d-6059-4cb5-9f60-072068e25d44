package top.kx.kxss.base.service.product.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.base.entity.product.BaseProductAttribute;
import top.kx.kxss.base.manager.product.BaseProductAttributeManager;
import top.kx.kxss.base.service.product.BaseProductAttributeService;
import top.kx.kxss.base.vo.query.product.BaseProductAttributePageQuery;
import top.kx.kxss.base.vo.result.product.BaseProductAttributeResultVO;
import top.kx.kxss.base.vo.save.product.BaseProductAttributeSaveVO;
import top.kx.kxss.base.vo.update.product.BaseProductAttributeUpdateVO;

/**
 * <p>
 * 业务实现类
 * 商品基础属性关联
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-16 18:44:16
 * @create [2025-06-16 18:44:16] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
//@Transactional(readOnly = true)
public class BaseProductAttributeServiceImpl extends SuperServiceImpl<BaseProductAttributeManager, Long, BaseProductAttribute, BaseProductAttributeSaveVO,
    BaseProductAttributeUpdateVO, BaseProductAttributePageQuery, BaseProductAttributeResultVO> implements BaseProductAttributeService {


    @Override
    public Boolean remove(LbQueryWrap<BaseProductAttribute> eq) {
        return superManager.remove(eq);
    }
}


