package top.kx.kxss.base.service.user;

import org.springframework.web.multipart.MultipartFile;
import top.kx.basic.base.service.SuperService;
import top.kx.kxss.base.entity.user.UserBackground;
import top.kx.kxss.base.vo.save.user.UserBackgroundSaveVO;
import top.kx.kxss.base.vo.update.user.UserBackgroundUpdateVO;
import top.kx.kxss.base.vo.result.user.UserBackgroundResultVO;
import top.kx.kxss.base.vo.query.user.UserBackgroundPageQuery;


/**
 * <p>
 * 业务接口
 * 用户背景图
 * </p>
 *
 * <AUTHOR>
 * @date 2025-04-19 16:25:46
 * @create [2025-04-19 16:25:46] [yan] [代码生成器生成]
 */
public interface UserBackgroundService extends SuperService<Long, UserBackground, UserBackgroundSaveVO,
        UserBackgroundUpdateVO, UserBackgroundPageQuery, UserBackgroundResultVO> {

    UserBackground upload(MultipartFile file, String appKey);

    UserBackgroundResultVO getOneByAppKey(String appKey);

}


