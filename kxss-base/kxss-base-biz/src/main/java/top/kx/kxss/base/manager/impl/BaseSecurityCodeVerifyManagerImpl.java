package top.kx.kxss.base.manager.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.base.entity.BaseSecurityCodeVerify;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.base.manager.BaseSecurityCodeVerifyManager;
import top.kx.kxss.base.mapper.BaseSecurityCodeVerifyMapper;

/**
 * <p>
 * 通用业务实现类
 * 防伪验证信息
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-06 16:18:00
 * @create [2025-06-06 16:18:00] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BaseSecurityCodeVerifyManagerImpl extends SuperManagerImpl<BaseSecurityCodeVerifyMapper, BaseSecurityCodeVerify> implements BaseSecurityCodeVerifyManager {

}


