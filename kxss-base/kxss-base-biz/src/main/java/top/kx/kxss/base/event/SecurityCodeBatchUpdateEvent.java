package top.kx.kxss.base.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;
import top.kx.kxss.base.entity.security.BaseSecurityCodeBatch;
import top.kx.kxss.base.vo.update.security.BaseSecurityCodeBatchUpdateVO;

@Getter
public class SecurityCodeBatchUpdateEvent extends ApplicationEvent {
    private final BaseSecurityCodeBatch batch;
    private final BaseSecurityCodeBatchUpdateVO batchUpdateVO;

    public SecurityCodeBatchUpdateEvent(Object source, BaseSecurityCodeBatch batch, BaseSecurityCodeBatchUpdateVO batchUpdateVO) {
        super(source);
        this.batch = batch;
        this.batchUpdateVO = batchUpdateVO;
    }
}