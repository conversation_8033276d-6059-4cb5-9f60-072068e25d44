package top.kx.kxss.base.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.base.entity.BaseSecurityCodeVerify;
import top.kx.kxss.base.manager.BaseSecurityCodeVerifyManager;
import top.kx.kxss.base.service.BaseSecurityCodeVerifyService;
import top.kx.kxss.base.vo.query.BaseSecurityCodeVerifyPageQuery;
import top.kx.kxss.base.vo.result.BaseSecurityCodeVerifyResultVO;
import top.kx.kxss.base.vo.save.BaseSecurityCodeVerifySaveVO;
import top.kx.kxss.base.vo.update.BaseSecurityCodeVerifyUpdateVO;

/**
 * <p>
 * 业务实现类
 * 防伪验证信息
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-06 16:18:00
 * @create [2025-06-06 16:18:00] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
//@Transactional(readOnly = true)
public class BaseSecurityCodeVerifyServiceImpl extends SuperServiceImpl<BaseSecurityCodeVerifyManager, Long, BaseSecurityCodeVerify, BaseSecurityCodeVerifySaveVO,
    BaseSecurityCodeVerifyUpdateVO, BaseSecurityCodeVerifyPageQuery, BaseSecurityCodeVerifyResultVO> implements BaseSecurityCodeVerifyService {


    @Override
    public BaseSecurityCodeVerify getOne(LbQueryWrap<BaseSecurityCodeVerify> wrap) {
        return superManager.getOne(wrap);
    }
}


