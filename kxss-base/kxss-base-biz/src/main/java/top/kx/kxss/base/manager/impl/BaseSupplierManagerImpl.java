package top.kx.kxss.base.manager.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.base.entity.supplier.BaseSupplier;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.base.manager.BaseSupplierManager;
import top.kx.kxss.base.mapper.BaseSupplierMapper;

/**
 * <p>
 * 通用业务实现类
 * 供应商信息信息
 * </p>
 *
 * <AUTHOR>
 * @date 2025-02-28 16:36:35
 * @create [2025-02-28 16:36:35] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BaseSupplierManagerImpl extends SuperManagerImpl<BaseSupplierMapper, BaseSupplier> implements BaseSupplierManager {

}


