//package top.kx.kxss.base.service.user;
//
//import top.kx.kxss.base.vo.query.user.BaseEmployeePageQuery;
//import top.kx.kxss.base.vo.result.user.BaseEmployeeResultVO;
//import top.kx.kxss.base.vo.save.user.BaseDistributorSaveVO;
//
//import java.util.List;
//
///**
// * <p>
// * 业务接口
// * 经销商
// * </p>
// *
// * <AUTHOR>
// * @date 2021-10-18
// */
//public interface BaseDistributorService {
//
//    /**
//     * 保存
//     * @param model 实体
//     * @return
//     */
//    BaseEmployeeResultVO save(BaseDistributorSaveVO model);
//
//    List<BaseEmployeeResultVO> query(BaseEmployeePageQuery query);
//}
