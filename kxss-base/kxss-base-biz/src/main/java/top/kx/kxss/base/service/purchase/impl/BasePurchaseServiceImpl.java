package top.kx.kxss.base.service.purchase.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.entity.SuperEntity;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.QueryWrap;
import top.kx.basic.utils.DateUtils;
import top.kx.kxss.base.entity.BaseSecurityCode;
import top.kx.kxss.base.entity.distributor.BaseDistributor;
import top.kx.kxss.base.entity.product.BaseProduct;
import top.kx.kxss.base.entity.purchase.BasePurchase;
import top.kx.kxss.base.entity.purchase.BasePurchaseDetails;
import top.kx.kxss.base.manager.BaseSecurityCodeManager;
import top.kx.kxss.base.manager.purchase.BasePurchaseDetailsManager;
import top.kx.kxss.base.manager.purchase.BasePurchaseManager;
import top.kx.kxss.base.service.distributor.BaseDistributorService;
import top.kx.kxss.base.service.product.BaseProductService;
import top.kx.kxss.base.service.purchase.BasePurchaseService;
import top.kx.kxss.base.vo.query.purchase.BasePurchasePageQuery;
import top.kx.kxss.base.vo.result.distributor.BaseDistributorPurchaseResultVO;
import top.kx.kxss.base.vo.result.purchase.BasePurchaseResultVO;
import top.kx.kxss.base.vo.result.purchase.BasePurchaseSumResultVO;
import top.kx.kxss.base.vo.save.purchase.BasePurchaseSaveVO;
import top.kx.kxss.base.vo.update.purchase.BasePurchaseUpdateVO;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static top.kx.basic.utils.DateUtils.DEFAULT_DATE_FORMAT;
import static top.kx.basic.utils.DateUtils.DEFAULT_DATE_TIME_FORMAT;

/**
 * <p>
 * 业务实现类
 * 要货
 * </p>
 *
 * <AUTHOR>
 * @date 2025-03-10 14:57:47
 * @create [2025-03-10 14:57:47] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
//@Transactional(readOnly = true)
public class BasePurchaseServiceImpl extends SuperServiceImpl<BasePurchaseManager, Long, BasePurchase, BasePurchaseSaveVO,
        BasePurchaseUpdateVO, BasePurchasePageQuery, BasePurchaseResultVO> implements BasePurchaseService {

    private final BaseSecurityCodeManager baseSecurityCodeManager;
    private final BaseDistributorService baseDistributorService;
    private final BaseProductService baseProductService;
    private final BasePurchaseDetailsManager basePurchaseDetailsManager;

    @Override
    protected BasePurchase saveBefore(BasePurchaseSaveVO basePurchaseSaveVO) {
        basePurchaseSaveVO.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
        basePurchaseSaveVO.setEmployeeId(ContextUtil.getEmployeeId());
        // 查询最新的一条数据
        BasePurchase basePurchase = superManager.getOne(Wraps.<BasePurchase>lbQ()
                .orderByDesc(BasePurchase::getCreatedTime)
                .last("limit 1"));
        if (Objects.nonNull(basePurchase) && StringUtils.equals(DateUtils.format(basePurchase.getCreatedTime(), DEFAULT_DATE_FORMAT), DateUtils.format(LocalDateTime.now(), DEFAULT_DATE_FORMAT))) {
            long code = Long.parseLong(basePurchase.getCode()) + 1L;
            basePurchaseSaveVO.setCode(Long.toString(code));
        } else {
            basePurchaseSaveVO.setCode(DateUtils.format(LocalDateTime.now(), "yyyyMMdd") + "00001");
        }
        basePurchaseSaveVO.setNum(0);
        return super.saveBefore(basePurchaseSaveVO);
    }

    @Override
    public Boolean addNum(Long id, Integer num) {
        return superManager.addNum(id, num);
    }

    @Override
    public Boolean reduceNum(Long id, Integer num) {
        return superManager.reduceNum(id, num);
    }

    @Override
    public List<BaseDistributorPurchaseResultVO> purchaseSumList(List<Long> ids) {
        QueryWrapper<BasePurchase> wrapper = new QueryWrapper<>();
        wrapper.in("distributor_id", ids).eq("delete_flag", 0);
        wrapper.select("distributor_id as distributorId", "SUM(num) as purchaseNum", "min(created_time) as purchaseTime", "count(id) as purchaseCount");
        wrapper.groupBy("distributor_id");

        List<Map<String, Object>> result = superManager.listMaps(wrapper);
        List<BaseDistributorPurchaseResultVO> resultVOList = new ArrayList<>();
        for (Map<String, Object> map : result) {
            Long distributorId = (Long) map.get("distributorId");
            Long purchaseCount = (Long) map.get("purchaseCount");
            Integer purchaseNum = ((Number) map.get("purchaseNum")).intValue();
            LocalDateTime purchaseTime = (LocalDateTime) map.get("purchaseTime");
            resultVOList.add(BaseDistributorPurchaseResultVO.builder()
                    .distributorId(distributorId)
                    .purchaseNum(purchaseNum)
                    .purchaseTime(purchaseTime)
                    .purchaseCount(purchaseCount)
                    .build());
        }
        return resultVOList;
    }

    @Override
    public BasePurchaseSumResultVO querySum(BasePurchasePageQuery query) {
        BasePurchaseSumResultVO result = new BasePurchaseSumResultVO();
        QueryWrap<BasePurchase> wraps = new QueryWrap<>();
        wraps.lambda().eq(BasePurchase::getDistributorId, query.getDistributorId());
        wraps.select("SUM(num) as num");
        BasePurchase basePurchase = superManager.getOne(wraps);
        if (Objects.isNull(basePurchase)) {
            result.setTotalNum(0);
            result.setNum(0);
        } else {
            result.setTotalNum(basePurchase.getNum());
            result.setNum(basePurchase.getNum());
        }

        QueryWrap<BasePurchase> queryWrap = new QueryWrap<>();
        queryWrap.lambda()
                .eq(BasePurchase::getDistributorId, query.getDistributorId())
                .eq(BasePurchase::getProductId, query.getProductId())
                .eq(BasePurchase::getEmployeeId, query.getEmployeeId());
        if (StringUtils.isNotBlank(query.getStartDate()) && StringUtils.isNotBlank(query.getEndDate())) {
            queryWrap.lambda().between(BasePurchase::getCreatedTime,
                    DateUtils.format(DateUtils.getStartTime(query.getStartDate()), DEFAULT_DATE_TIME_FORMAT),
                    DateUtils.format(DateUtils.getEndTime(query.getEndDate()), DEFAULT_DATE_TIME_FORMAT));
        }
        if (StringUtils.isNotBlank(query.getKeyword())) {
            List<Long> distributorIds = baseDistributorService.list(Wraps.<BaseDistributor>lbQ()
                            .like(BaseDistributor::getName, query.getKeyword()))
                    .stream().map(BaseDistributor::getId).collect(Collectors.toList());
            List<Long> productIds = baseProductService.list(Wraps.<BaseProduct>lbQ()
                            .like(BaseProduct::getName, query.getKeyword()))
                    .stream().map(BaseProduct::getId).collect(Collectors.toList());
            queryWrap.lambda().and(wrap -> wrap
                    .like(BasePurchase::getCode, query.getKeyword())
                    .or()
                    .in(CollUtil.isNotEmpty(productIds), BasePurchase::getProductId, productIds)
                    .or()
                    .in(CollUtil.isNotEmpty(distributorIds), BasePurchase::getDistributorId, distributorIds)
            );
        }
        queryWrap.select("count(distinct product_id) productNum", "IFNULL(sum(num), 0) as purchaseNum");
        Map<String, Object> objectMap = superManager.getMap(queryWrap);
        Long productNum = (long) objectMap.get("productNum");
        Long purchaseNum = 0L;
        if (Objects.nonNull(objectMap.get("purchaseNum"))) {
            purchaseNum = Long.parseLong(objectMap.get("purchaseNum").toString());
        }
        result.setProductNum(productNum);
        result.setPurchaseNum(purchaseNum);
        
        // 添加下级经销商拿货数量统计
        if (Objects.nonNull(query.getDistributorId())) {
            // 查询下级经销商
            List<BaseDistributor> childDistributors = baseDistributorService.list(
                Wraps.<BaseDistributor>lbQ()
                    .eq(BaseDistributor::getParentId, query.getDistributorId())
                    .eq(BaseDistributor::getDeleteFlag, 0)
            );
            
            if (CollUtil.isNotEmpty(childDistributors)) {
                List<Long> childDistributorIds = childDistributors.stream()
                        .map(BaseDistributor::getId)
                        .collect(Collectors.toList());
                
                // 直接统计下级经销商的总拿货数量
                QueryWrapper<BasePurchase> childWrapper = new QueryWrapper<>();
                childWrapper.in("distributor_id", childDistributorIds).eq("delete_flag", 0);
                childWrapper.select("IFNULL(SUM(num), 0) as num");
                BasePurchase childPurchase = superManager.getOne(childWrapper);
                if (Objects.nonNull(childPurchase) ) {
                    result.setChildNum(childPurchase.getNum());
                } else {
                    result.setChildNum(0);
                }
            } else {
                result.setChildNum(0);
            }
        } else {
            result.setChildNum(0);
        }
        return result;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean removeByIds(Collection<Long> idList) {
        boolean remove = super.removeByIds(idList);
        if (remove) {
            basePurchaseDetailsManager.update(Wraps.<BasePurchaseDetails>lbU().set(SuperEntity::getDeleteFlag, 1)
                    .in(BasePurchaseDetails::getPurchaseId, idList));
        }
        return remove;
    }


    @Override
    public Boolean refreshPurchase() {
        // 查询在在防伪码表中， 但是不在要货表表中
        List<BasePurchase> purchaseList = superManager.list(Wraps.<BasePurchase>lbQ().inSql(BasePurchase::getId, "select distinct bsc.purchase_id " +
                "                from base_security_code bsc " +
                "                         left join base_purchase_details bpd " +
                "                                   on bpd.purchase_id = bsc.purchase_id and bpd.delete_flag = 0 " +
                "                where bsc.purchase_id is not null and bpd.id is null" +
                "                  and bsc.delete_flag = 0"));
        if (CollUtil.isEmpty(purchaseList)) {
            return true;
        }
        List<BaseProduct> productList = baseProductService.list(Wraps.lbQ());
        Map<Long, BaseProduct> productMap = productList.stream().collect(Collectors.toMap(BaseProduct::getId, Function.identity()));
        for (BasePurchase basePurchase : purchaseList) {
            List<BaseSecurityCode> securityCodeList = baseSecurityCodeManager.list(Wraps.<BaseSecurityCode>lbQ().eq(BaseSecurityCode::getPurchaseId, basePurchase.getId()));
            if (CollUtil.isEmpty(securityCodeList)) {
                continue;
            }
            BaseProduct baseProduct = productMap.get(basePurchase.getProductId());
            basePurchaseDetailsManager.saveBatch(securityCodeList.stream().map(securityCode ->{
                        BasePurchaseDetails basePurchaseDetails = BasePurchaseDetails.builder()
                                .securityCode(securityCode.getSecurityCode())
                                .productId(securityCode.getProductId())
                                .productName(Objects.nonNull(baseProduct) ? baseProduct.getName() : "-")
                                .distributorId(basePurchase.getDistributorId())
                                .purchaseId(basePurchase.getId())
                                .purchaseEmp(securityCode.getPurchaseControlBy())
                                .purchaseEnterType(securityCode.getPurchaseEnterType())
                                .remarks(securityCode.getPurchaseRemarks())
                                .createdOrgId(securityCode.getCreatedOrgId())
                                .build();
                        basePurchaseDetails.setCreatedTime(securityCode.getPurchaseControlTime());
                        return basePurchaseDetails;
                    }).collect(Collectors.toList()));
        }
        return true;
    }
}


