package top.kx.kxss.base.service.dealers;

import top.kx.basic.base.service.SuperService;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.base.entity.dealers.BaseDealers;
import top.kx.kxss.base.vo.save.dealers.BaseDealersSaveVO;
import top.kx.kxss.base.vo.update.dealers.BaseDealersUpdateVO;
import top.kx.kxss.base.vo.result.dealers.BaseDealersResultVO;
import top.kx.kxss.base.vo.query.dealers.BaseDealersPageQuery;


/**
 * <p>
 * 业务接口
 * 经销商信息
 * </p>
 *
 * <AUTHOR>
 * @date 2024-12-20 10:47:35
 * @create [2024-12-20 10:47:35] [dou] [代码生成器生成]
 */
public interface BaseDealersService extends SuperService<Long, BaseDealers, BaseDealersSaveVO,
    BaseDealersUpdateVO, BaseDealersPageQuery, BaseDealersResultVO> {

    BaseDealers getOne(LbQueryWrap<BaseDealers> queryWrap);

    boolean saveOrUpdate(BaseDealers dealers);
}


