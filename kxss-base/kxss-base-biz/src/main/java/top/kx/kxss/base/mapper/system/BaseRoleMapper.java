package top.kx.kxss.base.mapper.system;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import top.kx.basic.base.mapper.SuperMapper;
import top.kx.kxss.base.entity.system.BaseRole;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * 角色
 * </p>
 *
 * <AUTHOR>
 * @date 2021-10-18
 */
@Repository
public interface BaseRoleMapper extends SuperMapper<BaseRole> {

    /**
     * 根据角色id查询员工id
     *
     * @param roleIds roleIds
     * @return java.util.List<java.lang.Long>
     * <AUTHOR>
     * @date 2021/12/28 12:22 上午
     * @create [2021/12/28 12:22 上午 ] [tangyh] [初始创建]
     */
    List<Long> listEmployeeIdByRoleId(@Param("roleIds") List<Long> roleIds);

    /**
     * 根据机构id查询对应的角色
     *
     * @param orgId orgId
     * @return java.util.List<java.lang.Long>
     * <AUTHOR>
     * @date 2022/10/20 4:06 PM
     * @create [2022/10/20 4:06 PM ] [tangyh] [初始创建]
     */
    List<Long> selectRoleIdByOrgId(@Param("orgId") Long orgId);

    /**
     * 查询员工拥有的角色
     *
     * @param employeeId employeeId
     * @return java.util.List<java.lang.Long>
     * <AUTHOR>
     * @date 2022/10/20 3:44 PM
     * @create [2022/10/20 3:44 PM ] [tangyh] [初始创建]
     */
    List<Long> selectRoleByEmployeeId(@Param("employeeId") Long employeeId);

}
