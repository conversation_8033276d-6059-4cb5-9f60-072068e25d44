package top.kx.kxss.base.manager.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.base.entity.BaseSecurityCodeAttributeDetails;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.base.manager.BaseSecurityCodeAttributeDetailsManager;
import top.kx.kxss.base.mapper.BaseSecurityCodeAttributeDetailsMapper;

/**
 * <p>
 * 通用业务实现类
 * 防伪信息规格属性
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-17 10:23:59
 * @create [2025-06-17 10:23:59] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BaseSecurityCodeAttributeDetailsManagerImpl extends SuperManagerImpl<BaseSecurityCodeAttributeDetailsMapper, BaseSecurityCodeAttributeDetails> implements BaseSecurityCodeAttributeDetailsManager {

}


