package top.kx.kxss.msg.strategy.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2022/7/25 10:43 PM
 * @create [2022/7/25 10:43 PM ] [tangyh] [初始创建]
 */
@Data
@RequiredArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class MsgResult {
    private String title;
    private String content;
    private Object result;
}
