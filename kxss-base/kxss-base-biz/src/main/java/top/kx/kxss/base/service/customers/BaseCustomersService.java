package top.kx.kxss.base.service.customers;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.base.entity.customers.BaseCustomers;
import top.kx.kxss.base.vo.save.customers.BaseCustomersSaveVO;
import top.kx.kxss.base.vo.update.customers.BaseCustomersUpdateVO;
import top.kx.kxss.base.vo.result.customers.BaseCustomersResultVO;
import top.kx.kxss.base.vo.query.customers.BaseCustomersPageQuery;


/**
 * <p>
 * 业务接口
 * 客户咨询信息
 * </p>
 *
 * <AUTHOR>
 * @date 2024-12-19 10:30:39
 * @create [2024-12-19 10:30:39] [dou] [代码生成器生成]
 */
public interface BaseCustomersService extends SuperService<Long, BaseCustomers, BaseCustomersSaveVO,
    BaseCustomersUpdateVO, BaseCustomersPageQuery, BaseCustomersResultVO> {

    Boolean receive(Long id);
}


