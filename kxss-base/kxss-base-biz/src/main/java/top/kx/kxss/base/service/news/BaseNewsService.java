package top.kx.kxss.base.service.news;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.base.entity.news.BaseNews;
import top.kx.kxss.base.vo.query.news.BaseNewsPageQuery;
import top.kx.kxss.base.vo.result.news.BaseNewsResultVO;
import top.kx.kxss.base.vo.save.news.BaseNewsSaveVO;
import top.kx.kxss.base.vo.update.news.BaseNewsUpdateVO;


/**
 * <p>
 * 业务接口
 * 新闻资讯
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-25 16:12:08
 * @create [2023-05-25 16:12:08] [dou] [代码生成器生成]
 */
public interface BaseNewsService extends SuperService<Long, BaseNews, BaseNewsSaveVO,
    BaseNewsUpdateVO, BaseNewsPageQuery, BaseNewsResultVO> {

    /**
     * 查询新闻信息
     * @param id
     * @return
     */
    BaseNewsResultVO getInfoById(Long id);

    /**
     * 撤销
     * @param id
     * @return
     */
    Boolean revoke(Long id);
}


