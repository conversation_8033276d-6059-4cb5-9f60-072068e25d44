package top.kx.kxss.base.service.print.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.base.entity.print.BasePrintTemplate;
import top.kx.kxss.base.manager.print.BasePrintTemplateManager;
import top.kx.kxss.base.service.print.BasePrintTemplateService;
import top.kx.kxss.base.vo.query.print.BasePrintTemplatePageQuery;
import top.kx.kxss.base.vo.result.print.BasePrintTemplateResultVO;
import top.kx.kxss.base.vo.save.print.BasePrintTemplateSaveVO;
import top.kx.kxss.base.vo.update.print.BasePrintTemplateUpdateVO;

import java.util.List;

/**
 * <p>
 * 业务实现类
 * 打印模板
 * </p>
 *
 * <AUTHOR>
 * @date 2025-08-27 10:48:00
 * @create [2025-08-27 10:48:00] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
//@Transactional(readOnly = true)
public class BasePrintTemplateServiceImpl extends SuperServiceImpl<BasePrintTemplateManager, Long, BasePrintTemplate, BasePrintTemplateSaveVO,
    BasePrintTemplateUpdateVO, BasePrintTemplatePageQuery, BasePrintTemplateResultVO> implements BasePrintTemplateService {

    private final EchoService echoService;

    @Override
    public BasePrintTemplate save(BasePrintTemplateSaveVO basePrintTemplateSaveVO) {
        superManager.remove(Wraps.<BasePrintTemplate>lbQ().eq(BasePrintTemplate::getProductId, basePrintTemplateSaveVO.getProductId()));
        basePrintTemplateSaveVO.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
        return super.save(basePrintTemplateSaveVO);
    }

    @Override
    public List<BasePrintTemplateResultVO> getListByProductId(Long productId) {
        List<BasePrintTemplate> printTemplateList = superManager.list(Wraps.<BasePrintTemplate>lbQ().eq(BasePrintTemplate::getProductId, productId));
        List<BasePrintTemplateResultVO> resultVOList = BeanPlusUtil.toBeanList(printTemplateList, BasePrintTemplateResultVO.class);
        echoService.action(resultVOList);
        return resultVOList;
    }
}


