package top.kx.kxss.base.manager.purchase;

import top.kx.basic.base.manager.SuperManager;
import top.kx.kxss.base.entity.purchase.BasePurchase;

/**
 * <p>
 * 通用业务接口
 * 要货
 * </p>
 *
 * <AUTHOR>
 * @date 2025-03-10 14:57:47
 * @create [2025-03-10 14:57:47] [yan] [代码生成器生成]
 */
public interface BasePurchaseManager extends SuperManager<BasePurchase> {

    Boolean addNum(Long id, Integer num);
    Boolean reduceNum(Long id, Integer num);

}


