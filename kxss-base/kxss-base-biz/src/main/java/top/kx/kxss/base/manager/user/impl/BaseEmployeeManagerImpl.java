package top.kx.kxss.base.manager.user.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.manager.impl.SuperCacheManagerImpl;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.model.cache.CacheKeyBuilder;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.CollHelper;
import top.kx.kxss.base.entity.user.BaseEmployee;
import top.kx.kxss.base.manager.user.BaseEmployeeManager;
import top.kx.kxss.base.mapper.user.BaseEmployeeMapper;
import top.kx.kxss.base.vo.result.user.BaseEmployeeResultVO;
import top.kx.kxss.common.cache.base.user.EmployeeCacheKeyBuilder;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 通用业务实现类
 * 员工
 * </p>
 *
 * <AUTHOR>
 * @date 2021-10-18
 * @create [2021-10-18] [zuihou] [代码生成器生成]
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BaseEmployeeManagerImpl extends SuperCacheManagerImpl<BaseEmployeeMapper, BaseEmployee> implements BaseEmployeeManager {


    @Transactional(readOnly = true)
    @Override
    public Map<Serializable, Object> findByIds(Set<Serializable> ids) {
        List<BaseEmployee> list = findByIds(ids, null).stream().filter(Objects::nonNull).collect(Collectors.toList());
        return CollHelper.uniqueIndex(list, BaseEmployee::getId, BaseEmployee::getRealName);
    }

    @Override
    protected CacheKeyBuilder cacheKeyBuilder() {
        return new EmployeeCacheKeyBuilder();
    }

    @Override
    public List<BaseEmployeeResultVO> listEmployeeByUserId(Long userId) {
        return baseMapper.listEmployeeByUserId(userId);
    }

    @Override
    public IPage<BaseEmployeeResultVO> selectPageResultVO(IPage<BaseEmployee> page, Wrapper<BaseEmployee> wrapper) {
        return baseMapper.selectPageResultVO(page, wrapper);
    }

    @Override
    public BaseEmployee getEmployeeByUser(Long userId) {
        ArgumentAssert.notNull(userId, "用户id为空");
        return baseMapper.selectOne(Wraps.<BaseEmployee>lbQ().eq(BaseEmployee::getUserId, userId));
    }
}
