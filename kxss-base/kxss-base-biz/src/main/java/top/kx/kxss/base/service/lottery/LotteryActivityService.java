package top.kx.kxss.base.service.lottery;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.base.entity.lottery.LotteryActivity;
import top.kx.kxss.base.vo.result.lottery.LotteryChanceResultVO;
import top.kx.kxss.base.vo.result.prize.DefPrizeResultVO;
import top.kx.kxss.base.vo.save.lottery.LotteryActivitySaveVO;
import top.kx.kxss.base.vo.update.lottery.LotteryActivityStatusUpdateVO;
import top.kx.kxss.base.vo.update.lottery.LotteryActivityUpdateVO;
import top.kx.kxss.base.vo.result.lottery.LotteryActivityResultVO;
import top.kx.kxss.base.vo.query.lottery.LotteryActivityPageQuery;
import top.kx.kxss.model.enumeration.base.LotteryActivityTypeEnum;


/**
 * <p>
 * 业务接口
 * 抽奖活动表
 * </p>
 *
 * <AUTHOR>
 * @date 2025-09-15 17:23:11
 * @create [2025-09-15 17:23:11] [yan] [代码生成器生成]
 */
public interface LotteryActivityService extends SuperService<Long, LotteryActivity, LotteryActivitySaveVO,
    LotteryActivityUpdateVO, LotteryActivityPageQuery, LotteryActivityResultVO> {

    Boolean updateStatus(LotteryActivityStatusUpdateVO updateVO);


    LotteryActivityResultVO getDetail(Long id);

    /**
     * 获取用户抽奖机会
     * @param type 类型：1-注册抽奖
     * @param source  来源，防伪码等
     * @return
     */
    LotteryChanceResultVO getChance(LotteryActivityTypeEnum type, String source);

    DefPrizeResultVO lottery(Long id);
}


