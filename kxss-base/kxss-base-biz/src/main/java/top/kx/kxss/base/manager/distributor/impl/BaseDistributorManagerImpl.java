package top.kx.kxss.base.manager.distributor.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.manager.impl.SuperCacheManagerImpl;
import top.kx.basic.model.cache.CacheKeyBuilder;
import top.kx.basic.utils.CollHelper;
import top.kx.kxss.base.entity.dealers.BaseDealers;
import top.kx.kxss.base.entity.distributor.BaseDistributor;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.base.manager.distributor.BaseDistributorManager;
import top.kx.kxss.base.mapper.distributor.BaseDistributorMapper;
import top.kx.kxss.common.cache.base.user.DealersCacheKeyBuilder;
import top.kx.kxss.common.cache.base.user.DistributorCacheKeyBuilder;
import top.kx.kxss.common.cache.base.user.ProductCacheKeyBuilder;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 通用业务实现类
 * 耗材经销商信息
 * </p>
 *
 * <AUTHOR>
 * @date 2025-03-07 15:45:07
 * @create [2025-03-07 15:45:07] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BaseDistributorManagerImpl extends SuperCacheManagerImpl<BaseDistributorMapper, BaseDistributor> implements BaseDistributorManager {

    @Transactional(readOnly = true)
    @Override
    public Map<Serializable, Object> findByIds(Set<Serializable> ids) {
        List<BaseDistributor> list = findByIds(ids, null).stream().filter(Objects::nonNull).collect(Collectors.toList());
        return CollHelper.uniqueIndex(list, BaseDistributor::getId, BaseDistributor::getName);
    }

    @Override
    protected CacheKeyBuilder cacheKeyBuilder() {
        return new DistributorCacheKeyBuilder();
    }
}


