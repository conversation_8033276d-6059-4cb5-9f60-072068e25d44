package top.kx.kxss.base.service.product;

import top.kx.basic.base.service.SuperService;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.base.entity.product.BaseProductAttribute;
import top.kx.kxss.base.vo.save.product.BaseProductAttributeSaveVO;
import top.kx.kxss.base.vo.update.product.BaseProductAttributeUpdateVO;
import top.kx.kxss.base.vo.result.product.BaseProductAttributeResultVO;
import top.kx.kxss.base.vo.query.product.BaseProductAttributePageQuery;


/**
 * <p>
 * 业务接口
 * 商品基础属性关联
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-16 18:44:16
 * @create [2025-06-16 18:44:16] [yan] [代码生成器生成]
 */
public interface BaseProductAttributeService extends SuperService<Long, BaseProductAttribute, BaseProductAttributeSaveVO,
    BaseProductAttributeUpdateVO, BaseProductAttributePageQuery, BaseProductAttributeResultVO> {

    Boolean remove(LbQueryWrap<BaseProductAttribute> eq);
}


