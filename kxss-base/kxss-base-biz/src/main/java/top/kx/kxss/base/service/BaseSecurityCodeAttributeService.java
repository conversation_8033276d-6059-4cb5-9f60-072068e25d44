package top.kx.kxss.base.service;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.base.entity.BaseSecurityCodeAttribute;
import top.kx.kxss.base.vo.query.BaseSecurityCodeAttributePageQuery;
import top.kx.kxss.base.vo.result.BaseSecurityCodeAttributeDetailsResultVO;
import top.kx.kxss.base.vo.result.BaseSecurityCodeAttributeResultVO;
import top.kx.kxss.base.vo.save.BaseSecurityCodeAddAttributeVO;
import top.kx.kxss.base.vo.save.BaseSecurityCodeAttributeSaveVO;
import top.kx.kxss.base.vo.update.BaseSecurityCodeAttributeUpdateVO;

import java.util.List;


/**
 * <p>
 * 业务接口
 * 防伪信息规格属性
 * </p>
 *
 * <AUTHOR>
 * @date 2025-02-26 16:17:06
 * @create [2025-02-26 16:17:06] [yan] [代码生成器生成]
 */
public interface BaseSecurityCodeAttributeService extends SuperService<Long, BaseSecurityCodeAttribute, BaseSecurityCodeAttributeSaveVO,
    BaseSecurityCodeAttributeUpdateVO, BaseSecurityCodeAttributePageQuery, BaseSecurityCodeAttributeResultVO> {

    BaseSecurityCodeAttributeResultVO saveAttribute(BaseSecurityCodeAddAttributeVO saveVO);

    BaseSecurityCodeAttributeResultVO updateAttribute(BaseSecurityCodeAddAttributeVO saveVO);

    BaseSecurityCodeAttributeResultVO getDetail(Long id);

    List<BaseSecurityCodeAttributeDetailsResultVO> getAttributeDetailList(Long securityCodeId);
}


