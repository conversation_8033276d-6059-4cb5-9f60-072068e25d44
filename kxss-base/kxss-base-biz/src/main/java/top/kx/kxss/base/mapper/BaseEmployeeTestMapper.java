package top.kx.kxss.base.mapper;

import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import top.kx.basic.annotation.database.TenantLine;
import top.kx.basic.base.mapper.SuperMapper;
import top.kx.kxss.base.entity.user.BaseEmployee;


/**
 * 仅仅测试使用
 *
 * <AUTHOR>
 * @date 2021-10-18
 */
@Repository
@TenantLine
public interface BaseEmployeeTestMapper extends SuperMapper<BaseEmployee> {
    /**
     * get
     *
     * @param id id
     * @return top.kx.kxss.base.entity.user.BaseEmployee
     * <AUTHOR>
     * @date 2022/10/28 4:38 PM
     */
    @TenantLine(false)
    @Select("select * from base_employee where id = #{id}")
    BaseEmployee get(Long id);

}
