package top.kx.kxss.base.manager.question.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.base.entity.question.BaseQuestion;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.base.manager.question.BaseQuestionManager;
import top.kx.kxss.base.mapper.question.BaseQuestionMapper;

/**
 * <p>
 * 通用业务实现类
 * 常见问题
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-26 16:24:10
 * @create [2023-05-26 16:24:10] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BaseQuestionManagerImpl extends SuperManagerImpl<BaseQuestionMapper, BaseQuestion> implements BaseQuestionManager {

}


