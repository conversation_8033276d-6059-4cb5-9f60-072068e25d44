package top.kx.kxss.base.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.util.IOUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import top.kx.basic.base.entity.SuperEntity;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.cache.lock.DistributedLock;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.*;
import top.kx.kxss.base.entity.BaseSecurityCode;
import top.kx.kxss.base.entity.BaseSecurityCodeAttribute;
import top.kx.kxss.base.entity.BaseSecurityCodeAttributeDetails;
import top.kx.kxss.base.entity.BaseSecurityCodeVerify;
import top.kx.kxss.base.entity.attribute.BaseAttribute;
import top.kx.kxss.base.entity.attribute.BaseAttributeValue;
import top.kx.kxss.base.entity.dealers.BaseDealers;
import top.kx.kxss.base.entity.distributor.BaseDistributor;
import top.kx.kxss.base.entity.member.MemberInfo;
import top.kx.kxss.base.entity.product.BaseProduct;
import top.kx.kxss.base.entity.product.BaseProductAttribute;
import top.kx.kxss.base.entity.purchase.BasePurchaseDetails;
import top.kx.kxss.base.entity.user.BaseEmployee;
import top.kx.kxss.base.manager.BaseSecurityCodeAttributeDetailsManager;
import top.kx.kxss.base.manager.BaseSecurityCodeAttributeManager;
import top.kx.kxss.base.manager.BaseSecurityCodeManager;
import top.kx.kxss.base.manager.dealers.BaseDealersManager;
import top.kx.kxss.base.manager.product.BaseProductManager;
import top.kx.kxss.base.manager.purchase.BasePurchaseDetailsManager;
import top.kx.kxss.base.service.BaseSecurityCodeAttributeService;
import top.kx.kxss.base.service.BaseSecurityCodeService;
import top.kx.kxss.base.service.BaseSecurityCodeVerifyService;
import top.kx.kxss.base.service.attribute.BaseAttributeService;
import top.kx.kxss.base.service.attribute.BaseAttributeValueService;
import top.kx.kxss.base.service.biz.BaseBizLogService;
import top.kx.kxss.base.service.distributor.BaseDistributorService;
import top.kx.kxss.base.service.member.MemberInfoService;
import top.kx.kxss.base.service.product.BaseProductAttributeService;
import top.kx.kxss.base.service.purchase.BasePurchaseDetailsService;
import top.kx.kxss.base.service.purchase.BasePurchaseService;
import top.kx.kxss.base.service.user.BaseEmployeeService;
import top.kx.kxss.base.vo.query.*;
import top.kx.kxss.base.vo.result.*;
import top.kx.kxss.base.vo.result.attribute.BaseAttributeResultVO;
import top.kx.kxss.base.vo.result.member.RegisterConfirmResultVO;
import top.kx.kxss.base.vo.result.product.BaseProductResultVO;
import top.kx.kxss.base.vo.result.product.ProductResultVO;
import top.kx.kxss.base.vo.result.purchase.BasePurchaseDetailsResultVO;
import top.kx.kxss.base.vo.save.*;
import top.kx.kxss.base.vo.save.biz.BizLogSaveVO;
import top.kx.kxss.base.vo.save.purchase.BasePurchaseDetailsSaveVO;
import top.kx.kxss.base.vo.save.purchase.BasePurchaseRemarksVO;
import top.kx.kxss.base.vo.update.BaseSecurityCodeUpdateVO;
import top.kx.kxss.base.vo.update.member.MemberInfoUpdateVO;
import top.kx.kxss.file.entity.File;
import top.kx.kxss.file.enumeration.FileStorageType;
import top.kx.kxss.file.properties.FileServerProperties;
import top.kx.kxss.file.service.FileService;
import top.kx.kxss.file.utils.FileTypeUtil;
import top.kx.kxss.file.vo.param.FileUploadVO;
import top.kx.kxss.model.enumeration.base.SecurityCodeStatusEnum;
import top.kx.kxss.system.entity.tenant.DefUser;
import top.kx.kxss.system.service.system.DefDictService;
import top.kx.kxss.system.service.tenant.DefUserService;
import top.kx.kxss.system.vo.update.tenant.DefUserUpdateVO;

import javax.annotation.Resource;
import java.io.*;
import java.nio.file.Paths;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static top.kx.basic.utils.DateUtils.DEFAULT_DATE_TIME_FORMAT;
import static top.kx.basic.utils.DateUtils.SLASH_DATE_FORMAT;

/**
 * <p>
 * 业务实现类
 * 防伪信息
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-25 13:40:18
 * @create [2023-05-25 13:40:18] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
//@Transactional(readOnly = true)
public class BaseSecurityCodeServiceImpl extends SuperServiceImpl<BaseSecurityCodeManager, Long, BaseSecurityCode, BaseSecurityCodeSaveVO,
        BaseSecurityCodeUpdateVO, BaseSecurityCodePageQuery, BaseSecurityCodeResultVO> implements BaseSecurityCodeService {


    @Resource
    private BaseDealersManager baseDealersManager;
    @Resource
    private BaseProductManager baseProductManager;
    @Resource
    private MemberInfoService memberInfoService;
    @Resource
    private DefUserService defUserService;
    @Resource
    private FileService fileService;
    @Resource
    private EchoService echoService;
    @Resource
    private BaseBizLogService bizLogService;
    @Resource
    private BaseEmployeeService employeeService;
    @Resource
    private BaseDistributorService baseDistributorService;
    @Resource
    private BaseSecurityCodeAttributeManager baseSecurityCodeAttributeManager;
    @Resource
    private DefDictService defDictService;
    @Resource
    private BasePurchaseService basePurchaseService;

    private final FileServerProperties fileProperties;
    private final BasePurchaseDetailsService basePurchaseDetailsService;
    private final BasePurchaseDetailsManager basePurchaseDetailsManager;
    private final BaseAttributeService baseAttributeService;
    private final BaseAttributeValueService baseAttributeValueService;
    private final BaseProductAttributeService baseProductAttributeService;
    private final BaseSecurityCodeAttributeDetailsManager baseSecurityCodeAttributeDetailsManager;
    private final BaseSecurityCodeVerifyService baseSecurityCodeVerifyService;
    private final BaseSecurityCodeAttributeService baseSecurityCodeAttributeService;

    @Autowired
    private DistributedLock distributedLock;

    @Override
    public Boolean check(String code) {
        code = decrypt(code);
        return superManager.count(Wraps.<BaseSecurityCode>lbQ().eq(BaseSecurityCode::getSecurityCode, code)
                .ne(BaseSecurityCode::getStatus, SecurityCodeStatusEnum.IMPORT.getCode())
                .ne(BaseSecurityCode::getStatus, SecurityCodeStatusEnum.DEL.getCode())) > 0;
    }

    @Override
    public String checkCode(String code) {
        code = decrypt(code);
        boolean b = superManager.count(Wraps.<BaseSecurityCode>lbQ().eq(BaseSecurityCode::getSecurityCode, code)
                .ne(BaseSecurityCode::getStatus, SecurityCodeStatusEnum.IMPORT.getCode())
                .ne(BaseSecurityCode::getStatus, SecurityCodeStatusEnum.DEL.getCode())) > 0;
        if (b) {
            return code;
        } else {
            return null;
        }
    }

    @Override
    public Boolean checkSecurityCode(String code, Long id) {
        ArgumentAssert.notEmpty(code, "请填写防伪编码");
        LbQueryWrap<BaseSecurityCode> wrap = Wraps.<BaseSecurityCode>lbQ()
                .eq(BaseSecurityCode::getSecurityCode, code).ne(BaseSecurityCode::getId, id);
        return superManager.count(wrap) > 0;
    }

    @Override
    public Boolean checkBigCode(String code, Long id) {
        ArgumentAssert.notEmpty(code, "请填写防伪编码");
        if ("-".equals(code)) {
            return false;
        }
        LbQueryWrap<BaseSecurityCode> wrap = Wraps.<BaseSecurityCode>lbQ()
                .eq(BaseSecurityCode::getBigCode, code).ne(BaseSecurityCode::getId, id);
        return superManager.count(wrap) > 0;
    }

    @Override
    public Boolean checkCode(String code, Long id) {
        ArgumentAssert.notEmpty(code, "请填写防伪序列号");
        LbQueryWrap<BaseSecurityCode> wrap = Wraps.<BaseSecurityCode>lbQ()
                .eq(BaseSecurityCode::getCode, code).ne(BaseSecurityCode::getId, id);
        return superManager.count(wrap) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseProductResultVO productBySecurityCode(String code, String type) {
        code = decrypt(code);
        BaseSecurityCode securityCode = superManager.getOne(Wraps.<BaseSecurityCode>lbQ().eq(BaseSecurityCode::getSecurityCode, code));
        ArgumentAssert.notNull(securityCode, "您所识别非康溪防伪二维码，请谨慎购买！");
        ArgumentAssert.isFalse(ObjectUtil.equal(securityCode.getStatus(), SecurityCodeStatusEnum.IMPORT.getCode()), "您所识别非康溪防伪二维码，请谨慎购买！");
        ArgumentAssert.isFalse(ObjectUtil.equal(securityCode.getStatus(), SecurityCodeStatusEnum.DEL.getCode()), "您所识别非康溪防伪二维码，请谨慎购买！");
        BaseProduct product = baseProductManager.getOne(Wraps.<BaseProduct>lbQ().eq(BaseProduct::getId, securityCode.getProductId()));
        ArgumentAssert.notNull(product, "商品不存在！");
        //新增绑定日志
        bizLogService.saveLog(BizLogSaveVO.builder()
                .securityCode(securityCode).status(SecurityCodeStatusEnum.SELECT.getCode())
                .type(type).desc("查询序列号").build());
        //记录码的查询次数
        securityCode.setSelectNum(securityCode.getSelectNum() + 1);
        superManager.updateById(securityCode);
        BaseProductResultVO productResultVO = BeanUtil.copyProperties(product, BaseProductResultVO.class);
        //是否注册
        productResultVO.setIsRegister(ObjectUtil.equal(securityCode.getStatus(), SecurityCodeStatusEnum.REGISTER.getCode()));
        productResultVO.setRegisterTime(securityCode.getRegisterTime());

        if (ObjectUtil.isNotNull(productResultVO.getProductImage())) {
            productResultVO.setProductImageFile(fileService.getById(productResultVO.getProductImage()));
        }
        productResultVO.setSecurityCode(securityCode.getSecurityCode());
        productResultVO.setSelectNum(securityCode.getSelectNum());
        // 查询规格属性
//        List<BaseSecurityCodeAttribute> attributeList = baseSecurityCodeAttributeManager.list(Wraps.<BaseSecurityCodeAttribute>lbQ().eq(BaseSecurityCodeAttribute::getSecurityCodeId, securityCode.getId()));
//        if (CollUtil.isNotEmpty(attributeList)) {
//            productResultVO.setAttributeResultVOList(BeanPlusUtil.toBeanList(attributeList, BaseSecurityCodeAttributeResultVO.class));
//        }
        return productResultVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RegisterConfirmResultVO confirmInfo(String code) {
        BaseSecurityCode securityCode = superManager.getOne(Wraps.<BaseSecurityCode>lbQ().eq(BaseSecurityCode::getSecurityCode, code));
        ArgumentAssert.notNull(securityCode, "您所识别非康溪防伪二维码，请谨慎购买！");
        ArgumentAssert.isFalse(ObjectUtil.equal(securityCode.getStatus(), SecurityCodeStatusEnum.IMPORT.getCode())
                || ObjectUtil.equal(securityCode.getStatus(), SecurityCodeStatusEnum.DEL.getCode()), "您所识别非康溪防伪二维码，请谨慎购买！");
        ArgumentAssert.isFalse(ObjectUtil.equal(securityCode.getStatus(), SecurityCodeStatusEnum.REGISTER.getCode()), "已被注册，请勿重复注册");
        BaseProduct product = baseProductManager.getOne(Wraps.<BaseProduct>lbQ().eq(BaseProduct::getId, securityCode.getProductId()));
        ArgumentAssert.notNull(product, "您所识别非康溪防伪二维码，请谨慎购买！");
        MemberInfo memberInfo = memberInfoService.getByUserId(ContextUtil.getUserId());
        RegisterConfirmResultVO build = RegisterConfirmResultVO.builder()
                .productName(product.getName()).securityCode(securityCode.getSecurityCode())
                .mobile(memberInfo.getMobile()).fullName(memberInfo.getFullName())
                .sex(memberInfo.getSex()).birth(memberInfo.getBirth()).regions(StrUtil.isBlank(memberInfo.getRegions()) ? null : JSONUtil.toList(memberInfo.getRegions(), String.class))
                .build();
        echoService.action(build);
        return build;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseSecurityCode registerSecurityCode(RegisterConfirmSaveVO saveVO) {
        BaseSecurityCode securityCode = superManager.getOne(Wraps.<BaseSecurityCode>lbQ().eq(BaseSecurityCode::getSecurityCode, saveVO.getSecurityCode())
                .eq(BaseSecurityCode::getStatus, SecurityCodeStatusEnum.BIND.getCode()));
        ArgumentAssert.notNull(securityCode, "您所识别非康溪防伪二维码，请谨慎购买！");
        ArgumentAssert.isFalse(ObjectUtil.equal(securityCode.getStatus(), SecurityCodeStatusEnum.IMPORT.getCode())
                || ObjectUtil.equal(securityCode.getStatus(), SecurityCodeStatusEnum.DEL.getCode()), "您所识别非康溪防伪二维码，请谨慎购买！");
        ArgumentAssert.isFalse(ObjectUtil.equal(securityCode.getStatus(), SecurityCodeStatusEnum.REGISTER.getCode()), "已被注册，请勿重复注册");
        BaseProduct product = baseProductManager.getOne(Wraps.<BaseProduct>lbQ().eq(BaseProduct::getId, securityCode.getProductId()));
        ArgumentAssert.notNull(product, "您所识别非康溪防伪二维码，请谨慎购买！");
        //新增绑定日志
        bizLogService.saveLog(BizLogSaveVO.builder().securityCode(securityCode).type(saveVO.getType()).desc("查询序列号").build());
        //会员信息
        MemberInfo memberInfo = memberInfoService.getByUserId(ContextUtil.getUserId());
        securityCode.setMemberId(memberInfo.getId());
        securityCode.setRegisterTime(LocalDateTime.now());
        securityCode.setStatus(SecurityCodeStatusEnum.REGISTER.getCode());
        //更新会员信息
        MemberInfoUpdateVO memberInfoUpdateVO = MemberInfoUpdateVO.builder()
                .productNum(memberInfo.getProductNum() + 1)
                .id(memberInfo.getId()).birth(StrUtil.isNotBlank(saveVO.getBirth()) ? saveVO.getBirth() : null)
                .sex(StrUtil.isNotBlank(saveVO.getSex()) ? saveVO.getSex() : null)
                .fullName(StrUtil.isNotBlank(saveVO.getFullName()) ? saveVO.getFullName() : null)
                .regions(saveVO.getRegions()).build();
        memberInfoService.updateById(memberInfoUpdateVO);
        //更新用户性别信息
        DefUser defUser = defUserService.getById(ContextUtil.getUserId());
        DefUserUpdateVO defUserUpdateVO = DefUserUpdateVO.builder()
                .id(defUser.getId())
                .sex(saveVO.getSex())
                .build();
        defUserService.updateById(defUserUpdateVO);
        ArgumentAssert.isFalse(!superManager.updateById(securityCode), "注册失败");
        return securityCode;
    }

    @Override
    public List<ProductResultVO> getProductByMemberId(Long memberId) {
        List<BaseSecurityCode> securityCodeList = superManager.list(Wraps.<BaseSecurityCode>lbQ()
                .eq(BaseSecurityCode::getStatus, SecurityCodeStatusEnum.REGISTER.getCode())
                .eq(BaseSecurityCode::getMemberId, memberId).orderByDesc(BaseSecurityCode::getRegisterTime));
        if (CollUtil.isEmpty(securityCodeList)) {
            return Lists.newArrayList();
        }
        //商品信息
        List<Long> productIds = securityCodeList.stream().map(BaseSecurityCode::getProductId).collect(Collectors.toList());
        Map<Long, BaseProduct> productMap = baseProductManager.list(Wraps.<BaseProduct>lbQ().in(BaseProduct::getId, productIds))
                .stream().collect(Collectors.toMap(BaseProduct::getId, k -> k));
        //经销商信息
        List<Long> dealersId = securityCodeList.stream().map(BaseSecurityCode::getDealersId)
                .filter(ObjectUtil::isNotNull).collect(Collectors.toList());
        Map<Long, BaseDealers> baseDealersMap = CollUtil.isNotEmpty(dealersId) ?
                baseDealersManager.list(Wraps.<BaseDealers>lbQ().in(BaseDealers::getId, dealersId))
                        .stream().collect(Collectors.toMap(BaseDealers::getId, k -> k)) : MapUtil.newHashMap();
        //图片信息
        List<Long> productImages = productMap.values().stream().map(BaseProduct::getProductImage).collect(Collectors.toList());
        Map<Long, File> fileMap = CollectionUtil.isNotEmpty(productImages) ? fileService.list(Wraps.<File>lbQ().in(File::getId, productImages))
                .stream().collect(Collectors.toMap(File::getId, k -> k)) : new HashMap<>();
        return securityCodeList.stream().map(v -> {
            ProductResultVO productResultVO = BeanUtil.copyProperties(v, ProductResultVO.class);
            BaseProduct baseProduct = productMap.get(v.getProductId());
            if (Objects.nonNull(baseProduct) && CollUtil.isNotEmpty(fileMap) && Objects.nonNull(baseProduct.getProductImage())) {
                productResultVO.setProductImageFile(fileMap.get(baseProduct.getProductImage()));
            }
            BeanUtil.copyProperties(baseProduct, productResultVO);
            if (ObjectUtil.isNotNull(v.getDealersId())) {
                BaseDealers dealers = baseDealersMap.get(v.getDealersId());
                productResultVO.setBallRoomName(dealers.getBallRoomName());
                productResultVO.setDealersName(dealers.getName());
                productResultVO.setDealersMobile(dealers.getMobile());
                productResultVO.setDealersType(dealers.getType());
            }
            return productResultVO;
        }).collect(Collectors.toList());
    }

    @Override
    public CodeCountStatisticsVO statistics() {
        //获取本周绑定数据
        LocalDate weekStar = DateUtils.getWeekStar();
        LocalDate weekEnd = DateUtils.getWeekEnd();
        long weekCountNum = superManager.count(Wraps.<BaseSecurityCode>lbQ()
                .in(BaseSecurityCode::getStatus, Arrays.asList(SecurityCodeStatusEnum.BIND.getCode(), SecurityCodeStatusEnum.REGISTER.getCode()))
                .isNotNull(BaseSecurityCode::getBillDate).between(BaseSecurityCode::getBillDate,
                        DateUtils.getStartTime(DateUtils.format(weekStar, DateUtils.DEFAULT_DATE_FORMAT)),
                        DateUtils.getEndTime(DateUtils.format(weekEnd, DateUtils.DEFAULT_DATE_FORMAT))));
        long todayCountNum = superManager.count(Wraps.<BaseSecurityCode>lbQ()
                .in(BaseSecurityCode::getStatus, Arrays.asList(SecurityCodeStatusEnum.BIND.getCode(), SecurityCodeStatusEnum.REGISTER.getCode()))
                .isNotNull(BaseSecurityCode::getBillDate).between(BaseSecurityCode::getBillDate,
                        LocalDate.now().atStartOfDay(),
                        LocalDate.now().atTime(23, 59, 59)));
        long scrapWeekCountNum = superManager.count(Wraps.<BaseSecurityCode>lbQ()
                .eq(BaseSecurityCode::getStatus, SecurityCodeStatusEnum.DEL.getCode())
                .isNotNull(BaseSecurityCode::getScrapTime).between(BaseSecurityCode::getScrapTime,
                        DateUtils.getStartTime(DateUtils.format(weekStar, DateUtils.DEFAULT_DATE_FORMAT)),
                        DateUtils.getEndTime(DateUtils.format(weekEnd, DateUtils.DEFAULT_DATE_FORMAT))));

        long scrapTodayCountNum = superManager.count(Wraps.<BaseSecurityCode>lbQ()
                .eq(BaseSecurityCode::getStatus, SecurityCodeStatusEnum.DEL.getCode())
                .isNotNull(BaseSecurityCode::getScrapTime).between(BaseSecurityCode::getScrapTime,
                        LocalDate.now().atStartOfDay(),
                        LocalDate.now().atTime(23, 59, 59)));
        return CodeCountStatisticsVO.builder()
                .todayBind(todayCountNum)
                .todayScrap(scrapTodayCountNum)
                .weekScrap(scrapWeekCountNum)
                .weekBind(weekCountNum)
                .weekStar(DateUtils.format(weekStar, "M.d"))
                .weekEnd(DateUtils.format(weekEnd, "M.d"))
                .build();
    }


    @Override
    public List<EmpSecurityBindResultVO> bindList(SecurityCodeBindQuery query) {
        LbQueryWrap<BaseSecurityCode> queryWrap = Wraps.<BaseSecurityCode>lbQ();
        queryWrap.isNotNull(BaseSecurityCode::getProductId)
                .in(BaseSecurityCode::getStatus, Arrays.asList(SecurityCodeStatusEnum.BIND.getCode(), SecurityCodeStatusEnum.REGISTER.getCode()))
                .between(BaseSecurityCode::getBillDate, DateUtils.getMonthStar(query.getMonth() + "-01"), DateUtils.getMonthEnd(query.getMonth() + "-01"))
        ;
        if (StrUtil.isNotBlank(query.getKeyword())) {
            List<Long> empIds = employeeService.list(Wraps.<BaseEmployee>lbQ().like(BaseEmployee::getRealName, query.getKeyword()))
                    .stream().map(BaseEmployee::getId).collect(Collectors.toList());
            queryWrap.and(wrap -> wrap.like(BaseSecurityCode::getSecurityCode, query.getKeyword())
                    .or()
                    .inSql(BaseSecurityCode::getProductId,
                            "select id from base_product where delete_flag = 0 and name like '%" + query.getKeyword() + "%'")
                    .or().in(CollUtil.isNotEmpty(empIds), BaseSecurityCode::getBindUser, empIds)
            );
        }
        queryWrap.orderByDesc(BaseSecurityCode::getBillDate);
        List<BaseSecurityCode> list = superManager.list(queryWrap);
        Map<String, List<BaseSecurityCode>> codeMap = list.stream().collect(Collectors.groupingBy(baseSecurityCode -> DateUtils.format(baseSecurityCode.getBillDate(), DateUtils.DEFAULT_DATE_FORMAT_EN)));
        List<String> keyList = codeMap.keySet().stream().sorted(Comparator.comparing(String::valueOf).reversed()).collect(Collectors.toList());
        return keyList.stream().map(billDate -> {
            //获取星期信息
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtils.DEFAULT_DATE_FORMAT_EN);
            LocalDate localDate = LocalDate.parse(billDate, formatter);
            DayOfWeek dayOfWeek = localDate.getDayOfWeek();
            //封装详情信息
            List<EmpSecurityCodeResultVO> bindList = codeMap.get(billDate).stream().map(v -> BeanUtil.copyProperties(v, EmpSecurityCodeResultVO.class)).collect(Collectors.toList());
            bindList.sort(Comparator.comparing(EmpSecurityCodeResultVO::getBillDate).reversed());
            echoService.action(bindList);
            return EmpSecurityBindResultVO.builder()
                    .month(DateUtils.format(localDate, "M月dd日")).week(DateUtils.getWeekStr(dayOfWeek.getValue()))
                    .count(bindList.size()).children(bindList).build();
        }).collect(Collectors.toList());
    }

    @Override
    public IPage<SecurityCodeBindResultVO> bindPageList(PageParams<SecurityCodeBindPageQuery> params) {
        params.setSort("");
        params.setOrder("");
        IPage<BaseSecurityCode> page = params.buildPage(BaseSecurityCode.class);
        SecurityCodeBindPageQuery query = params.getModel();
        BaseSecurityCode model = BeanUtil.toBean(query, BaseSecurityCode.class);
        LbQueryWrap<BaseSecurityCode> wraps = Wraps.lbq(model, params.getExtra(), BaseSecurityCode.class);
        wraps.eq(BaseSecurityCode::getStatus, SecurityCodeStatusEnum.BIND.getCode());
        wraps.eq(BaseSecurityCode::getDeleteFlag, 0);
        wraps.between(BaseSecurityCode::getBillDate,
                DateUtils.format(DateUtils.getStartTime(query.getStartDate()), DEFAULT_DATE_TIME_FORMAT),
                DateUtils.format(DateUtils.getEndTime(query.getEndDate()), DEFAULT_DATE_TIME_FORMAT));
        //关键字查询
        if (StrUtil.isNotBlank(query.getKeyword())) {
            List<Long> empIds = employeeService.list(Wraps.<BaseEmployee>lbQ().like(BaseEmployee::getRealName, query.getKeyword()))
                    .stream().map(BaseEmployee::getId).collect(Collectors.toList());
            wraps.and(wrap -> wrap.like(BaseSecurityCode::getSecurityCode, query.getKeyword())
                    .or()
                    .inSql(BaseSecurityCode::getProductId,
                            "select id from base_product where delete_flag = 0 and name like '%" + query.getKeyword() + "%'")
                    .or().in(CollUtil.isNotEmpty(empIds), BaseSecurityCode::getBindUser, empIds)
            );
        }
        wraps.orderByDesc(BaseSecurityCode::getBillDate);
        page(page, wraps);
        IPage<SecurityCodeBindResultVO> pageVO = BeanPlusUtil.toBeanPage(page, SecurityCodeBindResultVO.class);
        echoService.action(pageVO);
        return pageVO;
    }


    @Override
    public IPage<SecurityCodeScrapResultVO> scrapPageList(PageParams<SecurityCodeScrapQuery> params) {
        params.setSort("");
        params.setOrder("");
        IPage<BaseSecurityCode> page = params.buildPage(BaseSecurityCode.class);
        SecurityCodeScrapQuery query = params.getModel();
        BaseSecurityCode model = BeanUtil.toBean(query, BaseSecurityCode.class);
        LbQueryWrap<BaseSecurityCode> wraps = Wraps.lbq(model, params.getExtra(), BaseSecurityCode.class);
        wraps.eq(BaseSecurityCode::getStatus, SecurityCodeStatusEnum.DEL.getCode());
        wraps.eq(BaseSecurityCode::getDeleteFlag, 0);
        wraps.between(BaseSecurityCode::getScrapTime,
                DateUtils.format(DateUtils.getStartTime(query.getStartDate()), DEFAULT_DATE_TIME_FORMAT),
                DateUtils.format(DateUtils.getEndTime(query.getEndDate()), DEFAULT_DATE_TIME_FORMAT));
        //关键字查询
        if (StrUtil.isNotBlank(query.getKeyword())) {
            List<Long> empIds = employeeService.list(Wraps.<BaseEmployee>lbQ().like(BaseEmployee::getRealName, query.getKeyword()))
                    .stream().map(BaseEmployee::getId).collect(Collectors.toList());
            wraps.and(wrap -> wrap.like(BaseSecurityCode::getSecurityCode, query.getKeyword())
                    .or()
                    .inSql(BaseSecurityCode::getProductId,
                            "select id from base_product where delete_flag = 0 and name like '%" + query.getKeyword() + "%'")
                    .or().in(CollUtil.isNotEmpty(empIds), BaseSecurityCode::getScrapUser, empIds)
            );
        }
        wraps.orderByDesc(BaseSecurityCode::getScrapTime);
        page(page, wraps);
        IPage<SecurityCodeScrapResultVO> pageVO = BeanPlusUtil.toBeanPage(page, SecurityCodeScrapResultVO.class);
        echoService.action(pageVO);
        return pageVO;
    }

    @Deprecated
    @Override
    public IPage<SecurityCodePurchaseControlResultVO> purchaseControlPageList(PageParams<SecurityCodePurchaseControlQuery> params) {
        params.setSort("");
        params.setOrder("");
        IPage<BaseSecurityCode> page = params.buildPage(BaseSecurityCode.class);
        SecurityCodePurchaseControlQuery query = params.getModel();
        BaseSecurityCode model = BeanUtil.toBean(query, BaseSecurityCode.class);
        LbQueryWrap<BaseSecurityCode> wraps = Wraps.lbq(model, params.getExtra(), BaseSecurityCode.class);
        wraps.between(BaseSecurityCode::getPurchaseControlTime,
                DateUtils.format(DateUtils.getStartTime(query.getStartDate()), DEFAULT_DATE_TIME_FORMAT),
                DateUtils.format(DateUtils.getEndTime(query.getEndDate()), DEFAULT_DATE_TIME_FORMAT));
        wraps.eq(BaseSecurityCode::getDeleteFlag, 0);
        //关键字查询
        if (StrUtil.isNotBlank(query.getKeyword())) {
            List<Long> distributorIds = baseDistributorService.list(Wraps.<BaseDistributor>lbQ().like(BaseDistributor::getName, query.getKeyword()))
                    .stream().map(BaseDistributor::getId).collect(Collectors.toList());
            wraps.and(wrap -> wrap.like(BaseSecurityCode::getSecurityCode, query.getKeyword())
                    .or()
                    .inSql(BaseSecurityCode::getProductId,
                            "select id from base_product where delete_flag = 0 and name like '%" + query.getKeyword() + "%'")
                    .or()
                    .in(CollUtil.isNotEmpty(distributorIds), BaseSecurityCode::getDistributorId, distributorIds)
            );
        }
        wraps.isNotNull(BaseSecurityCode::getDistributorId);
        wraps.orderByDesc(BaseSecurityCode::getPurchaseControlTime);
        page(page, wraps);
        IPage<SecurityCodePurchaseControlResultVO> pageVO = BeanPlusUtil.toBeanPage(page, SecurityCodePurchaseControlResultVO.class);
        echoService.action(pageVO);
        return pageVO;
    }

    @Override
    public IPage<SecurityCodePurchaseControlResultVO> purchaseItemPageList(PageParams<SecurityCodePurchaseQuery> params) {
        params.setSort("");
        params.setOrder("");
        SecurityCodePurchaseQuery query = params.getModel();
        LbQueryWrap<BasePurchaseDetails> wraps = Wraps.lbQ();
        wraps.eq(BasePurchaseDetails::getPurchaseId, query.getPurchaseId());
        wraps.eq(BasePurchaseDetails::getDeleteFlag, 0);
        if (StrUtil.isNotBlank(query.getStartDate()) && StrUtil.isNotBlank(query.getEndDate())) {
            wraps.between(BasePurchaseDetails::getCreatedTime, query.getStartDate(), query.getEndDate());
        }
        //关键字查询
        if (StrUtil.isNotBlank(query.getKeyword())) {
            wraps.and(wrap -> wrap.eq(BasePurchaseDetails::getSecurityCode, query.getKeyword())
                    .or()
                    .inSql(BasePurchaseDetails::getProductId,
                            "select id from base_product where delete_flag = 0 and name like '%" + query.getKeyword() + "%'")
            );
        }
        if (StrUtil.isNotBlank(query.getSecurityCode())) {
            wraps.eq(BasePurchaseDetails::getSecurityCode, query.getSecurityCode());
        }
        wraps.orderByAsc(BasePurchaseDetails::getCreatedTime);
        IPage<BasePurchaseDetails> iPage = basePurchaseDetailsService.page(params.buildPage(BasePurchaseDetails.class), wraps);
        IPage<SecurityCodePurchaseControlResultVO> pageVO = BeanPlusUtil.toBeanPage(iPage, SecurityCodePurchaseControlResultVO.class);
       // 查询这条防伪码拿货记录，有没有二级经销商拿货，如果有的话，就将一级经销商的二级经销商用一个字段返回给前端， 如果有多个，就用逗号分隔，

        // 批量查询优化：避免在循环中查询数据库
        if (CollUtil.isNotEmpty(pageVO.getRecords())) {
            // 1. 收集所有经销商ID和防伪码
            Set<Long> distributorIds = pageVO.getRecords().stream()
                    .map(SecurityCodePurchaseControlResultVO::getDistributorId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
            
            Set<String> securityCodes = pageVO.getRecords().stream()
                    .map(SecurityCodePurchaseControlResultVO::getSecurityCode)
                    .filter(StrUtil::isNotBlank)
                    .collect(Collectors.toSet());
            
            // 2. 批量查询所有相关的下级经销商
            Map<Long, List<BaseDistributor>> parentChildMap = new HashMap<>();
            if (CollUtil.isNotEmpty(distributorIds)) {
                List<BaseDistributor> allChildDistributors = baseDistributorService.list(
                    Wraps.<BaseDistributor>lbQ()
                        .in(BaseDistributor::getParentId, distributorIds)
                        .eq(BaseDistributor::getDeleteFlag, 0)
                );
                // 按父经销商ID分组
                parentChildMap = allChildDistributors.stream()
                        .collect(Collectors.groupingBy(BaseDistributor::getParentId));
            }
            
            // 3. 批量查询所有相关防伪码的拿货记录
            Map<String, List<BasePurchaseDetails>> securityCodePurchaseMap = new HashMap<>();
            if (CollUtil.isNotEmpty(securityCodes)) {
                List<BasePurchaseDetails> allPurchaseDetails = basePurchaseDetailsService.list(
                    Wraps.<BasePurchaseDetails>lbQ()
                        .in(BasePurchaseDetails::getSecurityCode, securityCodes)
                        .eq(BasePurchaseDetails::getDeleteFlag, 0)
                );
                // 按防伪码分组
                securityCodePurchaseMap = allPurchaseDetails.stream()
                        .collect(Collectors.groupingBy(BasePurchaseDetails::getSecurityCode));
            }
            
            // 4. 在循环中进行数据组装（只做内存操作，不查询数据库）
            final Map<Long, List<BaseDistributor>> finalParentChildMap = parentChildMap;
            final Map<String, List<BasePurchaseDetails>> finalSecurityCodePurchaseMap = securityCodePurchaseMap;
            
            pageVO.getRecords().forEach(item -> {
                item.setPurchaseRemarks(item.getRemarks());
                item.setPurchaseControlBy(item.getPurchaseEmp());
                item.setPurchaseControlTime(item.getCreatedTime());
                
                // 处理下级经销商拿货信息
                if (Objects.nonNull(item.getDistributorId()) && StrUtil.isNotBlank(item.getSecurityCode())) {
                    List<BaseDistributor> childDistributors = finalParentChildMap.get(item.getDistributorId());
                    List<BasePurchaseDetails> purchaseDetailsList = finalSecurityCodePurchaseMap.get(item.getSecurityCode());
                    
                    if (CollUtil.isNotEmpty(childDistributors) && CollUtil.isNotEmpty(purchaseDetailsList)) {
                        // 获取有此防伪码拿货记录的下级经销商ID集合
                        Set<Long> purchaseDistributorIds = purchaseDetailsList.stream()
                                .map(BasePurchaseDetails::getDistributorId)
                                .collect(Collectors.toSet());
                        
                        // 筛选出有拿货记录的下级经销商并格式化名称
                        List<String> childDistributorNames = childDistributors.stream()
                                .filter(distributor -> purchaseDistributorIds.contains(distributor.getId()))
                                .map(distributor -> {
                                    String name = StrUtil.isNotBlank(distributor.getName()) ? distributor.getName() : "";
                                    String shopName = StrUtil.isNotBlank(distributor.getShopName()) ? distributor.getShopName() : "";
                                    if (StrUtil.isNotBlank(name) && StrUtil.isNotBlank(shopName)) {
                                        return name;
                                    } else if (StrUtil.isNotBlank(name)) {
                                        return name;
                                    } else if (StrUtil.isNotBlank(shopName)) {
                                        return shopName;
                                    } else {
                                        return "未知经销商";
                                    }
                                })
                                .collect(Collectors.toList());
                        
                         // 将下级经销商拿货信息存储到echoMap中（用逗号分隔）
                         if (CollUtil.isNotEmpty(childDistributorNames)) {
                             item.getEchoMap().put("childDistributorName", String.join(",", childDistributorNames));
                         }
                    }
                }
            });
        }
        
        echoService.action(pageVO);
        return pageVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseSecurityCode bindCode(SecurityCodeBindSaveVO model) {
        model.setSecurityCode(decrypt(model.getSecurityCode()));
        BaseProduct product = baseProductManager.getOne(Wraps.<BaseProduct>lbQ().eq(BaseProduct::getId, model.getProductId()));
        ArgumentAssert.notNull(product, "您所绑定商品不存在！");
        BaseSecurityCode securityCode = superManager.getOne(Wraps.<BaseSecurityCode>lbQ().eq(BaseSecurityCode::getSecurityCode, model.getSecurityCode()));
        ArgumentAssert.notNull(securityCode, "标签不存在！");
        ArgumentAssert.isFalse(ObjectUtil.equal(securityCode.getStatus(), SecurityCodeStatusEnum.BIND.getCode()), "标签已绑定，请勿重复操作！");
        ArgumentAssert.isFalse(ObjectUtil.equal(securityCode.getStatus(), SecurityCodeStatusEnum.DEL.getCode()), "标签已报废，无法绑定！");
        ArgumentAssert.isFalse(ObjectUtil.equal(securityCode.getStatus(), SecurityCodeStatusEnum.REGISTER.getCode()), "标签已注册，无法再次绑定！！");
        //新增绑定日志
        bizLogService.saveLog(BizLogSaveVO.builder().securityCode(securityCode).type(model.getType()).desc("绑定序列号").build());
        //绑定信息
        securityCode.setStatus(SecurityCodeStatusEnum.BIND.getCode());
        securityCode.setBillDate(LocalDateTime.now());
        securityCode.setProductId(model.getProductId());
        securityCode.setSupplier(model.getSupplier());
        securityCode.setWareHouse(model.getWareHouse());
        securityCode.setBindUser(ContextUtil.getEmployeeId());
        securityCode.setEnterType(model.getType());
        superManager.updateById(securityCode);
        return securityCode;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseSecurityCode scrap(SecurityCodeScrapSaveVO model) {
        model.setSecurityCode(decrypt(model.getSecurityCode()));
        BaseSecurityCode securityCode = superManager.getOne(Wraps.<BaseSecurityCode>lbQ().eq(BaseSecurityCode::getSecurityCode, model.getSecurityCode()));
        ArgumentAssert.notNull(securityCode, "标签不存在！");
        ArgumentAssert.isFalse(ObjectUtil.equal(securityCode.getStatus(), SecurityCodeStatusEnum.BIND.getCode()), "标签已绑定，无法报废！");
        ArgumentAssert.isFalse(ObjectUtil.equal(securityCode.getStatus(), SecurityCodeStatusEnum.DEL.getCode()), "标签已报废，请勿重复操作！");
        ArgumentAssert.isFalse(ObjectUtil.equal(securityCode.getStatus(), SecurityCodeStatusEnum.REGISTER.getCode()), "标签已注册，无法报废！！");
        //新增报废日志
        bizLogService.saveLog(BizLogSaveVO.builder().securityCode(securityCode).type(model.getType()).desc("报废序列号").build());
        //报废信息
        securityCode.setStatus(SecurityCodeStatusEnum.DEL.getCode());
        securityCode.setScrapTime(LocalDateTime.now());
        securityCode.setScrapUser(ContextUtil.getEmployeeId());
        securityCode.setEnterType(model.getType());
        securityCode.setProductId(model.getProductId());
        securityCode.setWareHouse(model.getWareHouse());
        securityCode.setSupplier(model.getSupplier());
        securityCode.setScrapReason(model.getScrapReason());
        superManager.updateById(securityCode);
        return securityCode;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseSecurityCode purchaseControl(SecurityCodePurchaseControlSaveVO model) {
        model.setSecurityCode(decrypt(model.getSecurityCode()));
        BaseProduct product = baseProductManager.getOne(Wraps.<BaseProduct>lbQ().eq(BaseProduct::getId, model.getProductId()));
        ArgumentAssert.notNull(product, "您所绑定商品不存在！");
        BaseSecurityCode securityCode = superManager.getOne(Wraps.<BaseSecurityCode>lbQ().eq(BaseSecurityCode::getSecurityCode, model.getSecurityCode()));
        ArgumentAssert.notNull(securityCode, "标签不存在！");
        ArgumentAssert.notNull(securityCode.getProductId(), "标签未绑定商品！");
        ArgumentAssert.isFalse(!securityCode.getProductId().equals(model.getProductId()), "请检查当前商品是否和绑定商品一致！");
        // ArgumentAssert.isNull(securityCode.getDistributorId(), "此序列号已绑定经销商！");
        //新增绑定日志
        bizLogService.saveLog(BizLogSaveVO.builder().securityCode(securityCode).type(model.getType()).desc("序列号绑定经销商").build());
        BasePurchaseDetailsSaveVO basePurchaseDetails = BasePurchaseDetailsSaveVO.builder()
                .securityCode(securityCode.getSecurityCode())
                .productId(securityCode.getProductId())
                .productName(product.getName())
                .distributorId(model.getDistributorId())
                .purchaseId(model.getPurchaseId())
                .purchaseEmp(ContextUtil.getEmployeeId())
                .purchaseEnterType(model.getType())
                .remarks(model.getPurchaseRemarks())
                .createdOrgId(ContextUtil.getCurrentCompanyId())
                .build();
        basePurchaseDetailsService.save(basePurchaseDetails);
        basePurchaseService.addNum(model.getPurchaseId(), 1);
        return securityCode;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<BaseSecurityCode> purchaseControlNew(SecurityCodePurchaseControlSaveVO model) {
        boolean lock = false;
        try {
            lock = distributedLock.lock("PURCHASE_BIND:" + model.getPurchaseId(), 0);
            if (!lock) {
                ArgumentAssert.isTrue(false, "操作太频繁，请慢一些！");
            }
            model.setSecurityCode(decrypt(model.getSecurityCode()));
            BaseProduct product = baseProductManager.getOne(Wraps.<BaseProduct>lbQ().eq(BaseProduct::getId, model.getProductId()));
            ArgumentAssert.notNull(product, "您所绑定商品不存在！");
            List<BaseSecurityCode> resultList = superManager.list(Wraps.<BaseSecurityCode>lbQ().eq(BaseSecurityCode::getBigCode, model.getSecurityCode()));
            if (CollUtil.isEmpty(resultList)) {
                BaseSecurityCode baseSecurityCode = superManager.getOne(Wraps.<BaseSecurityCode>lbQ().eq(BaseSecurityCode::getSecurityCode, model.getSecurityCode()));
                resultList.add(baseSecurityCode);
            }
            resultList = resultList.stream().filter(Objects::nonNull).collect(Collectors.toList());
            ArgumentAssert.isTrue(CollUtil.isNotEmpty(resultList), "防伪码不存在！");
            BaseDistributor baseDistributor = baseDistributorService.getById(model.getDistributorId());
            List<BasePurchaseDetails> basePurchaseDetailsList = new ArrayList<>();
            for (BaseSecurityCode securityCode : resultList) {
                ArgumentAssert.notNull(securityCode, "标签不存在！");
                ArgumentAssert.notNull(securityCode.getProductId(), "标签未绑定商品！");
                ArgumentAssert.isFalse(!securityCode.getProductId().equals(model.getProductId()), "请检查当前商品是否和绑定商品一致！");
                ArgumentAssert.isFalse(basePurchaseDetailsService.checkBind(model.getPurchaseId(), securityCode.getSecurityCode(), baseDistributor), "此防伪码已经绑定经销商,请勿重复绑定！");
                BasePurchaseDetails basePurchaseDetails = BasePurchaseDetails.builder()
                        .securityCode(securityCode.getSecurityCode())
                        .productId(securityCode.getProductId())
                        .productName(product.getName())
                        .distributorId(model.getDistributorId())
                        .purchaseId(model.getPurchaseId())
                        .purchaseEmp(ContextUtil.getEmployeeId())
                        .purchaseEnterType(model.getType())
                        .createdOrgId(ContextUtil.getCurrentCompanyId())
                        .build();
                basePurchaseDetails.setCreatedTime(LocalDateTime.now());
                basePurchaseDetailsList.add(basePurchaseDetails);
                // 返回添加要货的时间
                securityCode.setPurchaseControlTime(basePurchaseDetails.getCreatedTime());
            }
            //新增绑定日志
            BizLogSaveVO saveVO = BizLogSaveVO.builder().securityCodeList(resultList).type(model.getType()).desc("序列号绑定经销商").build();
            bizLogService.saveBatchLog(saveVO);
            basePurchaseDetailsService.saveBatch(basePurchaseDetailsList);
            basePurchaseService.addNum(model.getPurchaseId(), basePurchaseDetailsList.size());
            return resultList;
        } finally {
            if (lock) {
                distributedLock.releaseLock("PURCHASE_BIND:" + model.getPurchaseId());
            }
        }
    }

    @Override
    public List<SecurityCodePurchaseControlResultVO> purchaseDetail(String securityCode) {
        securityCode = decrypt(securityCode);
        List<BasePurchaseDetails> basePurchaseDetailsList = basePurchaseDetailsService.list(Wraps.<BasePurchaseDetails>lbQ().eq(BasePurchaseDetails::getSecurityCode, securityCode).orderByAsc(BasePurchaseDetails::getCreatedTime));
        ArgumentAssert.isTrue(CollUtil.isNotEmpty(basePurchaseDetailsList), "拿货明细不存在！");
        List<SecurityCodePurchaseControlResultVO> resultVOList = BeanPlusUtil.toBeanList(basePurchaseDetailsList, SecurityCodePurchaseControlResultVO.class);
        resultVOList.forEach(item -> {
            item.setPurchaseRemarks(item.getRemarks());
            item.setPurchaseControlBy(item.getPurchaseEmp());
            item.setPurchaseControlTime(item.getCreatedTime());
        });

        if (CollUtil.isNotEmpty(basePurchaseDetailsList)) {
            List<Long> collect = basePurchaseDetailsList.stream().map(BasePurchaseDetails::getDistributorId).distinct().collect(Collectors.toList());
            List<BaseDistributor> distributorList = baseDistributorService.listByIds(collect);
            if (CollUtil.isNotEmpty(distributorList)) {
                Map<Long, BaseDistributor> mapDistributor = distributorList.stream().collect(Collectors.toMap(BaseDistributor::getId, Function.identity()));
                resultVOList.forEach(item -> {
                    if (Objects.isNull(item.getDistributorId())) {
                        return;
                    }
                    BaseDistributor distributor = mapDistributor.get(item.getDistributorId());
                    if (Objects.nonNull(distributor)) {
                        item.setRemarks(distributor.getRemarks());
                        item.setMobile(distributor.getMobile());
                    }
                });
            }
        }
        echoService.action(resultVOList);
        return resultVOList;
    }

    @Override
    public BaseSecurityCodeResultVO getOneBySecurityCode(String securityCode) {
        BaseSecurityCode baseSecurityCode = superManager.getOne(Wraps.<BaseSecurityCode>lbQ()
                .eq(BaseSecurityCode::getSecurityCode, securityCode).last("limit 1"));
        if (Objects.isNull(baseSecurityCode)) {
            return null;
        }
        BaseSecurityCodeResultVO resultVO = BeanPlusUtil.toBean(securityCode, BaseSecurityCodeResultVO.class);
        echoService.action(resultVO);
        return resultVO;
    }

    @Override
    public BaseSecurityCodeResultVO getBaseAttributeBySecurityCode(String securityCode) {
        BaseSecurityCode baseSecurityCode = superManager.getOne(Wraps.<BaseSecurityCode>lbQ()
                .eq(BaseSecurityCode::getSecurityCode, securityCode).last("limit 1"));
        if (Objects.isNull(baseSecurityCode)) {
            return null;
        }
        ArgumentAssert.isTrue(ObjectUtil.isNotNull(baseSecurityCode.getProductId()), "标签未绑定商品！");
        BaseSecurityCodeResultVO resultVO = BeanPlusUtil.toBean(baseSecurityCode, BaseSecurityCodeResultVO.class);
        List<BaseProductAttribute> productAttributeList = baseProductAttributeService.list(Wraps.<BaseProductAttribute>lbQ().eq(BaseProductAttribute::getProductId, resultVO.getProductId()));
        if (CollUtil.isNotEmpty(productAttributeList)) {
            List<Long> attributeIds = productAttributeList.stream().map(BaseProductAttribute::getAttributeId).distinct().collect(Collectors.toList());
            List<BaseAttribute> baseAttributeList = baseAttributeService.list(Wraps.<BaseAttribute>lbQ().eq(BaseAttribute::getState, true)
                    .in(SuperEntity::getId, attributeIds).orderByDesc(BaseAttribute::getSortValue));
            if (CollUtil.isNotEmpty(baseAttributeList)) {
                BaseSecurityCodeAttribute baseSecurityCodeAttribute = baseSecurityCodeAttributeManager.getOne(Wraps.<BaseSecurityCodeAttribute>lbQ()
                        .eq(BaseSecurityCodeAttribute::getSecurityCode, baseSecurityCode.getSecurityCode()).eq(BaseSecurityCodeAttribute::getProductId, baseSecurityCode.getProductId())
                        .last("limit 1"));
                List<BaseSecurityCodeAttributeDetails> attributeDetailsList = new ArrayList<>();
                if (Objects.nonNull(baseSecurityCodeAttribute)) {
                    attributeDetailsList = baseSecurityCodeAttributeDetailsManager.list(Wraps.<BaseSecurityCodeAttributeDetails>lbQ().eq(BaseSecurityCodeAttributeDetails::getCodeAttributeId, baseSecurityCodeAttribute.getId()));
                }
                List<BaseAttributeResultVO> attributeResultVOList = BeanPlusUtil.toBeanList(baseAttributeList, BaseAttributeResultVO.class);
                List<BaseAttributeValue> attributeValueList = baseAttributeValueService.list(Wraps.<BaseAttributeValue>lbQ().in(BaseAttributeValue::getAttributeId, attributeIds));
                if (CollUtil.isNotEmpty(attributeValueList)) {
                    Map<Long, List<String>> attributeValueMap = attributeValueList.stream().collect(Collectors.groupingBy(BaseAttributeValue::getAttributeId, Collectors.mapping(BaseAttributeValue::getValue, Collectors.toList())));
                    attributeResultVOList.forEach(item -> {
                        item.setValueList(attributeValueMap.get(item.getId()));
                    });
                }
                if (CollUtil.isNotEmpty(attributeDetailsList)) {
                    Map<Long, String> securityCodeAttributeValueMap = attributeDetailsList.stream().collect(Collectors.toMap(BaseSecurityCodeAttributeDetails::getAttributeId, BaseSecurityCodeAttributeDetails::getAttributeValue, (k1, k2) -> k1));
                    attributeResultVOList.forEach(item -> {
                        item.setValue(securityCodeAttributeValueMap.get(item.getId()));
                    });
                }
                resultVO.setBaseAttributeResultVOList(attributeResultVOList);
            }
        }
        echoService.action(resultVO);
        return resultVO;
    }

    @Override
    public BaseSecurityCodeResultVO getDetail(Long id) {
        BaseSecurityCode securityCode = superManager.getById(id);
        if (ObjectUtil.isNull(securityCode)) {
            return null;
        }
        BaseSecurityCodeResultVO resultVO = BeanPlusUtil.toBean(securityCode, BaseSecurityCodeResultVO.class);
        echoService.action(resultVO);
        return resultVO;
    }

    @Override
    public Boolean checkCodeBindProduct(String securityCode) {
//        securityCode = decrypt(securityCode);
        BaseSecurityCode baseSecurityCode = superManager.getOne(Wraps.<BaseSecurityCode>lbQ()
                .eq(BaseSecurityCode::getSecurityCode, securityCode).last("limit 1"));
        if (Objects.isNull(baseSecurityCode)) {
            return false;
        }
        return Objects.nonNull(baseSecurityCode.getProductId());
    }

    @Override
    public BaseSecurityCodeResultVO securityCodeTrace(String code) {
        BaseSecurityCode securityCode = superManager.getOne(Wraps.<BaseSecurityCode>lbQ().eq(BaseSecurityCode::getSecurityCode, code));
        ArgumentAssert.notNull(securityCode, "防伪码信息不存在，请核验防伪码");
        BaseSecurityCodeResultVO securityCodeResultVO = BeanUtil.copyProperties(securityCode, BaseSecurityCodeResultVO.class);

        List<BaseSecurityCodeVerify> verifyList = baseSecurityCodeVerifyService.list(Wraps.<BaseSecurityCodeVerify>lbQ()
                .eq(BaseSecurityCodeVerify::getSecurityCode, code).orderByAsc(BaseSecurityCodeVerify::getVerifyTime).orderByAsc(SuperEntity::getCreatedTime));
        List<BaseSecurityCodeVerifyResultVO> verifyResultVOList = BeanPlusUtil.toBeanList(verifyList, BaseSecurityCodeVerifyResultVO.class);
        securityCodeResultVO.setVerifyResultVOList(verifyResultVOList);
        // 查询规格属性
        securityCodeResultVO.setAttributeResultVO(baseSecurityCodeAttributeService.getAttributeDetailList(securityCode.getId()));

        echoService.action(securityCodeResultVO);

        // 查询时间
        List<BaseSecurityCodeTimeResultVO> timeResultVOList = new ArrayList<>();
        // 创建时间
        timeResultVOList.add(BaseSecurityCodeTimeResultVO.builder().name("创建时间").value(securityCodeResultVO.getCreatedTime()).userName(CollUtil.isNotEmpty(securityCodeResultVO.getEchoMap()) && Objects.nonNull(securityCodeResultVO.getEchoMap().get("createdBy")) ? securityCodeResultVO.getEchoMap().get("createdBy").toString() : "-").build());
        // 绑定时间
        if (Objects.nonNull(securityCode.getBillDate())) {
            timeResultVOList.add(BaseSecurityCodeTimeResultVO.builder().name("绑定时间").value(securityCodeResultVO.getBillDate()).userName(CollUtil.isNotEmpty(securityCodeResultVO.getEchoMap()) && Objects.nonNull(securityCodeResultVO.getEchoMap().get("bindUser")) ? securityCodeResultVO.getEchoMap().get("bindUser").toString() : "-").build());
        }
        if (ObjectUtil.isNotNull(securityCode.getRegisterTime())) {
            timeResultVOList.add(BaseSecurityCodeTimeResultVO.builder().name("注册时间").value(securityCodeResultVO.getRegisterTime()).userName(CollUtil.isNotEmpty(securityCodeResultVO.getEchoMap()) && Objects.nonNull(securityCodeResultVO.getEchoMap().get("memberId")) ? securityCodeResultVO.getEchoMap().get("memberId").toString() : "-").build());
        }
        // 拿货时间
        List<BasePurchaseDetails> basePurchaseDetailsList = basePurchaseDetailsService.list(Wraps.<BasePurchaseDetails>lbQ().eq(BasePurchaseDetails::getSecurityCode, code));
        if (CollUtil.isNotEmpty(basePurchaseDetailsList)) {
            List<BasePurchaseDetailsResultVO> detailsResultVOList = BeanPlusUtil.toBeanList(basePurchaseDetailsList, BasePurchaseDetailsResultVO.class);
            echoService.action(detailsResultVOList);
            detailsResultVOList.forEach(item -> {
                timeResultVOList.add(BaseSecurityCodeTimeResultVO.builder().name("经销商拿货时间").value(item.getCreatedTime()).userName(CollUtil.isNotEmpty(item.getEchoMap()) && Objects.nonNull(item.getEchoMap().get("distributorId")) ? item.getEchoMap().get("distributorId").toString() : "-").build());
            });
        }
        timeResultVOList.sort(Comparator.comparing(BaseSecurityCodeTimeResultVO::getValue));
        securityCodeResultVO.setTimeResultVOList(timeResultVOList);
        return securityCodeResultVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean del(String code) {
        BaseSecurityCode securityCode = superManager.getOne(Wraps.<BaseSecurityCode>lbQ().eq(BaseSecurityCode::getSecurityCode, code)
                .isNotNull(BaseSecurityCode::getProductId));
        ArgumentAssert.notNull(securityCode, "标签不存在！");
        ArgumentAssert.isFalse(ObjectUtil.isNotNull(securityCode.getMemberId()), "已被注册，请勿删除");
        //新增删除日志
        bizLogService.saveLog(BizLogSaveVO.builder().securityCode(securityCode).desc("删除序列号").build());
        securityCode.setBindUser(null);
        securityCode.setSupplier(null);
        securityCode.setProductId(null);
        securityCode.setEnterType(null);
        securityCode.setWareHouse(null);
        securityCode.setBillDate(null);
        securityCode.setStatus(SecurityCodeStatusEnum.IMPORT.getCode());
        return superManager.updateById(securityCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean delPurchase(String code, Long purchaseId) {
        BaseSecurityCode securityCode = superManager.getOne(Wraps.<BaseSecurityCode>lbQ().eq(BaseSecurityCode::getSecurityCode, code)
                .isNotNull(BaseSecurityCode::getProductId));
        ArgumentAssert.isTrue(Objects.nonNull(securityCode), "防伪信息不存在！");
        List<BasePurchaseDetails> basePurchaseDetailsList = basePurchaseDetailsService.list(Wraps.<BasePurchaseDetails>lbQ()
                .eq(BasePurchaseDetails::getPurchaseId, purchaseId)
                .eq(BasePurchaseDetails::getSecurityCode, code));
        ArgumentAssert.isTrue(CollUtil.isNotEmpty(basePurchaseDetailsList), "拿货信息不存在！");
        //新增删除日志
        bizLogService.saveLog(BizLogSaveVO.builder().securityCode(securityCode).desc("删除经销商绑定序列号").build());
        basePurchaseService.reduceNum(purchaseId, basePurchaseDetailsList.size());
        return basePurchaseDetailsService.removeByIds(basePurchaseDetailsList.stream().map(BasePurchaseDetails::getId).collect(Collectors.toList()));
    }

    @Override
    public Boolean updatePurchaseRemarks(BasePurchaseRemarksVO remarksVO) {
        return basePurchaseDetailsManager.update(Wraps.<BasePurchaseDetails>lbU()
                .set(BasePurchaseDetails::getRemarks, remarksVO.getPurchaseRemarks())
                .eq(BasePurchaseDetails::getSecurityCode, remarksVO.getSecurityCode())
                .eq(BasePurchaseDetails::getPurchaseId, remarksVO.getPurchaseId()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removePurchaseControl(SecurityCodePurchaseControlRemoveVO removeVO) {
        if (CollUtil.isEmpty(removeVO.getSecurityCodeList()) || ObjectUtil.isNull(removeVO.getPurchaseId())) {
            return false;
        }
        List<BasePurchaseDetails> basePurchaseDetailsList = basePurchaseDetailsService.list(Wraps.<BasePurchaseDetails>lbQ().in(BasePurchaseDetails::getSecurityCode, removeVO.getSecurityCodeList())
                .eq(BasePurchaseDetails::getPurchaseId, removeVO.getPurchaseId()));
        if (CollUtil.isEmpty(basePurchaseDetailsList)) {
            return false;
        }
        if (CollUtil.isNotEmpty(basePurchaseDetailsList)) {
            basePurchaseService.reduceNum(removeVO.getPurchaseId(), basePurchaseDetailsList.size());
        }
        return basePurchaseDetailsService.removeByIds(basePurchaseDetailsList.stream().map(BasePurchaseDetails::getId).distinct().collect(Collectors.toList()));
    }

    @Override
    public CodeStatisticsVO statisticsData(SecurityCodeStatisticsQuery query) {
        //防伪码信息
        List<BaseSecurityCode> securityCodes = Lists.newArrayList();
        if (query.getSelectType() == null || query.getSelectType() == 1) {
            //绑定数据
            securityCodes = superManager.list(Wraps.<BaseSecurityCode>lbQ()
                    .between(BaseSecurityCode::getBillDate, DateUtils.getStartTime(query.getStartDate()), DateUtils.getEndTime(query.getEndDate()))
                    .in(BaseSecurityCode::getStatus, Arrays.asList(SecurityCodeStatusEnum.BIND.getCode(), SecurityCodeStatusEnum.REGISTER.getCode()))
                    .isNotNull(BaseSecurityCode::getProductId).isNotNull(BaseSecurityCode::getBindUser)
                    .ne(BaseSecurityCode::getStatus, SecurityCodeStatusEnum.IMPORT.getCode())
                    .ne(BaseSecurityCode::getStatus, SecurityCodeStatusEnum.DEL.getCode()))
            ;
        } else if (query.getSelectType() == 2) {
            //报废数据
            securityCodes = superManager.list(Wraps.<BaseSecurityCode>lbQ()
                    .between(BaseSecurityCode::getScrapTime, DateUtils.getStartTime(query.getStartDate()), DateUtils.getEndTime(query.getEndDate()))
                    .isNotNull(BaseSecurityCode::getScrapUser).eq(BaseSecurityCode::getStatus, SecurityCodeStatusEnum.DEL.getCode()));
        } else if (query.getSelectType() == 3) {
            //注册数据
            securityCodes = superManager.list(Wraps.<BaseSecurityCode>lbQ()
                    .between(BaseSecurityCode::getRegisterTime, DateUtils.getStartTime(query.getStartDate()), DateUtils.getEndTime(query.getEndDate()))
                    .isNotNull(BaseSecurityCode::getMemberId).eq(BaseSecurityCode::getStatus, SecurityCodeStatusEnum.REGISTER.getCode()));
        } else {
            ArgumentAssert.isFalse(true, "查询类型不存在");
        }
        CodeStatisticsVO statisticsVO = new CodeStatisticsVO();
        statisticsVO.setTotalCount(CollUtil.isNotEmpty(securityCodes) ? securityCodes.size() : 0);
        //商品信息
        Map<Long, BaseProduct> productMap = baseProductManager.list(Wraps.<BaseProduct>lbQ().eq(BaseProduct::getState, true))
                .stream().collect(Collectors.toMap(BaseProduct::getId, k -> k));
        Map<Long, List<BaseSecurityCode>> productIdMap = securityCodes.stream().collect(Collectors.groupingBy(BaseSecurityCode::getProductId));
        //封装数据
        if (CollUtil.isNotEmpty(productMap)) {
            statisticsVO.setDataList(productMap.keySet().stream().filter(productIdMap::containsKey).map(v -> {
                List<BaseSecurityCode> securityCodeList = productIdMap.get(v);
                BaseProduct baseProduct = productMap.get(v);
                return StatisticsVO.builder().value(CollUtil.isNotEmpty(securityCodeList) ? securityCodeList.size() : 0)
                        .name(ObjectUtil.isNotNull(baseProduct) ? baseProduct.getName() : "").build();
            }).collect(Collectors.toList()));
        }
        return statisticsVO;
    }

    @Override
    public List<StatisticsVO> operateStatistics(SecurityCodeStatisticsQuery query) {
        //防伪码信息
        List<BaseSecurityCode> securityCodes = Lists.newArrayList();
        if (query.getSelectType() == null || query.getSelectType() == 1) {
            //绑定数据
            securityCodes = superManager.list(Wraps.<BaseSecurityCode>lbQ()
                    .between(BaseSecurityCode::getBillDate, DateUtils.getStartTime(query.getStartDate()), DateUtils.getEndTime(query.getEndDate()))
                    .in(BaseSecurityCode::getStatus, Arrays.asList(SecurityCodeStatusEnum.BIND.getCode(), SecurityCodeStatusEnum.REGISTER.getCode()))
                    .isNotNull(BaseSecurityCode::getProductId).isNotNull(BaseSecurityCode::getBindUser)
                    .ne(BaseSecurityCode::getStatus, SecurityCodeStatusEnum.IMPORT.getCode())
                    .ne(BaseSecurityCode::getStatus, SecurityCodeStatusEnum.DEL.getCode()));
        } else if (query.getSelectType() == 2) {
            //报废数据
            securityCodes = superManager.list(Wraps.<BaseSecurityCode>lbQ()
                    .between(BaseSecurityCode::getScrapTime, DateUtils.getStartTime(query.getStartDate()), DateUtils.getEndTime(query.getEndDate()))
                    .isNotNull(BaseSecurityCode::getScrapUser).eq(BaseSecurityCode::getStatus, SecurityCodeStatusEnum.DEL.getCode()));
        } else if (query.getSelectType() == 3) {
            //注册数据
            securityCodes = superManager.list(Wraps.<BaseSecurityCode>lbQ()
                    .between(BaseSecurityCode::getRegisterTime, DateUtils.getStartTime(query.getStartDate()), DateUtils.getEndTime(query.getEndDate()))
                    .isNotNull(BaseSecurityCode::getMemberId).eq(BaseSecurityCode::getStatus, SecurityCodeStatusEnum.REGISTER.getCode()));
        } else {
            ArgumentAssert.isFalse(true, "查询类型不存在");
        }
        //根据type进行分组
        Map<Long, List<BaseSecurityCode>> bindUserMap = securityCodes.stream()
                .collect(Collectors.groupingBy(ObjectUtil.isNull(query.getSelectType()) || query.getSelectType() == 1 ? BaseSecurityCode::getBindUser : (ObjectUtil.isNotNull(query.getSelectType()) && query.getSelectType() == 2) ? BaseSecurityCode::getScrapUser : BaseSecurityCode::getMemberId));
        if (CollUtil.isNotEmpty(bindUserMap)) {
            Map<Long, String> empUserMap;
            if (ObjectUtil.isNotNull(query.getSelectType()) && query.getSelectType() == 3) {
                empUserMap = memberInfoService.list(Wraps.<MemberInfo>lbQ().in(MemberInfo::getId, bindUserMap.keySet())).stream().collect(Collectors.toMap(MemberInfo::getId, MemberInfo::getName));
            } else {
                empUserMap = employeeService.list(Wraps.<BaseEmployee>lbQ().in(BaseEmployee::getId, bindUserMap.keySet())).stream().collect(Collectors.toMap(BaseEmployee::getId, BaseEmployee::getRealName));
            }
            //进行数据分组
            return bindUserMap.keySet().stream().map(v -> {
                String name = empUserMap.get(v);
                List<BaseSecurityCode> securityCodeList = bindUserMap.get(v);
                return StatisticsVO.builder()
                        .name(StrUtil.isBlank(name) ? "" : name)
                        .value(CollUtil.isNotEmpty(securityCodeList) ? securityCodeList.size() : 0)
                        .build();
            }).collect(Collectors.toList()).stream().sorted(Comparator.comparing(StatisticsVO::getValue).reversed()).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SecurityCodeFileVO importCode(MultipartFile file, String type) {
        StringBuilder stringBuilder = null;
        List<BaseSecurityCode> list = Lists.newArrayList();
        InputStreamReader streamReader = null;
        BufferedReader reader = null;
        try {
            fileService.upload(file, FileUploadVO.builder()
                    .bizType("SECURITY_CODE_IMPORT_ERROR")
                    .build());
            streamReader = new InputStreamReader(file.getInputStream());
            reader = new BufferedReader(streamReader);
            String line;
            stringBuilder = new StringBuilder();
            while ((line = reader.readLine()) != null) {
                if (line == "null" || line.isEmpty() || line == "") {
                    continue;
                }
                String[] split = line.split(",");
                if ("CODE".equals(type)) {
                    if (split.length != 3) {
                        stringBuilder.append(line).append("----数据格式异常\r\n");
                        continue;
                    }
                    list.add(BaseSecurityCode.builder().code(split[0]).securityCode(split[1]).url(split[2])
                            .bigCode("-")
                            .selectNum(0).status(SecurityCodeStatusEnum.IMPORT.getCode()).build());
                }
                if ("BIG_SMALL_CODE".equals(type)) {
                    if (split.length != 5) {
                        stringBuilder.append(line).append("----数据格式异常\r\n");
                        continue;
                    }
                    list.add(BaseSecurityCode.builder().code(split[0]).securityCode(split[1]).url(split[2])
                            .bigCode(split[3])
                            .selectNum(0).status(SecurityCodeStatusEnum.IMPORT.getCode()).build());
                }

            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            IOUtils.close(streamReader);
            IOUtils.close(reader);
        }
        //新增数据
        if (CollUtil.isNotEmpty(list)) {
            List<String> securityCodes = list.stream().map(BaseSecurityCode::getSecurityCode).collect(Collectors.toList());
            Map<String, BaseSecurityCode> securityCodeMap = CollUtil.isNotEmpty(securityCodes) ? superManager.list(Wraps.<BaseSecurityCode>lbQ()
                    .in(BaseSecurityCode::getSecurityCode, securityCodes)).stream().collect(Collectors.toMap(BaseSecurityCode::getSecurityCode, k -> k)) : new HashMap<>();
            for (String securityCode : securityCodeMap.keySet()) {
                BaseSecurityCode saveVO = list.stream().filter(baseSecurityCodeSaveVO -> ObjectUtil.equal(securityCode, baseSecurityCodeSaveVO.getSecurityCode())).findFirst().get();
                stringBuilder.append(saveVO.getCode()).append(",").append(saveVO.getSecurityCode()).append(",").append(saveVO.getUrl()).append("----数据已存在\n");
            }
            list = list.stream().filter(baseSecurityCodeSaveVO -> !securityCodeMap.keySet().contains(baseSecurityCodeSaveVO.getSecurityCode()))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(list)) {
                saveBatch(list);
            }
        }
        String str = String.valueOf(stringBuilder);
        if (str == "null" || StrUtil.isBlank(str)) {
            return SecurityCodeFileVO.builder().errFile(null).flag(true).build();
        }
        //异常信息 生成文件
        log.info("错误数据：{}", str);
        FileServerProperties.Local local = fileProperties.getLocal();
        // 相对路径
        String originalFileName = System.currentTimeMillis() + "_error.txt";
        String path = getPath("SECURITY_CODE_UPLOAD_ERROR", originalFileName);
        // web服务器存放的绝对路径
        String absolutePath = Paths.get(local.getStoragePath(), "", path).toString();
        BufferedWriter writer = null;
        try {
            java.io.File errFile = new java.io.File(absolutePath);
            if (!errFile.exists()) {
                errFile.getParentFile().mkdirs();
                errFile.createNewFile();
                if (errFile.mkdir()) {
                    System.out.println("Directory is created!");
                } else {
                    System.out.println("Failed to create directory!");
                }
            }
            writer = new BufferedWriter(new FileWriter(absolutePath));
            writer.write(str);
            writer.flush();
            writer.close();
            //上传错误文件;
            File file1 = fileService.save(File.builder()
                    .bizType("SECURITY_CODE_UPLOAD_ERROR").path(path).storageType(FileStorageType.LOCAL).url(local.getUrlPrefix() + "" + StrPool.SLASH + path)
                    .originalFileName(originalFileName)
                    .contentType(file.getContentType())
                    .size(file.getSize()).uniqueFileName(originalFileName)
                    .suffix(FilenameUtils.getExtension(originalFileName))
                    .fileType(FileTypeUtil.getFileType(file.getContentType()))
                    .build());
            return SecurityCodeFileVO.builder().errFile(file1).flag(false).build();
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            IOUtils.close(writer);
        }
    }

    /**
     * 企业/年/月/日/业务类型/唯一文件名
     */
    protected String getPath(String bizType, String uniqueFileName) {
        return new StringJoiner(StrPool.SLASH)
                .add(bizType).add(getDateFolder()).add(uniqueFileName).toString();
    }

    /**
     * 获取年月日 2020/09/01
     *
     * @return 日期文件夹
     */
    protected String getDateFolder() {
        return LocalDate.now().format(DateTimeFormatter.ofPattern(SLASH_DATE_FORMAT));
    }


    @Override
    public String decrypt(String code) {
        ArgumentAssert.isFalse(StrUtil.isBlank(code), "密文为空");
        try {
            log.info("防伪码：{}", code);
            if (code.startsWith("kxp")) {
                return code;
            }
            if (code.startsWith("https://") || code.startsWith("http://")) {
                if (code.contains("=")) {
                    code = code.substring(code.lastIndexOf("=") + 1);
                } else {
                    code = code.substring(code.lastIndexOf("?") + 1);
                }
            }
            if (!isNumeric(code)) {
                //解密
                code = DES3Util.des3Decrypt(code);
            }
            log.info("解析后防伪码：{}", code);
        } catch (Exception e) {
        }
        if (StrUtil.isBlank(code)) {
            code = "-9999";
        }
        return code;
    }

    public static boolean isNumeric(String str) {
        return Optional.ofNullable(str)
                .filter(s -> s.matches("\\d+"))
                .isPresent();
    }

    public static String reverseEveryTwoChars(String s) {
        return IntStream.range(0, s.length() / 2)
                .mapToObj(i -> new StringBuilder(s.substring(i * 2, i * 2 + 2)).reverse().toString())
                .collect(Collectors.joining());
    }

//    public static void main(String[] args) {
//        String s = "3E6C442D20C3770EB0CC808FE31CC2315AFAD739".substring(0, 16);
//        System.out.println("反转前16位：" + s);
//        System.out.println("反转后16位：" + reverseEveryTwoChars(s));
//    }

    public static void main(String[] args) {
//        String code = "https://fw.kxss147.com/fw/?60024641967940039437";
        String code = "0126260169903864";

        System.out.println(isNumeric(code));
    }
}


