package top.kx.kxss.base.service;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.manager.impl.SuperCacheManagerImpl;
import top.kx.basic.model.cache.CacheKeyBuilder;
import top.kx.kxss.base.entity.user.BaseEmployee;
import top.kx.kxss.base.mapper.BaseEmployeeTestMapper;
import top.kx.kxss.common.cache.base.user.EmployeeCacheKeyBuilder;


/**
 * 仅测试使用
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2022/9/20 11:31 AM
 * @create [2022/9/20 11:31 AM ] [tangyh] [初始创建]
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)

public class BaseEmployeeTestServiceImpl extends SuperCacheManagerImpl<BaseEmployeeTestMapper, BaseEmployee> implements BaseEmployeeTestService {
    @Override
    protected CacheKeyBuilder cacheKeyBuilder() {
        return new EmployeeCacheKeyBuilder();
    }

    @Override
    public BaseEmployee get(Long id) {
        return baseMapper.get(id);
    }
}
