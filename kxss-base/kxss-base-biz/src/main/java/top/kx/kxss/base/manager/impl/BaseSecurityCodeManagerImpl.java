package top.kx.kxss.base.manager.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.base.entity.BaseSecurityCode;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.base.manager.BaseSecurityCodeManager;
import top.kx.kxss.base.mapper.BaseSecurityCodeMapper;

/**
 * <p>
 * 通用业务实现类
 * 防伪信息
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-25 13:40:18
 * @create [2023-05-25 13:40:18] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BaseSecurityCodeManagerImpl extends SuperManagerImpl<BaseSecurityCodeMapper, BaseSecurityCode> implements BaseSecurityCodeManager {

}


