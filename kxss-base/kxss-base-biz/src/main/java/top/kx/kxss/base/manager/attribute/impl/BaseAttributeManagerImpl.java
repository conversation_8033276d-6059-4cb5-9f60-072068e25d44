package top.kx.kxss.base.manager.attribute.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.manager.impl.SuperCacheManagerImpl;
import top.kx.basic.model.cache.CacheKeyBuilder;
import top.kx.basic.utils.CollHelper;
import top.kx.kxss.base.entity.attribute.BaseAttribute;
import top.kx.kxss.base.manager.attribute.BaseAttributeManager;
import top.kx.kxss.base.mapper.attribute.BaseAttributeMapper;
import top.kx.kxss.common.cache.base.user.AttributeCacheKeyBuilder;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 通用业务实现类
 * 商品基础属性
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-16 18:44:16
 * @create [2025-06-16 18:44:16] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BaseAttributeManagerImpl extends SuperCacheManagerImpl<BaseAttributeMapper, BaseAttribute> implements BaseAttributeManager {

    @Transactional(readOnly = true)
    @Override
    public Map<Serializable, Object> findByIds(Set<Serializable> ids) {
        List<BaseAttribute> list = findByIds(ids, null).stream().filter(Objects::nonNull).collect(Collectors.toList());
        return CollHelper.uniqueIndex(list, BaseAttribute::getId, BaseAttribute::getName);
    }

    @Override
    protected CacheKeyBuilder cacheKeyBuilder() {
        return new AttributeCacheKeyBuilder();
    }

}


