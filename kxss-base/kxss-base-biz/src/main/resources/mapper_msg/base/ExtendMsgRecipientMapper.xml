<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.msg.mapper.ExtendMsgRecipientMapper">
    <!--
        代码生成器 by 2022-07-10 11:41:17
        自定义sql建议在base文件夹同级新建ext文件夹，并新建同名且同namespace的xml进行编写。方便修改字段时，重新生成此文件。
    -->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.msg.entity.ExtendMsgRecipient">
        <id column="id" property="id"/>
        <result column="msg_id" property="msgId"/>
        <result column="recipient" property="recipient"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="updated_time" property="updatedTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , msg_id, recipient, created_by, created_time, updated_by,
        updated_time
    </sql>

</mapper>
