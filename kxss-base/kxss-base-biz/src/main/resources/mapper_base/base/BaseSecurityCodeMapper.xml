<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.base.mapper.BaseSecurityCodeMapper">
<!--
    代码生成器 by 2023-05-25 13:40:18
    自定义sql建议在base文件夹同级新建ext文件夹，并新建同名且同namespace的xml进行编写。方便修改字段时，重新生成此文件。
-->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.base.entity.BaseSecurityCode">
        <id column="id" property="id" />
        <result column="code" property="code" />
        <result column="security_code" property="securityCode" />
        <result column="url" property="url" />
        <result column="bill_date" property="billDate" />
        <result column="product_id" property="productId" />
        <result column="ware_house" property="wareHouse" />
        <result column="supplier" property="supplier" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_time" property="updatedTime" />
        <result column="created_org_id" property="createdOrgId" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="member_id" property="memberId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, code, security_code, url, bill_date, product_id, 
        ware_house, supplier, created_by, created_time, updated_by, updated_time, 
        created_org_id, delete_flag, member_id
    </sql>

</mapper>
