<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.base.mapper.security.BaseSecurityCodeBatchDetailsMapper">
<!--
    代码生成器 by 2025-04-25 17:45:12
    自定义sql建议在base文件夹同级新建ext文件夹，并新建同名且同namespace的xml进行编写。方便修改字段时，重新生成此文件。
-->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.base.entity.security.BaseSecurityCodeBatchDetails">
        <id column="id" property="id" />
        <result column="batch_id" property="batchId" />
        <result column="security_code" property="securityCode" />
        <result column="url" property="url" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_time" property="updatedTime" />
        <result column="created_org_id" property="createdOrgId" />
        <result column="delete_flag" property="deleteFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, batch_id, security_code, url, big_code, created_by, 
        created_time, updated_by, updated_time, created_org_id, delete_flag
    </sql>

    <select id="getRepeatCode" resultType="java.lang.String">
        SELECT bscbd.security_code    AS securityCode
        FROM base_security_code_batch_details bscbd
                 INNER JOIN base_security_code bsc ON bscbd.security_code = bsc.security_code
        where bscbd.batch_id = #{batchId} and bscbd.delete_flag = 0 and bsc.delete_flag = 0
         limit 1000
    </select>

    <select id="getBigRepeatCode" resultType="java.lang.String">
        SELECT bscbd.big_security_code    AS securityCode
        FROM base_security_code_batch_details bscbd
                 INNER JOIN base_security_code bsc ON bscbd.big_security_code = bsc.security_code
        where bscbd.batch_id = #{batchId} and bscbd.delete_flag = 0 and bsc.delete_flag = 0 and bscbd.big_security_code != '-'
        limit 1000
    </select>
    <select id="getAllBigCode" resultType="top.kx.kxss.base.vo.result.security.BaseSecurityCodeBatchDetailsResultVO">
        SELECT distinct big_security_code AS bigSecurityCode,
                        big_url           AS bigUrl,
                        big_link_type     AS bigLinkType
        FROM base_security_code_batch_details bscbd
        where bscbd.batch_id = #{batchId} and bscbd.delete_flag = 0 and bscbd.big_security_code != '-'
    </select>

</mapper>
