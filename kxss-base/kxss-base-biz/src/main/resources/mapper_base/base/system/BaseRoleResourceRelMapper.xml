<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.base.mapper.system.BaseRoleResourceRelMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.base.entity.system.BaseRoleResourceRel">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="created_by" jdbcType="BIGINT" property="createdBy"/>
        <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime"/>
        <result column="updated_by" jdbcType="BIGINT" property="updatedBy"/>
        <result column="resource_id" jdbcType="BIGINT" property="resourceId"/>
        <result column="application_id" jdbcType="BIGINT" property="applicationId"/>
        <result column="role_id" jdbcType="BIGINT" property="roleId"/>
        <result column="created_org_id" jdbcType="BIGINT" property="createdOrgId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , created_time, created_by, updated_time, updated_by,
        resource_id, application_id, role_id, created_org_id
    </sql>

</mapper>
