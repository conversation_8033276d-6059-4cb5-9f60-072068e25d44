<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.base.mapper.print.BasePrintTemplateMapper">
<!--
    代码生成器 by 2025-08-27 10:48:00
    自定义sql建议在base文件夹同级新建ext文件夹，并新建同名且同namespace的xml进行编写。方便修改字段时，重新生成此文件。
-->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.base.entity.print.BasePrintTemplate">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="product_id" property="productId" />
        <result column="label_size_width" property="labelSizeWidth" />
        <result column="label_size_height" property="labelSizeHeight" />
        <result column="used_attr_ids" property="usedAttrIds" />
        <result column="print_content" property="printContent" />
        <result column="remarks" property="remarks" />
        <result column="created_time" property="createdTime" />
        <result column="created_by" property="createdBy" />
        <result column="updated_time" property="updatedTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="created_org_id" property="createdOrgId" />
        <result column="delete_flag" property="deleteFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name,product_id, label_size_width, label_size_height, used_attr_ids, print_content,
        remarks, created_time, created_by, updated_time, updated_by, created_org_id,
        delete_flag
    </sql>

</mapper>
