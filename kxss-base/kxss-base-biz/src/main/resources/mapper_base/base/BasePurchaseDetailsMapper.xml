<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.base.mapper.purchase.BasePurchaseDetailsMapper">
<!--
    代码生成器 by 2025-04-10 14:47:18
    自定义sql建议在base文件夹同级新建ext文件夹，并新建同名且同namespace的xml进行编写。方便修改字段时，重新生成此文件。
-->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.base.entity.purchase.BasePurchaseDetails">
        <id column="id" property="id" />
        <result column="security_code" property="securityCode" />
        <result column="product_id" property="productId" />
        <result column="product_name" property="productName" />
        <result column="distributor_id" property="distributorId" />
        <result column="purchase_id" property="purchaseId" />
        <result column="purchase_emp" property="purchaseEmp" />
        <result column="purchase_enter_type" property="purchaseEnterType" />
        <result column="remarks" property="remarks" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_time" property="updatedTime" />
        <result column="created_org_id" property="createdOrgId" />
        <result column="delete_flag" property="deleteFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, security_code, product_id, product_name, distributor_id, purchase_id, 
        purchase_emp, purchase_enter_type, remarks, created_by, created_time, updated_by, 
        updated_time, created_org_id, delete_flag
    </sql>

</mapper>
