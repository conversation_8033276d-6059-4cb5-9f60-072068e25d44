<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.base.mapper.download.BaseDownloadRecordsMapper">
<!--
    代码生成器 by 2025-04-25 17:45:12
    自定义sql建议在base文件夹同级新建ext文件夹，并新建同名且同namespace的xml进行编写。方便修改字段时，重新生成此文件。
-->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.base.entity.download.BaseDownloadRecords">
        <id column="id" property="id" />
        <result column="source_id" property="sourceId" />
        <result column="source_type" property="sourceType" />
        <result column="employee_id" property="employeeId" />
        <result column="url" property="url" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_time" property="updatedTime" />
        <result column="created_org_id" property="createdOrgId" />
        <result column="delete_flag" property="deleteFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, source_id, source_type, employee_id, url, created_by, 
        created_time, updated_by, updated_time, created_org_id, delete_flag
    </sql>

</mapper>
