<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.base.mapper.lottery.LotteryPrizeClaimMapper">
<!--
    代码生成器 by 2025-09-15 17:23:11
    自定义sql建议在base文件夹同级新建ext文件夹，并新建同名且同namespace的xml进行编写。方便修改字段时，重新生成此文件。
-->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.base.entity.lottery.LotteryPrizeClaim">
        <id column="id" property="id" />
        <result column="record_id" property="recordId" />
        <result column="activity_id" property="activityId" />
        <result column="user_id" property="userId" />
        <result column="receiver_name" property="receiverName" />
        <result column="phone" property="phone" />
        <result column="province" property="province" />
        <result column="city" property="city" />
        <result column="district" property="district" />
        <result column="detail_address" property="detailAddress" />
        <result column="claim_status" property="claimStatus" />
        <result column="express_company" property="expressCompany" />
        <result column="express_no" property="expressNo" />
        <result column="claim_info" property="claimInfo" />
        <result column="created_time" property="createdTime" />
        <result column="created_by" property="createdBy" />
        <result column="updated_time" property="updatedTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="remarks" property="remarks" />
        <result column="delete_flag" property="deleteFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, record_id, activity_id, user_id, receiver_name, phone, province, city, district, detail_address, claim_status,
        express_company, express_no, claim_info, created_time, created_by, updated_time,
        updated_by, remarks, delete_flag
    </sql>

</mapper>
