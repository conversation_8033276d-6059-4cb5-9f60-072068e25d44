<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.base.mapper.security.BaseSecurityCodeBatchMapper">
<!--
    代码生成器 by 2025-04-25 17:45:12
    自定义sql建议在base文件夹同级新建ext文件夹，并新建同名且同namespace的xml进行编写。方便修改字段时，重新生成此文件。
-->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.base.entity.security.BaseSecurityCodeBatch">
        <id column="id" property="id" />
        <result column="batch_code" property="batchCode" />
        <result column="num" property="num" />
        <result column="product_id" property="productId" />
        <result column="link_type" property="linkType" />
        <result column="link_url" property="linkUrl" />
        <result column="is_series" property="isSeries" />
        <result column="series_ratio" property="seriesRatio" />
        <result column="state" property="state" />
        <result column="import_state" property="importState" />
        <result column="enterprise_id" property="enterpriseId" />
        <result column="logistics_type" property="logisticsType" />
        <result column="logistics_date" property="logisticsDate" />
        <result column="created_emp" property="createdEmp" />
        <result column="remarks" property="remarks" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_time" property="updatedTime" />
        <result column="created_org_id" property="createdOrgId" />
        <result column="delete_flag" property="deleteFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, batch_code, num, product_id, link_type, link_url, 
        is_series, series_ratio, state, import_state, enterprise_id, logistics_type, 
        logistics_date, created_emp, remarks, created_by, created_time, updated_by, 
        updated_time, created_org_id, delete_flag
    </sql>

</mapper>
