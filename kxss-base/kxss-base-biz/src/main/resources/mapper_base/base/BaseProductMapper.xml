<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.base.mapper.product.BaseProductMapper">
<!--
    代码生成器 by 2023-05-25 13:52:40
    自定义sql建议在base文件夹同级新建ext文件夹，并新建同名且同namespace的xml进行编写。方便修改字段时，重新生成此文件。
-->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.base.entity.product.BaseProduct">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="category" property="category" />
        <result column="state" property="state" />
        <result column="code" property="code" />
        <result column="measuring_unit" property="measuringUnit" />
        <result column="spec" property="spec" />
        <result column="detail" property="detail" />
        <result column="product_image" property="productImage" />
        <result column="created_time" property="createdTime" />
        <result column="created_by" property="createdBy" />
        <result column="updated_time" property="updatedTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="created_org_id" property="createdOrgId" />
        <result column="remarks" property="remarks" />
        <result column="delete_flag" property="deleteFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, category, state, code, measuring_unit, 
        spec, detail, product_image, created_time, created_by, updated_time, 
        updated_by, created_org_id, remarks, delete_flag
    </sql>

</mapper>
