<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.base.mapper.distributor.BaseDistributorMapper">
<!--
    代码生成器 by 2025-03-07 15:45:07
    自定义sql建议在base文件夹同级新建ext文件夹，并新建同名且同namespace的xml进行编写。方便修改字段时，重新生成此文件。
-->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.base.entity.distributor.BaseDistributor">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="mobile" property="mobile" />
        <result column="code_" property="code" />
        <result column="shop_name" property="shopName" />
        <result column="address" property="address" />
        <result column="regions" property="regions" />
        <result column="remarks" property="remarks" />
        <result column="created_org_id" property="createdOrgId" />
        <result column="created_time" property="createdTime" />
        <result column="created_by" property="createdBy" />
        <result column="updated_time" property="updatedTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="delete_flag" property="deleteFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, mobile, code_, shop_name, address, 
        regions, remarks, created_org_id, created_time, created_by, updated_time, 
        updated_by, delete_flag
    </sql>

</mapper>
