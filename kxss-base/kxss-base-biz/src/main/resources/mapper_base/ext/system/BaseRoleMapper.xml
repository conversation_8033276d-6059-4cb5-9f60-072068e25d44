<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.base.mapper.system.BaseRoleMapper">

    <select id="listEmployeeIdByRoleId" parameterType="map" resultType="java.lang.Long">
        SELECT DISTINCT employee_id
        FROM base_employee_role_rel where role_id in
        <foreach collection="roleIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>


    <select id="selectRoleIdByOrgId" parameterType="map" resultType="java.lang.Long">
        SELECT DISTINCT r.id
        FROM base_role r INNER JOIN base_org_role_rel err on r.id = err.role_id
        <where>
            err.org_id = #{orgId} and r.state = 1 and err.delete_flag = 0
        </where>
    </select>

    <select id="selectRoleByEmployeeId" parameterType="map" resultType="java.lang.Long">
        SELECT DISTINCT r.id
        FROM base_role r INNER JOIN base_employee_role_rel err on r.id = err.role_id
        <where>
            err.employee_id = #{employeeId} and r.state = 1 and err.delete_flag = 0
        </where>
    </select>
</mapper>
