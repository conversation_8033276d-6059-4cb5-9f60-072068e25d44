package top.kx.kxss.base.controller.security;

import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.base.controller.SuperController;
import top.kx.kxss.base.service.BaseSecurityCodeAttributeDetailsService;
import top.kx.kxss.base.entity.BaseSecurityCodeAttributeDetails;
import top.kx.kxss.base.vo.save.BaseSecurityCodeAttributeDetailsSaveVO;
import top.kx.kxss.base.vo.update.BaseSecurityCodeAttributeDetailsUpdateVO;
import top.kx.kxss.base.vo.result.BaseSecurityCodeAttributeDetailsResultVO;
import top.kx.kxss.base.vo.query.BaseSecurityCodeAttributeDetailsPageQuery;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 前端控制器
 * 防伪信息规格属性
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-17 10:23:59
 * @create [2025-06-17 10:23:59] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/baseSecurityCodeAttributeDetails")
@Api(value = "BaseSecurityCodeAttributeDetails", tags = "防伪信息规格属性")
public class BaseSecurityCodeAttributeDetailsController extends SuperController<BaseSecurityCodeAttributeDetailsService, Long, BaseSecurityCodeAttributeDetails, BaseSecurityCodeAttributeDetailsSaveVO,
    BaseSecurityCodeAttributeDetailsUpdateVO, BaseSecurityCodeAttributeDetailsPageQuery, BaseSecurityCodeAttributeDetailsResultVO> {
    private final EchoService echoService;
    @Override
    public EchoService getEchoService() {
        return echoService;
    }

}


