package top.kx.kxss.base.controller.user;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.kxss.base.service.user.BaseEmployeeService;
import top.kx.kxss.base.vo.update.user.EmployeeUpdateVO;

import javax.annotation.Resource;


/**
 * <p>
 * 前端控制器
 * 员工
 * </p>
 *
 * <AUTHOR>
 * @date 2021-10-18
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/employee")
@Api(value = "Employee", tags = "员工信息-小程序员工端")
public class EmployeeController {

    @Resource
    private BaseEmployeeService baseEmployeeService;


    @ApiOperation(value = "更新员工信息-员工端", notes = "更新员工信息-员工端")
    @PutMapping(value = "/updateEmployee")
    public R<Boolean> updateEmployee(@RequestBody @Validated EmployeeUpdateVO updateVO) {
        return R.success(baseEmployeeService.updateEmployee(updateVO));
    }

}
