package top.kx.kxss.base.controller.dealers;

import cn.hutool.core.util.DesensitizedUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.base.entity.dealers.BaseDealers;
import top.kx.kxss.base.service.dealers.BaseDealersService;
import top.kx.kxss.base.vo.query.dealers.BaseDealersPageQuery;
import top.kx.kxss.base.vo.result.dealers.BaseDealersResultVO;
import top.kx.kxss.base.vo.save.dealers.BaseDealersSaveVO;
import top.kx.kxss.base.vo.update.dealers.BaseDealersUpdateVO;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 前端控制器
 * 经销商信息
 * </p>
 *
 * <AUTHOR>
 * @date 2024-12-20 10:47:35
 * @create [2024-12-20 10:47:35] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/baseDealers")
@Api(value = "BaseDealers", tags = "俱乐部信息")
public class BaseDealersController extends SuperController<BaseDealersService, Long, BaseDealers, BaseDealersSaveVO,
    BaseDealersUpdateVO, BaseDealersPageQuery, BaseDealersResultVO> {
    private final EchoService echoService;
    @Override
    public EchoService getEchoService() {
        return echoService;
    }


    @Override
    public R<List<BaseDealersResultVO>> query(BaseDealersPageQuery data) {
        R<List<BaseDealersResultVO>> listR = super.query(data);
        for (BaseDealersResultVO baseDealersResultVO : listR.getData()) {
            baseDealersResultVO.setMobile(DesensitizedUtil.mobilePhone(baseDealersResultVO.getMobile()));
        }
        return listR;
    }

    @Override
    public void handlerResult(IPage<BaseDealersResultVO> page) {
        for (BaseDealersResultVO baseDealersResultVO : page.getRecords()) {
            baseDealersResultVO.setMobile(DesensitizedUtil.mobilePhone(baseDealersResultVO.getMobile()));
        }
        super.handlerResult(page);
    }

    @Override
    public R<BaseDealersResultVO> getDetail(Long aLong) {
        BaseDealers baseDealers = superService.getById(aLong);
        if (Objects.isNull(baseDealers)) {
            return R.success(null);
        }
        BaseDealersResultVO resultVO = BeanPlusUtil.toBean(baseDealers, BaseDealersResultVO.class);
        resultVO.setMobile(DesensitizedUtil.mobilePhone(resultVO.getMobile()));
        echoService.action(resultVO);
        return R.success(resultVO);
    }
}


