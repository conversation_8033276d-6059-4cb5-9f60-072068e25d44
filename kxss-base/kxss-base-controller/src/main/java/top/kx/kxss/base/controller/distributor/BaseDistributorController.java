package top.kx.kxss.base.controller.distributor;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestMethod;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.base.entity.SuperEntity;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.database.mybatis.conditions.query.QueryWrap;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.base.entity.distributor.BaseDistributor;
import top.kx.kxss.base.service.distributor.BaseDistributorService;
import top.kx.kxss.base.service.purchase.BasePurchaseService;
import top.kx.kxss.base.vo.query.distributor.BaseDistributorPageQuery;
import top.kx.kxss.base.vo.result.distributor.BaseDistributorPurchaseResultVO;
import top.kx.kxss.base.vo.result.distributor.BaseDistributorResultVO;
import top.kx.kxss.base.vo.result.distributor.BaseDistributorTreeResultVO;
import top.kx.kxss.base.vo.save.distributor.BaseDistributorSaveVO;
import top.kx.kxss.base.vo.update.distributor.BaseDistributorUpdateVO;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import org.apache.poi.ss.usermodel.Workbook;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 前端控制器
 * 耗材经销商信息
 * </p>
 *
 * <AUTHOR>
 * @date 2025-03-07 15:45:07
 * @create [2025-03-07 15:45:07] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/baseDistributor")
@Api(value = "BaseDistributor", tags = "耗材经销商信息")
public class BaseDistributorController extends SuperController<BaseDistributorService, Long, BaseDistributor, BaseDistributorSaveVO,
    BaseDistributorUpdateVO, BaseDistributorPageQuery, BaseDistributorResultVO> {
    private final EchoService echoService;
    @Override
    public EchoService getEchoService() {
        return echoService;
    }

    private final BasePurchaseService basePurchaseService;


    @Override
    public QueryWrap<BaseDistributor> handlerWrapper(BaseDistributor model, PageParams<BaseDistributorPageQuery> params) {
        params.setOrder("");
        params.setSort("");
        QueryWrap<BaseDistributor> wrapper = super.handlerWrapper(model, params);
        BaseDistributorPageQuery query = params.getModel();
        if (Objects.nonNull(query.getIsDistributor()) && query.getIsDistributor()) {
            wrapper.lambda().eq(BaseDistributor::getCreatedBy, ContextUtil.getUserId());
        } else {
            if (Objects.nonNull(query.getParentId())) {
                wrapper.lambda().eq(BaseDistributor::getParentId, query.getParentId());
            } else {
                wrapper.lambda().eq(BaseDistributor::getParentId, 0L);
            }
        }
        if (StringUtils.isNotBlank(query.getKeyword())) {
            wrapper.lambda().and(e ->
                    e.like(BaseDistributor::getCode, query.getKeyword())
                    .or()
                    .like(BaseDistributor::getShopName, query.getKeyword())
                    .or()
                    .like(BaseDistributor::getMobile, query.getKeyword())
                    .or()
                    .like(BaseDistributor::getName, query.getKeyword()));
        }
        wrapper.lambda().orderByDesc(SuperEntity::getCreatedTime);
        return wrapper;
    }


    @Override
    public void handlerQueryParams(PageParams<BaseDistributorPageQuery> params) {
        params.setOrder("");
        params.setSort("");
        super.handlerQueryParams(params);
    }

    @Override
    public R<List<BaseDistributorResultVO>> query(BaseDistributorPageQuery data) {
        LbQueryWrap<BaseDistributor> wrap = Wraps.<BaseDistributor>lbQ()
                .eq(SuperEntity::getDeleteFlag, 0)
                .like(StringUtils.isNotBlank(data.getName()), BaseDistributor::getName, data.getName())
                .like(StringUtils.isNotBlank(data.getMobile()), BaseDistributor::getMobile, data.getMobile())
                .like(StringUtils.isNotBlank(data.getCode()), BaseDistributor::getCode, data.getCode())
                .like(StringUtils.isNotBlank(data.getShopName()), BaseDistributor::getShopName, data.getShopName());
        if (Objects.nonNull(data.getIsDistributor()) && data.getIsDistributor()) {
            wrap.eq(BaseDistributor::getCreatedBy, ContextUtil.getUserId());
        } else {
            wrap.eq(BaseDistributor::getParentId, 0L);
        }
        if (StringUtils.isNotBlank(data.getKeyword())) {
            wrap.and(e -> e.like(BaseDistributor::getCode, data.getKeyword())
                    .or()
                    .like(BaseDistributor::getShopName, data.getKeyword())
                    .or()
                    .like(BaseDistributor::getName, data.getKeyword())
                    .or()
                    .like(BaseDistributor::getMobile, data.getKeyword())
            );
        }
        List<BaseDistributor> distributorList = superService.list(wrap);
        return R.success(BeanPlusUtil.toBeanList(distributorList, BaseDistributorResultVO.class));
    }


    @Override
    public void handlerResult(IPage<BaseDistributorResultVO> page) {
        if (CollUtil.isNotEmpty(page.getRecords())) {
            List<Long> distributorIds = page.getRecords().stream().map(BaseDistributorResultVO::getId).distinct().collect(Collectors.toList());
            List<BaseDistributor> childDistributorList = superService.list(Wraps.<BaseDistributor>lbQ().in(BaseDistributor::getParentId, distributorIds));
            List<Long> childDistributeIds = childDistributorList.stream().map(SuperEntity::getId).distinct().collect(Collectors.toList());
            List<Long> allDistributorIds = Lists.newArrayList(distributorIds);
            allDistributorIds.addAll(childDistributeIds);
            List<BaseDistributorPurchaseResultVO> purchaseSumList = basePurchaseService.purchaseSumList(allDistributorIds);
            Map<Long, BaseDistributorPurchaseResultVO> purchaseSumMap = purchaseSumList.stream().collect(Collectors.toMap(BaseDistributorPurchaseResultVO::getDistributorId, Function.identity()));
            
            // 建立父子关系映射：父经销商ID -> 子经销商ID列表
            Map<Long, List<Long>> parentChildMap = childDistributorList.stream()
                    .collect(Collectors.groupingBy(BaseDistributor::getParentId, 
                            Collectors.mapping(SuperEntity::getId, Collectors.toList())));
            
            for (BaseDistributorResultVO record : page.getRecords()) {
                // 设置当前经销商的拿货数据
                if (purchaseSumMap.containsKey(record.getId())) {
                    BaseDistributorPurchaseResultVO purchaseResultVO = purchaseSumMap.get(record.getId());
                    record.setPurchaseNum(Objects.nonNull(purchaseResultVO.getPurchaseNum()) ? purchaseResultVO.getPurchaseNum() : 0);
                    record.setPurchaseCount(Objects.nonNull(purchaseResultVO.getPurchaseCount()) ? purchaseResultVO.getPurchaseCount() : 0L);
                    record.setPurchaseTime(purchaseResultVO.getPurchaseTime());
                } else {
                    record.setPurchaseNum(0);
                    record.setPurchaseCount(0L);
                    record.setPurchaseTime(null);
                }
                
                // 计算下级经销商拿货数量
                int childPurchaseNum = 0;
                List<Long> childIds = parentChildMap.get(record.getId());
                if (CollUtil.isNotEmpty(childIds)) {
                    for (Long childId : childIds) {
                        BaseDistributorPurchaseResultVO childPurchase = purchaseSumMap.get(childId);
                        if (Objects.nonNull(childPurchase) && Objects.nonNull(childPurchase.getPurchaseNum())) {
                            childPurchaseNum += childPurchase.getPurchaseNum();
                        }
                    }
                }
                record.setChildPurchaseNum(childPurchaseNum);
            }
        }
        super.handlerResult(page);
    }


    @ApiOperation(value = "按树结构查询地区")
    @PostMapping("/tree")
    @WebLog("级联查询地区")
    public R<List<BaseDistributorTreeResultVO>> tree(@RequestBody BaseDistributorPageQuery pageQuery) {
        return success(superService.tree(pageQuery));
    }


    @ApiOperation(value = "经销商删除，有拿货单的不能删除")
    @DeleteMapping("/remove")
    @WebLog("经销商删除")
    public R<Boolean> remove(@RequestParam Long id) {
        return success(superService.remove(id));
    }

    @ApiOperation("刷新经销商userId")
    @GetMapping("/refreshUserId")
    public R<Boolean> refreshUserId() {
        return R.success(superService.refreshUserId());
    }

        @ApiOperation("刷新拿货数据")
    @GetMapping("/refreshPurchase")
    public R<Boolean> refreshPurchase() {
        return R.success(basePurchaseService.refreshPurchase());
    }

    @ApiOperation(value = "经销商信息-导出", notes = "经销商信息-导出")
    @RequestMapping(value = "/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void distributorExport(@RequestBody @Validated BaseDistributorPageQuery query, HttpServletResponse response) {
        // 构建查询条件
        QueryWrap<BaseDistributor> wrap = new QueryWrap<>();
        wrap.lambda().eq(SuperEntity::getDeleteFlag, 0);
        
        // 处理查询条件
        if (Objects.nonNull(query.getIsDistributor()) && query.getIsDistributor()) {
            wrap.lambda().eq(BaseDistributor::getCreatedBy, ContextUtil.getUserId());
        } else {
            if (Objects.nonNull(query.getParentId())) {
                wrap.lambda().eq(BaseDistributor::getParentId, query.getParentId());
            } else {
                wrap.lambda().eq(BaseDistributor::getParentId, 0L);
            }
        }
        
        if (StringUtils.isNotBlank(query.getKeyword())) {
            wrap.lambda().and(e ->
                    e.like(BaseDistributor::getCode, query.getKeyword())
                    .or()
                    .like(BaseDistributor::getShopName, query.getKeyword())
                    .or()
                    .like(BaseDistributor::getMobile, query.getKeyword())
                    .or()
                    .like(BaseDistributor::getName, query.getKeyword()));
        }
        wrap.lambda().orderByDesc(SuperEntity::getCreatedTime);
        
        // 查询数据
        List<BaseDistributor> list = superService.list(wrap);
        List<BaseDistributorResultVO> resultVOList = BeanPlusUtil.toBeanList(list, BaseDistributorResultVO.class);
        
        // 处理拿货数据和下级经销商数据
        if (CollUtil.isNotEmpty(resultVOList)) {
            List<Long> distributorIds = resultVOList.stream().map(BaseDistributorResultVO::getId).distinct().collect(Collectors.toList());
            List<BaseDistributor> childDistributorList = superService.list(Wraps.<BaseDistributor>lbQ().in(BaseDistributor::getParentId, distributorIds));
            List<Long> childDistributeIds = childDistributorList.stream().map(SuperEntity::getId).distinct().collect(Collectors.toList());
            List<Long> allDistributorIds = Lists.newArrayList(distributorIds);
            allDistributorIds.addAll(childDistributeIds);
            List<BaseDistributorPurchaseResultVO> purchaseSumList = basePurchaseService.purchaseSumList(allDistributorIds);
            Map<Long, BaseDistributorPurchaseResultVO> purchaseSumMap = purchaseSumList.stream().collect(Collectors.toMap(BaseDistributorPurchaseResultVO::getDistributorId, Function.identity()));
            
            // 建立父子关系映射：父经销商ID -> 子经销商ID列表
            Map<Long, List<Long>> parentChildMap = childDistributorList.stream()
                    .collect(Collectors.groupingBy(BaseDistributor::getParentId, 
                            Collectors.mapping(SuperEntity::getId, Collectors.toList())));
            
            for (BaseDistributorResultVO record : resultVOList) {
                // 设置当前经销商的拿货数据
                if (purchaseSumMap.containsKey(record.getId())) {
                    BaseDistributorPurchaseResultVO purchaseResultVO = purchaseSumMap.get(record.getId());
                    record.setPurchaseNum(Objects.nonNull(purchaseResultVO.getPurchaseNum()) ? purchaseResultVO.getPurchaseNum() : 0);
                    record.setPurchaseCount(Objects.nonNull(purchaseResultVO.getPurchaseCount()) ? purchaseResultVO.getPurchaseCount() : 0L);
                    record.setPurchaseTime(purchaseResultVO.getPurchaseTime());
                } else {
                    record.setPurchaseNum(0);
                    record.setPurchaseCount(0L);
                    record.setPurchaseTime(null);
                }
                
                // 计算下级经销商拿货数量
                int childPurchaseNum = 0;
                List<Long> childIds = parentChildMap.get(record.getId());
                if (CollUtil.isNotEmpty(childIds)) {
                    for (Long childId : childIds) {
                        BaseDistributorPurchaseResultVO childPurchase = purchaseSumMap.get(childId);
                        if (Objects.nonNull(childPurchase) && Objects.nonNull(childPurchase.getPurchaseNum())) {
                            childPurchaseNum += childPurchase.getPurchaseNum();
                        }
                    }
                }
                record.setChildPurchaseNum(childPurchaseNum);
            }
        }
        
        // Echo处理
        echoService.action(resultVOList);
        
        // 使用EasyPOI导出Excel
        ExportParams exportParams = new ExportParams("经销商信息", "经销商数据");
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, BaseDistributorResultVO.class, resultVOList);
        
        // 设置响应头
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf8");
        response.setHeader("Content-disposition", "attachment;filename=Distributor.xls");
        
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            workbook.write(outputStream);
            workbook.close();
        } catch (IOException e) {
            log.error("导出失败", e);
            throw new RuntimeException(e);
        }
    }

 

    @Override
    public R<BaseDistributorResultVO> getDetail(Long aLong) {
        BaseDistributor baseDistributor = superService.getById(aLong);
        if (Objects.isNull(baseDistributor)) {
            return R.success(null);
        }
        BaseDistributorResultVO resultVO = BeanPlusUtil.toBean(baseDistributor, BaseDistributorResultVO.class);
        List<BaseDistributorPurchaseResultVO> purchaseSumList = basePurchaseService.purchaseSumList(Collections.singletonList(resultVO.getId()));
        if (CollUtil.isNotEmpty(purchaseSumList)) {
            BaseDistributorPurchaseResultVO purchaseResultVO = purchaseSumList.get(0);
            resultVO.setPurchaseNum(Objects.nonNull(purchaseResultVO.getPurchaseNum()) ? purchaseResultVO.getPurchaseNum() : 0);
            resultVO.setPurchaseCount(Objects.nonNull(purchaseResultVO.getPurchaseCount()) ? purchaseResultVO.getPurchaseCount() : 0L);
            resultVO.setPurchaseTime(purchaseResultVO.getPurchaseTime());
        }
        
        // 计算下级经销商拿货数量
        List<BaseDistributor> childDistributorList = superService.list(Wraps.<BaseDistributor>lbQ().eq(BaseDistributor::getParentId, aLong));
        int childPurchaseNum = 0;
        if (CollUtil.isNotEmpty(childDistributorList)) {
            List<Long> childIds = childDistributorList.stream().map(SuperEntity::getId).collect(Collectors.toList());
            List<BaseDistributorPurchaseResultVO> childPurchaseSumList = basePurchaseService.purchaseSumList(childIds);
            childPurchaseNum = childPurchaseSumList.stream()
                    .mapToInt(purchase -> Objects.nonNull(purchase.getPurchaseNum()) ? purchase.getPurchaseNum() : 0)
                    .sum();
        }
        resultVO.setChildPurchaseNum(childPurchaseNum);
        
        return R.success(resultVO);
    }
}


