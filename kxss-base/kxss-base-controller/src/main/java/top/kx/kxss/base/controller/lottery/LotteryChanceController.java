package top.kx.kxss.base.controller.lottery;

import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.base.controller.SuperController;
import top.kx.kxss.base.service.lottery.LotteryChanceService;
import top.kx.kxss.base.entity.lottery.LotteryChance;
import top.kx.kxss.base.vo.save.lottery.LotteryChanceSaveVO;
import top.kx.kxss.base.vo.update.lottery.LotteryChanceUpdateVO;
import top.kx.kxss.base.vo.result.lottery.LotteryChanceResultVO;
import top.kx.kxss.base.vo.query.lottery.LotteryChancePageQuery;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 前端控制器
 * 用户抽奖机会表
 * </p>
 *
 * <AUTHOR>
 * @date 2025-09-16 15:57:00
 * @create [2025-09-16 15:57:00] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/lotteryChance")
@Api(value = "LotteryChance", tags = "用户抽奖机会表")
public class LotteryChanceController extends SuperController<LotteryChanceService, Long, LotteryChance, LotteryChanceSaveVO,
    LotteryChanceUpdateVO, LotteryChancePageQuery, LotteryChanceResultVO> {
    private final EchoService echoService;
    @Override
    public EchoService getEchoService() {
        return echoService;
    }

}


