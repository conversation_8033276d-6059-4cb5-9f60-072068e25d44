package top.kx.kxss.base.controller.user;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.kxss.base.service.user.UserService;
import top.kx.kxss.base.vo.update.user.UserPasswordUpdateVO;
import top.kx.kxss.base.vo.update.user.UserSelfUpdateVO;

import javax.annotation.Resource;


/**
 * <p>
 * 前端控制器
 * 员工
 * </p>
 *
 * <AUTHOR>
 * @date 2021-10-18
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/user")
@Api(value = "user", tags = "用户基础信息")
public class UserController {

    @Resource
    private UserService userService;


    @ApiOperation(value = "更新用户基本信息", notes = "更新用户基本信息")
    @PutMapping(value = "/updateSelf")
    public R<Boolean> updateSelf(@RequestBody @Validated UserSelfUpdateVO updateVO) {
        return R.success(userService.updateSelf(updateVO));
    }


    @ApiOperation(value = "更新密码", notes = "更新密码")
    @PutMapping(value = "/updatePassword")
    public R<Boolean> updatePassword(@RequestBody @Validated UserPasswordUpdateVO updateVO) {
        return R.success(userService.updatePassword(updateVO));
    }

}
