package top.kx.kxss.file.controller;

import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.controller.DeleteController;
import top.kx.basic.base.controller.QueryController;
import top.kx.basic.base.controller.SuperSimpleController;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.kxss.file.entity.File;
import top.kx.kxss.file.service.FileService;

/**
 * <p>
 * 前端控制器
 * 增量文件上传日志
 * </p>
 *
 * <AUTHOR>
 * @date 2021-06-30
 * @create [2021-06-30] [tangyh] [初始创建]
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/file")
@RequiredArgsConstructor
@Api(value = "FileBaseController", tags = "租户库-文件操作接口")
public class FileBaseController extends SuperSimpleController<FileService, Long, File, File, File, File, File>
        implements QueryController<Long, File, File, File, File, File>, DeleteController<Long, File, File, File, File, File> {
    private final EchoService echoService;

    @Override
    public EchoService getEchoService() {
        return echoService;
    }

    @Override
    public Class<File> getResultVOClass() {
        return File.class;
    }

}
