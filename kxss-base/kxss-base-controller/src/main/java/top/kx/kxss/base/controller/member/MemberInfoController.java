package top.kx.kxss.base.controller.member;

import cn.hutool.core.util.DesensitizedUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.base.entity.member.MemberInfo;
import top.kx.kxss.base.service.member.MemberInfoService;
import top.kx.kxss.base.vo.query.member.MemberInfoPageQuery;
import top.kx.kxss.base.vo.result.member.MemberInfoResultVO;
import top.kx.kxss.base.vo.save.member.MemberInfoSaveVO;
import top.kx.kxss.base.vo.update.member.MemberInfoUpdateVO;

import java.util.List;

/**
 * <p>
 * 前端控制器
 * 会员信息
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-24 18:03:33
 * @create [2023-05-24 18:03:33] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/memberInfo")
@Api(value = "MemberInfo", tags = "会员信息")
public class MemberInfoController extends SuperController<MemberInfoService, Long, MemberInfo, MemberInfoSaveVO,
        MemberInfoUpdateVO, MemberInfoPageQuery, MemberInfoResultVO> {
    private final EchoService echoService;

    @Override
    public EchoService getEchoService() {
        return echoService;
    }


    @Override
    public R<List<MemberInfoResultVO>> query(MemberInfoPageQuery data) {
        R<List<MemberInfoResultVO>> query = super.query(data);
        for (MemberInfoResultVO memberInfoResultVO : query.getData()) {
            memberInfoResultVO.setMobile(DesensitizedUtil.mobilePhone(memberInfoResultVO.getMobile()));
            memberInfoResultVO.setNameAndMobile(memberInfoResultVO.getName() + "-" + memberInfoResultVO.getMobile());
        }
        return query;
    }

    @Override
    public void handlerResult(IPage<MemberInfoResultVO> page) {
        for (MemberInfoResultVO record : page.getRecords()) {
            record.setMobile(DesensitizedUtil.mobilePhone(record.getMobile()));
        }
        super.handlerResult(page);
    }

    @Override
    public R<MemberInfoResultVO> getDetail(Long aLong) {
        MemberInfo memberInfo = superService.getById(aLong);
        MemberInfoResultVO resultVO = BeanPlusUtil.toBean(memberInfo, MemberInfoResultVO.class);
        resultVO.setMobile(DesensitizedUtil.mobilePhone(resultVO.getMobile()));
        echoService.action(resultVO);
        return R.success(resultVO);
    }

    @Override
    public R<MemberInfo> handlerSave(MemberInfoSaveVO model) {
        model.setProductNum(0);
        return super.handlerSave(model);
    }
}


