//package top.kx.kxss.base.controller.user;
//
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//import top.kx.basic.annotation.log.WebLog;
//import top.kx.basic.base.R;
//import top.kx.kxss.base.service.user.BaseDistributorService;
//import top.kx.kxss.base.vo.query.user.BaseEmployeePageQuery;
//import top.kx.kxss.base.vo.result.user.BaseEmployeeResultVO;
//import top.kx.kxss.base.vo.save.user.BaseDistributorSaveVO;
//
//import java.util.List;
//
///**
// * <p>
// * 前端控制器
// * 经销商
// * </p>
// *
// * <AUTHOR>
// * @date 2021-10-18
// */
//@Slf4j
//@Validated
//@RestController
//@RequestMapping("/baseDistributor")
//@Api(value = "baseDistributor", tags = "经销商")
//public class BaseDistributorController {
//
//
//    private final BaseDistributorService baseDistributorService;
//
//    @Autowired
//    public BaseDistributorController(BaseDistributorService baseDistributorService) {
//        this.baseDistributorService = baseDistributorService;
//    }
//
//    @ApiOperation("新增经销商")
//    @PostMapping("/save")
//    @WebLog("新增经销商")
//    public R<BaseEmployeeResultVO> save(@RequestBody @Validated BaseDistributorSaveVO model) {
//        return R.success(baseDistributorService.save(model));
//    }
//
//    @ApiOperation("查询经销商")
//    @PostMapping("/query")
//    @WebLog("查询经销商")
//    public R<List<BaseEmployeeResultVO>> query(@RequestBody BaseEmployeePageQuery query) {
//        return R.success(baseDistributorService.query(query));
//    }
//
//
//}
