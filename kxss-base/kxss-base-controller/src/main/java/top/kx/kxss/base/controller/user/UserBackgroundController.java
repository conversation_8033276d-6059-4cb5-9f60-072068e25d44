package top.kx.kxss.base.controller.user;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.kxss.base.entity.user.UserBackground;
import top.kx.kxss.base.service.user.UserBackgroundService;
import top.kx.kxss.base.vo.query.user.UserBackgroundPageQuery;
import top.kx.kxss.base.vo.result.user.UserBackgroundResultVO;
import top.kx.kxss.base.vo.save.user.UserBackgroundSaveVO;
import top.kx.kxss.base.vo.update.user.UserBackgroundUpdateVO;

import static top.kx.basic.exception.code.ExceptionCode.BASE_VALID_PARAM;
import static top.kx.kxss.common.constant.SwaggerConstants.DATA_TYPE_MULTIPART_FILE;
import static top.kx.kxss.common.constant.SwaggerConstants.DATA_TYPE_STRING;

/**
 * <p>
 * 前端控制器
 * 用户背景图
 * </p>
 *
 * <AUTHOR>
 * @date 2025-04-19 16:25:46
 * @create [2025-04-19 16:25:46] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/userBackground")
@Api(value = "userBackground", tags = "用户背景图")
public class UserBackgroundController extends SuperController<UserBackgroundService, Long, UserBackground, UserBackgroundSaveVO,
        UserBackgroundUpdateVO, UserBackgroundPageQuery, UserBackgroundResultVO> {
    private final EchoService echoService;

    @Override
    public EchoService getEchoService() {
        return echoService;
    }


    @ApiOperation(value = "上传背景图", notes = "背景图上传")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "附件", dataType = DATA_TYPE_MULTIPART_FILE, allowMultiple = true, required = true),
            @ApiImplicitParam(name = "appKey", value = "appKey:EMPLOYEE_SERVICE", dataType = DATA_TYPE_STRING, required = false),
    })
    @PostMapping(value = "/upload")
    @WebLog("上传背景图")
    public R<UserBackground> upload(@RequestParam(value = "file") MultipartFile file,
                                  @RequestParam(value = "appKey", required = false, defaultValue = "EMPLOYEE_SERVICE") String appKey) {
        // 忽略路径字段,只处理文件类型
        if (file.isEmpty()) {
            return R.validFail(BASE_VALID_PARAM.build("请上传有效文件"));
        }
        return R.success(superService.upload(file, appKey));
    }


    @ApiOperation(value = "查询应用背景图", notes = "查询应用背景图")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "appKey", value = "appKey:EMPLOYEE_SERVICE", dataType = DATA_TYPE_STRING, required = false),
    })
    @GetMapping(value = "/getOneByAppKey")
    @WebLog("查询应用背景图")
    public R<UserBackgroundResultVO> getOneByAppKey(@RequestParam(value = "appKey", required = false, defaultValue = "EMPLOYEE_SERVICE") String appKey) {
        return R.success(superService.getOneByAppKey(appKey));
    }

}


