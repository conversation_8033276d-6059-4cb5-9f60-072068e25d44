package top.kx.kxss.base.controller.mobile;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.kxss.base.entity.dealers.BaseDealers;
import top.kx.kxss.base.entity.distributor.BaseDistributor;
import top.kx.kxss.base.entity.member.MemberInfo;
import top.kx.kxss.base.service.dealers.BaseDealersService;
import top.kx.kxss.base.service.distributor.BaseDistributorService;
import top.kx.kxss.base.service.member.MemberInfoService;
import top.kx.kxss.base.vo.query.BaseMobileQuery;
import top.kx.kxss.model.enumeration.base.MobileTypeEnum;

import java.util.Objects;

/**
 * <p>
 * 前端控制器
 * 耗材经销商信息
 * </p>
 *
 * <AUTHOR>
 * @date 2025-03-07 15:45:07
 * @create [2025-03-07 15:45:07] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/mobile")
@Api(value = "Mobile", tags = "耗材经销商信息")
public class MobileController {
    private final BaseDistributorService baseDistributorService;
    private final MemberInfoService memberInfoService;
    private final BaseDealersService baseDealersService;

    /**
     * 根据经销商ID获取手机号信息，非脱敏
     */
    @ApiOperation(value = "手机号脱敏解析")
    @PostMapping("/getMobile")
    public R<String> getMobile(@RequestBody @Validated BaseMobileQuery query) {
        MobileTypeEnum mobileTypeEnum = MobileTypeEnum.get(query.getType());
        String mobile = null;
        switch (mobileTypeEnum) {
            case DISTRIBUTOR:
                BaseDistributor baseDistributor = baseDistributorService.getById(query.getId());
                if (Objects.nonNull(baseDistributor)) {
                    mobile = baseDistributor.getMobile();
                }
                break;
            case MEMBER:
                MemberInfo memberInfo = memberInfoService.getById(query.getId());
                if (Objects.nonNull(memberInfo)) {
                    mobile = memberInfo.getMobile();
                }
                break;
            case DEALERS:
                BaseDealers baseDealers = baseDealersService.getById(query.getId());
                if (Objects.nonNull(baseDealers)) {
                    mobile = baseDealers.getMobile();
                }
                break;
        }
        return R.success(mobile);
    }
}


