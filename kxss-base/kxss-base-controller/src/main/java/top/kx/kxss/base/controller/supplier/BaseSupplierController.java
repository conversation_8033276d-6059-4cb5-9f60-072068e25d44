package top.kx.kxss.base.controller.supplier;

import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.base.controller.SuperController;
import top.kx.kxss.base.service.BaseSupplierService;
import top.kx.kxss.base.entity.supplier.BaseSupplier;
import top.kx.kxss.base.vo.save.supplier.BaseSupplierSaveVO;
import top.kx.kxss.base.vo.update.supplier.BaseSupplierUpdateVO;
import top.kx.kxss.base.vo.result.supplier.BaseSupplierResultVO;
import top.kx.kxss.base.vo.query.supplier.BaseSupplierPageQuery;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 前端控制器
 * 供应商信息信息
 * </p>
 *
 * <AUTHOR>
 * @date 2025-02-28 16:36:35
 * @create [2025-02-28 16:36:35] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/baseSupplier")
@Api(value = "BaseSupplier", tags = "供应商信息信息")
public class BaseSupplierController extends SuperController<BaseSupplierService, Long, BaseSupplier, BaseSupplierSaveVO,
    BaseSupplierUpdateVO, BaseSupplierPageQuery, BaseSupplierResultVO> {
    private final EchoService echoService;
    @Override
    public EchoService getEchoService() {
        return echoService;
    }

}


