package top.kx.kxss.base.controller.customers;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.database.mybatis.conditions.query.QueryWrap;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.kxss.base.entity.customers.BaseCustomers;
import top.kx.kxss.base.service.customers.BaseCustomersService;
import top.kx.kxss.base.vo.query.customers.BaseCustomersPageQuery;
import top.kx.kxss.base.vo.result.customers.BaseCustomersResultVO;
import top.kx.kxss.base.vo.save.customers.BaseCustomersSaveVO;
import top.kx.kxss.base.vo.update.customers.BaseCustomersUpdateVO;

import javax.validation.constraints.NotNull;
import java.util.Objects;

/**
 * <p>
 * 前端控制器
 * 客户咨询信息
 * </p>
 *
 * <AUTHOR>
 * @date 2024-12-19 10:30:39
 * @create [2024-12-19 10:30:39] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/baseCustomers")
@Api(value = "BaseCustomers", tags = "客户咨询信息")
public class BaseCustomersController extends SuperController<BaseCustomersService, Long, BaseCustomers, BaseCustomersSaveVO,
        BaseCustomersUpdateVO, BaseCustomersPageQuery, BaseCustomersResultVO> {
    private final EchoService echoService;

    @Override
    public EchoService getEchoService() {
        return echoService;
    }


    /**
     * 领取
     *
     * @param id id
     */
    @ApiOperation(value = "领取", notes = "领取")
    @PostMapping("/receive")
    @WebLog("领取")
    public R<Boolean> receive(
            @NotNull(message = "请选择id") @RequestParam Long id) {
        return success(superService.receive(id));
    }


    @Override
    public QueryWrap<BaseCustomers> handlerWrapper(BaseCustomers model, PageParams<BaseCustomersPageQuery> params) {
        QueryWrap<BaseCustomers> baseCustomersQueryWrap = super.handlerWrapper(model, params);
        BaseCustomersPageQuery pageQuery = params.getModel();
        if (Objects.nonNull(pageQuery.getStartCollectionTime())) {
            baseCustomersQueryWrap.lambda().ge(BaseCustomers::getCollectionTime, pageQuery.getStartCollectionTime());
        }
        if (Objects.nonNull(pageQuery.getEndCollectionTime())) {
            baseCustomersQueryWrap.lambda().le(BaseCustomers::getCollectionTime, pageQuery.getEndCollectionTime());
        }
        return baseCustomersQueryWrap;
    }
}


