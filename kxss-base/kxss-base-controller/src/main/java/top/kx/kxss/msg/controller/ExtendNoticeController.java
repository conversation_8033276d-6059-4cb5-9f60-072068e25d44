package top.kx.kxss.msg.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.msg.entity.ExtendNotice;
import top.kx.kxss.msg.enumeration.NoticeRemindModeEnum;
import top.kx.kxss.msg.service.ExtendNoticeService;
import top.kx.kxss.msg.vo.MyMsgResult;
import top.kx.kxss.msg.vo.query.ExtendNoticePageQuery;
import top.kx.kxss.msg.vo.result.ExtendNoticeResultVO;
import top.kx.kxss.msg.vo.save.ExtendNoticeSaveVO;
import top.kx.kxss.msg.vo.update.ExtendNoticeUpdateVO;

import java.util.List;

/**
 * <p>
 * 前端控制器
 * 通知表
 * </p>
 *
 * <AUTHOR>
 * @date 2022-07-04 15:51:37
 * @create [2022-07-04 15:51:37] [zuihou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/extendNotice/anyone")
@Api(value = "ExtendNotice", tags = "通知表")
public class ExtendNoticeController extends SuperController<ExtendNoticeService, Long, ExtendNotice, ExtendNoticeSaveVO,
        ExtendNoticeUpdateVO, ExtendNoticePageQuery, ExtendNoticeResultVO> {
    private final EchoService echoService;

    @Override
    public EchoService getEchoService() {
        return echoService;
    }

    @Override
    public void handlerQueryParams(PageParams<ExtendNoticePageQuery> params) {
        params.getModel().setRecipientId(ContextUtil.getEmployeeId());
    }


    @ApiOperation(value = "全量查询我的未读消息", notes = "全量查询我的消息")
    @PostMapping("/myNotice")
    @WebLog(value = "'全量查询我的消息:第' + #params?.current + '页, 显示' + #params?.size + '行'", response = false)
    public R<MyMsgResult> myNotice(@RequestBody @Validated PageParams<ExtendNoticePageQuery> params) {

        IPage<ExtendNotice> todoList = params.buildPage(ExtendNotice.class);
        IPage<ExtendNotice> noticeList = params.buildPage(ExtendNotice.class);
        IPage<ExtendNotice> earlyWarningList = params.buildPage(ExtendNotice.class);
        superService.page(todoList, Wraps.<ExtendNotice>lbQ()
                .eq(ExtendNotice::getRemindMode, NoticeRemindModeEnum.TO_DO.getValue()).
                eq(ExtendNotice::getIsRead, false).eq(ExtendNotice::getRecipientId, ContextUtil.getEmployeeId()));
        superService.page(noticeList, Wraps.<ExtendNotice>lbQ()
                .eq(ExtendNotice::getRemindMode, NoticeRemindModeEnum.NOTICE.getValue()).
                eq(ExtendNotice::getIsRead, false).eq(ExtendNotice::getRecipientId, ContextUtil.getEmployeeId()));
        superService.page(earlyWarningList, Wraps.<ExtendNotice>lbQ()
                .eq(ExtendNotice::getRemindMode, NoticeRemindModeEnum.EARLY_WARNING.getValue()).
                eq(ExtendNotice::getIsRead, false).eq(ExtendNotice::getRecipientId, ContextUtil.getEmployeeId()));

        MyMsgResult result = MyMsgResult.builder()
                .todoList(BeanPlusUtil.toBeanPage(todoList, ExtendNoticeResultVO.class))
                .noticeList(BeanPlusUtil.toBeanPage(noticeList, ExtendNoticeResultVO.class))
                .earlyWarningList(BeanPlusUtil.toBeanPage(earlyWarningList, ExtendNoticeResultVO.class))
                .build();
        return R.success(result);
    }


    /**
     * 标记消息为已读
     *
     * @param noticeIds 主表id
     * @return 是否成功
     */
    @ApiOperation(value = "标记消息为已读", notes = "标记消息为已读")
    @PostMapping(value = "/mark")
    public R<Boolean> mark(@RequestBody List<Long> noticeIds) {
        return R.success(superService.mark(noticeIds, ContextUtil.getEmployeeId()));
    }

    /**
     * 删除消息中心
     *
     * @param receiveIds 接收id
     * @return 删除结果
     */
    @ApiOperation(value = "删除我的消息", notes = "根据id物理删除我的消息")
    @DeleteMapping("/deleteMyNotice")
    @WebLog("删除我的消息")
    public R<Boolean> deleteMyMsg(@RequestBody List<Long> receiveIds) {
        return R.success(superService.deleteMyNotice(receiveIds));
    }

}


