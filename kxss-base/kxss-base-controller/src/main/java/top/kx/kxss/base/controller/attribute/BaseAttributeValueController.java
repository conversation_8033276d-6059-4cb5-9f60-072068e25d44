package top.kx.kxss.base.controller.attribute;

import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.base.controller.SuperController;
import top.kx.kxss.base.service.attribute.BaseAttributeValueService;
import top.kx.kxss.base.entity.attribute.BaseAttributeValue;
import top.kx.kxss.base.vo.save.attribute.BaseAttributeValueSaveVO;
import top.kx.kxss.base.vo.update.attribute.BaseAttributeValueUpdateVO;
import top.kx.kxss.base.vo.result.attribute.BaseAttributeValueResultVO;
import top.kx.kxss.base.vo.query.attribute.BaseAttributeValuePageQuery;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 前端控制器
 * 商品基础属性值
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-16 18:44:16
 * @create [2025-06-16 18:44:16] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/baseAttributeValue")
@Api(value = "BaseAttributeValue", tags = "商品基础属性值")
public class BaseAttributeValueController extends SuperController<BaseAttributeValueService, Long, BaseAttributeValue, BaseAttributeValueSaveVO,
    BaseAttributeValueUpdateVO, BaseAttributeValuePageQuery, BaseAttributeValueResultVO> {
    private final EchoService echoService;
    @Override
    public EchoService getEchoService() {
        return echoService;
    }

}


