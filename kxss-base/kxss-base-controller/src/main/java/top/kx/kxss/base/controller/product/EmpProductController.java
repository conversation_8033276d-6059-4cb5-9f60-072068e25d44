package top.kx.kxss.base.controller.product;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.base.entity.product.BaseProduct;
import top.kx.kxss.base.entity.product.BaseProductAttribute;
import top.kx.kxss.base.service.product.BaseProductAttributeService;
import top.kx.kxss.base.service.product.BaseProductService;
import top.kx.kxss.base.vo.query.product.BaseProductPageQuery;
import top.kx.kxss.base.vo.result.product.BaseProductAttributeResultVO;
import top.kx.kxss.base.vo.result.product.EmpProductResultVO;
import top.kx.kxss.base.vo.save.product.BaseProductSaveVO;
import top.kx.kxss.base.vo.update.product.BaseProductUpdateVO;
import top.kx.kxss.file.entity.File;
import top.kx.kxss.file.service.FileService;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 前端控制器
 * 商品表
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-25 13:52:40
 * @create [2023-05-25 13:52:40] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/empProduct")
@Api(value = "EmpProduct", tags = "商品信息-员工端")
public class EmpProductController {
    @Resource
    private BaseProductService productService;
    @Resource
    private EchoService echoService;
    @Resource
    private FileService fileService;
    private final BaseProductAttributeService baseProductAttributeService;

    @ApiOperation(value = "分页查询商品信息", notes = "分页查询商品信息")
    @PostMapping("/page")
    @WebLog(value = "'分页查询商品信息:第' + #params?.current + '页, 显示' + #params?.size + '行'", response = false)
    public R<IPage<EmpProductResultVO>> page(@RequestBody PageParams<BaseProductPageQuery> params) {
        params.setSort("");
        params.setOrder("");
        IPage<BaseProduct> page = params.buildPage(BaseProduct.class);
        BaseProductPageQuery query = params.getModel();
        BaseProduct model = BeanUtil.toBean(query, BaseProduct.class);
        LbQueryWrap<BaseProduct> wraps = Wraps.lbq(model, params.getExtra(), BaseProduct.class);
        wraps.orderByAsc(BaseProduct::getSortValue);
        productService.page(page, wraps);
        IPage<EmpProductResultVO> pageVO = BeanPlusUtil.toBeanPage(page, EmpProductResultVO.class);
        echoService.action(pageVO);
        List<EmpProductResultVO> records = pageVO.getRecords();
        //图片信息
        List<Long> productImages = records.stream().map(EmpProductResultVO::getProductImage).collect(Collectors.toList());
        Map<Long, File> fileMap = CollectionUtil.isNotEmpty(productImages) ? fileService.list(Wraps.<File>lbQ().in(File::getId, productImages))
                .stream().collect(Collectors.toMap(File::getId, k -> k)) : new HashMap<>();

        for (EmpProductResultVO record : records) {
            if (ObjectUtil.isNotNull(record.getProductImage())) {
                record.setProductImageFile(fileMap.get(record.getProductImage()));
            }
        }
        return R.success(pageVO);
    }

    @ApiOperation("根据商品ID查询信息")
    @GetMapping("/detail")
    @WebLog("根据商品ID查询信息")
    public R<EmpProductResultVO> detail(@RequestParam Long id) {
        BaseProduct byId = productService.getById(id);
        if (ObjectUtil.isNull(byId)) {
            return R.success(null);
        }
        EmpProductResultVO empProductResultVO = BeanUtil.copyProperties(byId, EmpProductResultVO.class);
        List<BaseProductAttribute> productAttributeList = baseProductAttributeService.list(Wraps.<BaseProductAttribute>lbQ().in(BaseProductAttribute::getProductId, empProductResultVO.getId()));
        if (CollUtil.isNotEmpty(productAttributeList)) {
            empProductResultVO.setAttributeIds(productAttributeList.stream().map(BaseProductAttribute::getAttributeId).collect(Collectors.toList()));
        }
        echoService.action(empProductResultVO);
        if (ObjectUtil.isNotNull(empProductResultVO.getProductImage())) {
            empProductResultVO.setProductImageFile(fileService.getById(empProductResultVO.getProductImage()));
        }
        return R.success(empProductResultVO);
    }


    @ApiOperation("根据商品ID查询属性")
    @GetMapping("/getAttributeByProductId")
    @WebLog("根据商品ID查询信息")
    public R<List<BaseProductAttributeResultVO>> getAttributeByProductId(@RequestParam Long id) {
        List<BaseProductAttribute> productAttributeList = baseProductAttributeService.list(Wraps.<BaseProductAttribute>lbQ().in(BaseProductAttribute::getProductId, id));
        List<BaseProductAttributeResultVO> resultVOList = BeanPlusUtil.toBeanList(productAttributeList, BaseProductAttributeResultVO.class);
        echoService.action(resultVOList);
        return R.success(resultVOList);
    }

    @ApiOperation("新增商品")
    @PostMapping("/save")
    @WebLog("新增商品")
    public R<EmpProductResultVO> save(@RequestBody @Validated BaseProductSaveVO model) {
        model.setCode(StrUtil.isBlank(model.getCode()) ? productService.getCode() : model.getCode());
        BaseProduct baseProduct = productService.save(model);
        EmpProductResultVO empProductResultVO = BeanUtil.copyProperties(baseProduct, EmpProductResultVO.class);
        echoService.action(empProductResultVO);
        return R.success(empProductResultVO);
    }

    @ApiOperation("编辑商品")
    @PostMapping("/update")
    @WebLog("编辑商品")
    public R<EmpProductResultVO> update(@RequestBody @Validated BaseProductUpdateVO model) {
        BaseProduct baseProduct = productService.updateById(model);
        EmpProductResultVO empProductResultVO = BeanUtil.copyProperties(baseProduct, EmpProductResultVO.class);
        echoService.action(empProductResultVO);
        return R.success(empProductResultVO);
    }



    @ApiOperation("查询商品列表")
    @PostMapping("/query")
    @WebLog("查询商品列表")
    public R<List<EmpProductResultVO>> query(@RequestBody BaseProductPageQuery query) {
        LbQueryWrap<BaseProduct> wrap = Wraps.<BaseProduct>lbQ().eq(BaseProduct::getState, true);
        // 判断是否是经销商查询 临时要求， 经销商只查询 pro版皮头， 20250427 后续需要修改逻辑，这是临时方案
        if (Objects.nonNull(query) && query.getIsDistributor()) {
            wrap.eq(BaseProduct::getCode, "SP20230713593373");
        }
        wrap.orderByAsc(BaseProduct::getSortValue);
        List<EmpProductResultVO> list = productService.list(wrap).stream()
                .map(v -> BeanUtil.copyProperties(v, EmpProductResultVO.class)).collect(Collectors.toList());
        echoService.action(list);
        return R.success(list);
    }

}


