package top.kx.kxss.base.controller.lottery;

import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.base.controller.SuperController;
import top.kx.kxss.base.service.lottery.LotteryActivityPrizeService;
import top.kx.kxss.base.entity.lottery.LotteryActivityPrize;
import top.kx.kxss.base.vo.save.lottery.LotteryActivityPrizeSaveVO;
import top.kx.kxss.base.vo.update.lottery.LotteryActivityPrizeUpdateVO;
import top.kx.kxss.base.vo.result.lottery.LotteryActivityPrizeResultVO;
import top.kx.kxss.base.vo.query.lottery.LotteryActivityPrizePageQuery;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 前端控制器
 * 活动奖品配置表
 * </p>
 *
 * <AUTHOR>
 * @date 2025-09-15 17:23:11
 * @create [2025-09-15 17:23:11] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/lotteryActivityPrize")
@Api(value = "LotteryActivityPrize", tags = "活动奖品配置表")
public class LotteryActivityPrizeController extends SuperController<LotteryActivityPrizeService, Long, LotteryActivityPrize, LotteryActivityPrizeSaveVO,
    LotteryActivityPrizeUpdateVO, LotteryActivityPrizePageQuery, LotteryActivityPrizeResultVO> {
    private final EchoService echoService;
    @Override
    public EchoService getEchoService() {
        return echoService;
    }

}


