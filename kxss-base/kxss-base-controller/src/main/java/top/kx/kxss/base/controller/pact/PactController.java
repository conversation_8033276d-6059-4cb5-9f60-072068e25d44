package top.kx.kxss.base.controller.pact;

import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.kxss.base.service.pact.BasePactService;
import top.kx.kxss.base.vo.result.pact.PactResultVO;
import top.kx.kxss.model.enumeration.base.PactTypeEnum;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 前端控制器
 * 协议配置
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-25 17:21:04
 * @create [2023-05-25 17:21:04] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/pact")
@Api(value = "Pact", tags = "协议配置-用户端")
public class PactController {

    @Resource
    private BasePactService pactService;


    /**
     * 根据类型查询数据（1登录协议 2 用户协议） 默认为2
     */
    @ApiOperation(value = "根据类型查询数据（1登录协议 2 用户协议） 默认为2", notes = "根据类型查询数据（1登录协议 2 用户协议） 默认为2")
    @GetMapping("/list")
    @WebLog("根据类型查询数据（1登录协议 2 用户协议） 默认为2")
    public R<List<PactResultVO>> list(@RequestParam(required = false) String type) {
        type = StrUtil.isBlank(type) ? PactTypeEnum.USER.getCode() : type;
        return R.success(pactService.getListByType(type));
    }


}


