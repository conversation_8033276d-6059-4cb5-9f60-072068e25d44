package top.kx.kxss.base.controller.user;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperCacheController;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.kxss.base.biz.user.BaseEmployeeBiz;
import top.kx.kxss.base.entity.user.BaseEmployee;
import top.kx.kxss.base.service.user.BaseEmployeeService;
import top.kx.kxss.base.vo.query.user.BaseEmployeePageQuery;
import top.kx.kxss.base.vo.result.user.BaseEmployeeResultVO;
import top.kx.kxss.base.vo.save.system.DefBindUserVO;
import top.kx.kxss.base.vo.save.user.BaseEmployeeRoleRelSaveVO;
import top.kx.kxss.base.vo.save.user.BaseEmployeeSaveVO;
import top.kx.kxss.base.vo.update.user.BaseEmployeeUpdateVO;
import top.kx.kxss.system.entity.tenant.DefUser;
import top.kx.kxss.system.service.tenant.DefUserService;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <p>
 * 前端控制器
 * 员工
 * </p>
 *
 * <AUTHOR>
 * @date 2021-10-18
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/baseEmployee")
@Api(value = "BaseEmployee", tags = "员工")
public class BaseEmployeeController extends SuperCacheController<BaseEmployeeService, Long, BaseEmployee, BaseEmployeeSaveVO, BaseEmployeeUpdateVO,
        BaseEmployeePageQuery, BaseEmployeeResultVO> {

    private final EchoService echoService;
    private final BaseEmployeeBiz baseEmployeeBiz;
    private final DefUserService defUserService;

    @Override
    public EchoService getEchoService() {
        return echoService;
    }

    @Override
    public R<IPage<BaseEmployeeResultVO>> page(@RequestBody @Validated PageParams<BaseEmployeePageQuery> params) {
        IPage<BaseEmployeeResultVO> page = baseEmployeeBiz.findPageResultVO(params);
        handlerResult(page);
        return R.success(page);
    }

    @Override
    public R<BaseEmployeeResultVO> get(@PathVariable Long id) {
        return success(baseEmployeeBiz.getEmployeeUserById(id));
    }

    @Override
    public R<BaseEmployee> handlerUpdate(BaseEmployeeUpdateVO baseEmployeeUpdateVO) {
        if (ObjectUtil.isNotNull(baseEmployeeUpdateVO.getAvatarFile())) {
            baseEmployeeUpdateVO.setAvatarId(baseEmployeeUpdateVO.getAvatarFile().getId());
        }
        return super.handlerUpdate(baseEmployeeUpdateVO);
    }

    @Override
    public void handlerResult(IPage<BaseEmployeeResultVO> page) {
        if (CollUtil.isNotEmpty(page.getRecords())) {
            List<Long> userIds = page.getRecords().stream().map(BaseEmployeeResultVO::getUserId).distinct().collect(Collectors.toList());
            List<DefUser> defUserList = defUserService.list(Wraps.<DefUser>lbQ().in(DefUser::getId, userIds));
            // 转 map
            Map<Long, DefUser> map = defUserList.stream().collect(Collectors.toMap(DefUser::getId, item -> item));
            page.getRecords().forEach(item -> {
                DefUser defUser = map.get(item.getUserId());
                if (ObjectUtil.isNotEmpty(defUser)) {
                    item.setMobile(defUser.getMobile());
                }
            });
        }

        super.handlerResult(page);
    }

    /**
     * 给员工分配角色
     *
     * @param employeeRoleSaveVO 参数
     * @return 新增结果
     */
    @ApiOperation(value = "给员工分配角色", notes = "给员工分配角色")
    @PostMapping("/employeeRole")
    @WebLog("给员工分配角色")
    public R<List<Long>> saveEmployeeRole(@RequestBody BaseEmployeeRoleRelSaveVO employeeRoleSaveVO) {
        return success(superService.saveEmployeeRole(employeeRoleSaveVO));
    }

    /**
     * 查询员工的角色
     *
     * @param employeeId 员工id
     * @return 新增结果
     */
    @ApiOperation(value = "查询员工的角色")
    @GetMapping("/findEmployeeRoleByEmployeeId")
    @WebLog("查询员工的角色")
    public R<List<Long>> findEmployeeRoleByEmployeeId(@RequestParam Long employeeId) {
        return success(superService.findEmployeeRoleByEmployeeId(employeeId));
    }

    @Override
    public R<BaseEmployee> handlerSave(BaseEmployeeSaveVO model) {
        return R.success(baseEmployeeBiz.save(model));
    }


    @ApiOperation(value = "用户绑定企业")
    @PostMapping(value = "/invitationUser")
    @WebLog("租户管理员邀请用户进入企业")
    public R<Boolean> invitationUser(@RequestBody @Validated DefBindUserVO param) {
        return R.success(param.getIsBind() != null && param.getIsBind() ? baseEmployeeBiz.invitationUser(param) : baseEmployeeBiz.unInvitationUser(param));
    }

}
