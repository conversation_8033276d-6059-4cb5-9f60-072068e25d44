package top.kx.kxss.base.controller.banner;

import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.kxss.base.service.banner.BaseBannerService;
import top.kx.kxss.base.vo.result.banner.BaseBannerResultVO;
import top.kx.kxss.model.enumeration.base.BannerTypeEnum;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 前端控制器
 * 轮播图
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-25 15:35:00
 * @create [2023-05-25 15:35:00] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/banner")
@Api(value = "Banner", tags = "轮播图-用户端")
public class BannerController {

    @Resource
    private BaseBannerService baseBannerService;

    /**
     * 根据类型查询轮播图
     */
    @ApiOperation(value = "根据类型查询轮播图 默认为 1 首页", notes = "根据类型查询轮播图")
    @GetMapping("/list")
    @WebLog("根据类型查询轮播图")
    public R<List<BaseBannerResultVO>> list(@RequestParam(required = false) String type) {
        type = StrUtil.isBlank(type) ? BannerTypeEnum.HOME.getCode() : type;
        return R.success(baseBannerService.getBannerListByType(type));
    }


}


