package top.kx.kxss.base.controller.prize;

import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.base.controller.SuperController;
import top.kx.kxss.base.service.prize.DefPrizeStockChangeService;
import top.kx.kxss.base.entity.prize.DefPrizeStockChange;
import top.kx.kxss.base.vo.save.prize.DefPrizeStockChangeSaveVO;
import top.kx.kxss.base.vo.update.prize.DefPrizeStockChangeUpdateVO;
import top.kx.kxss.base.vo.result.prize.DefPrizeStockChangeResultVO;
import top.kx.kxss.base.vo.query.prize.DefPrizeStockChangePageQuery;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 前端控制器
 * 奖品库表库存变动
 * </p>
 *
 * <AUTHOR>
 * @date 2025-09-16 10:01:28
 * @create [2025-09-16 10:01:28] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/defPrizeStockChange")
@Api(value = "DefPrizeStockChange", tags = "奖品库表库存变动")
public class DefPrizeStockChangeController extends SuperController<DefPrizeStockChangeService, Long, DefPrizeStockChange, DefPrizeStockChangeSaveVO,
    DefPrizeStockChangeUpdateVO, DefPrizeStockChangePageQuery, DefPrizeStockChangeResultVO> {
    private final EchoService echoService;
    @Override
    public EchoService getEchoService() {
        return echoService;
    }

}


