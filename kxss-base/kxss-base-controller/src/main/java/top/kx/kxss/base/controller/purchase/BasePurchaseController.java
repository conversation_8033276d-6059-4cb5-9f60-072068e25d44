package top.kx.kxss.base.controller.purchase;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.QueryWrap;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.DateUtils;
import top.kx.kxss.base.entity.distributor.BaseDistributor;
import top.kx.kxss.base.entity.product.BaseProduct;
import top.kx.kxss.base.entity.purchase.BasePurchase;
import top.kx.kxss.base.entity.purchase.BasePurchaseDetails;
import top.kx.kxss.base.service.distributor.BaseDistributorService;
import top.kx.kxss.base.service.product.BaseProductService;
import top.kx.kxss.base.service.purchase.BasePurchaseService;
import top.kx.kxss.base.service.purchase.BasePurchaseDetailsService;
import top.kx.kxss.base.vo.query.purchase.BasePurchasePageQuery;
import top.kx.kxss.base.vo.result.distributor.BaseDistributorResultVO;
import top.kx.kxss.base.vo.result.purchase.BasePurchaseResultVO;
import top.kx.kxss.base.vo.result.purchase.BasePurchaseSumResultVO;
import top.kx.kxss.base.vo.save.purchase.BasePurchaseSaveVO;
import top.kx.kxss.base.vo.update.purchase.BasePurchaseUpdateVO;

import java.util.*;
import java.util.stream.Collectors;

import static top.kx.basic.utils.DateUtils.DEFAULT_DATE_TIME_FORMAT;

/**
 * <p>
 * 前端控制器
 * 要货
 * </p>
 *
 * <AUTHOR>
 * @date 2025-03-10 14:57:47
 * @create [2025-03-10 14:57:47] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/basePurchase")
@Api(value = "BasePurchase", tags = "要货")
public class BasePurchaseController extends SuperController<BasePurchaseService, Long, BasePurchase, BasePurchaseSaveVO,
        BasePurchaseUpdateVO, BasePurchasePageQuery, BasePurchaseResultVO> {
    private final EchoService echoService;
    private final BasePurchaseService basePurchaseService;

    @Override
    public EchoService getEchoService() {
        return echoService;
    }

    private final BaseDistributorService baseDistributorService;
    private final BaseProductService baseProductService;
    private final BasePurchaseDetailsService basePurchaseDetailsService;


    @Override
    public QueryWrap<BasePurchase> handlerWrapper(BasePurchase model, PageParams<BasePurchasePageQuery> params) {
        QueryWrap<BasePurchase> wraps = super.handlerWrapper(model, params);
        BasePurchasePageQuery query = params.getModel();
        if (StringUtils.isNotBlank(query.getStartDate()) && StringUtils.isNotBlank(query.getEndDate())) {
            wraps.lambda().between(BasePurchase::getCreatedTime,
                    DateUtils.format(DateUtils.getStartTime(query.getStartDate()), DEFAULT_DATE_TIME_FORMAT),
                    DateUtils.format(DateUtils.getEndTime(query.getEndDate()), DEFAULT_DATE_TIME_FORMAT));
        }
        if (Objects.nonNull(query.getIsDistributor())) {
            if (query.getIsDistributor()) {
                BaseDistributorResultVO resultVO = baseDistributorService.getByUserId(ContextUtil.getUserId());
                Long parentId = -1L;
                if (Objects.nonNull(resultVO)) {
                    parentId = resultVO.getId();
                }
                wraps.lambda().inSql(BasePurchase::getDistributorId, "select distinct id from base_distributor where delete_flag = 0 and parent_id = " + parentId);
            } else {
                wraps.lambda().inSql(BasePurchase::getDistributorId, "select distinct id from base_distributor where delete_flag = 0 and parent_id = 0");
            }
        }
        if (StrUtil.isNotBlank(query.getKeyword())) {
            List<Long> distributorIds = baseDistributorService.list(Wraps.<BaseDistributor>lbQ().and(wrap ->wrap
                                    .like(BaseDistributor::getCode, query.getKeyword())
                                    .or()
                                    .like(BaseDistributor::getMobile, query.getKeyword())))
                    .stream().map(BaseDistributor::getId).collect(Collectors.toList());
            List<Long> productIds = baseProductService.list(Wraps.<BaseProduct>lbQ()
                            .like(BaseProduct::getName, query.getKeyword()))
                    .stream().map(BaseProduct::getId).collect(Collectors.toList());
            wraps.lambda().and(wrap -> wrap
                    .like(BasePurchase::getCode, query.getKeyword())
                    .or()
                    .in(CollUtil.isNotEmpty(productIds), BasePurchase::getProductId, productIds)
                    .or()
                    .in(CollUtil.isNotEmpty(distributorIds), BasePurchase::getDistributorId, distributorIds)
            );
        }
        return wraps;
    }


    @Override
    public void handlerResult(IPage<BasePurchaseResultVO> page) {
        if (CollUtil.isNotEmpty(page.getRecords())) {
            // 批量查询优化：计算下级经销商拿货数量
            
            // 1. 收集所有拿货单ID和经销商ID
            List<Long> purchaseIds = page.getRecords().stream()
                    .map(BasePurchaseResultVO::getId)
                    .collect(Collectors.toList());
            
            Set<Long> distributorIds = page.getRecords().stream()
                    .map(BasePurchaseResultVO::getDistributorId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
            
            // 2. 批量查询所有拿货单的防伪码详情
            List<BasePurchaseDetails> allPurchaseDetails = basePurchaseDetailsService.list(
                Wraps.<BasePurchaseDetails>lbQ()
                    .in(BasePurchaseDetails::getPurchaseId, purchaseIds)
                    .eq(BasePurchaseDetails::getDeleteFlag, 0)
            );
            
            // 按拿货单ID分组防伪码
            Map<Long, List<String>> purchaseSecurityCodeMap = allPurchaseDetails.stream()
                    .collect(Collectors.groupingBy(
                        BasePurchaseDetails::getPurchaseId,
                        Collectors.mapping(BasePurchaseDetails::getSecurityCode, Collectors.toList())
                    ));
            
            // 3. 批量查询所有相关经销商的下级经销商
            Map<Long, List<BaseDistributor>> parentChildMap = new HashMap<>();
            if (CollUtil.isNotEmpty(distributorIds)) {
                List<BaseDistributor> allChildDistributors = baseDistributorService.list(
                    Wraps.<BaseDistributor>lbQ()
                        .in(BaseDistributor::getParentId, distributorIds)
                        .eq(BaseDistributor::getDeleteFlag, 0)
                );
                parentChildMap = allChildDistributors.stream()
                        .collect(Collectors.groupingBy(BaseDistributor::getParentId));
            }
            
            // 4. 批量查询所有防伪码的拿货详情（用于统计下级拿货）
            Set<String> allSecurityCodes = allPurchaseDetails.stream()
                    .map(BasePurchaseDetails::getSecurityCode)
                    .collect(Collectors.toSet());
            
            Map<String, List<BasePurchaseDetails>> securityCodeChildPurchaseMap = new HashMap<>();
            if (CollUtil.isNotEmpty(allSecurityCodes)) {
                List<BasePurchaseDetails> allChildPurchaseDetails = basePurchaseDetailsService.list(
                    Wraps.<BasePurchaseDetails>lbQ()
                        .in(BasePurchaseDetails::getSecurityCode, allSecurityCodes)
                        .eq(BasePurchaseDetails::getDeleteFlag, 0)
                );
                securityCodeChildPurchaseMap = allChildPurchaseDetails.stream()
                        .collect(Collectors.groupingBy(BasePurchaseDetails::getSecurityCode));
            }
            
            // 5. 为每个拿货单计算下级经销商拿货数量
            final Map<Long, List<BaseDistributor>> finalParentChildMap = parentChildMap;
            final Map<String, List<BasePurchaseDetails>> finalSecurityCodeChildPurchaseMap = securityCodeChildPurchaseMap;
            
            page.getRecords().forEach(purchase -> {
                int childNum = 0;
                
                // 获取当前拿货单的防伪码列表
                List<String> securityCodes = purchaseSecurityCodeMap.get(purchase.getId());
                // 获取当前经销商的下级经销商列表
                List<BaseDistributor> childDistributors = finalParentChildMap.get(purchase.getDistributorId());
                
                if (CollUtil.isNotEmpty(securityCodes) && CollUtil.isNotEmpty(childDistributors)) {
                    // 获取下级经销商ID集合
                    Set<Long> childDistributorIds = childDistributors.stream()
                            .map(BaseDistributor::getId)
                            .collect(Collectors.toSet());
                    
                    // 统计每个防伪码被下级经销商拿货的数量
                    for (String securityCode : securityCodes) {
                        List<BasePurchaseDetails> childPurchaseDetails = finalSecurityCodeChildPurchaseMap.get(securityCode);
                        if (CollUtil.isNotEmpty(childPurchaseDetails)) {
                            // 计算该防伪码被下级经销商拿货的次数
                            long count = childPurchaseDetails.stream()
                                    .filter(detail -> childDistributorIds.contains(detail.getDistributorId()))
                                    .count();
                            childNum += count;
                        }
                    }
                }
                
                // 设置下级经销商拿货数量
                purchase.setChildNum(childNum);
            });
        }
        super.handlerResult(page);
    }

    @ApiOperation("拿货单统计")
    @PostMapping("/sum")
    public R<BasePurchaseSumResultVO> querySum(@RequestBody BasePurchasePageQuery query) {
        return R.success(basePurchaseService.querySum(query));
    }

}


