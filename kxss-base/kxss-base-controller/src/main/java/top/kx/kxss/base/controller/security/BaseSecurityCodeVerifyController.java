package top.kx.kxss.base.controller.security;

import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.base.controller.SuperController;
import top.kx.kxss.base.service.BaseSecurityCodeVerifyService;
import top.kx.kxss.base.entity.BaseSecurityCodeVerify;
import top.kx.kxss.base.vo.save.BaseSecurityCodeVerifySaveVO;
import top.kx.kxss.base.vo.update.BaseSecurityCodeVerifyUpdateVO;
import top.kx.kxss.base.vo.result.BaseSecurityCodeVerifyResultVO;
import top.kx.kxss.base.vo.query.BaseSecurityCodeVerifyPageQuery;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 前端控制器
 * 防伪验证信息
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-06 16:18:00
 * @create [2025-06-06 16:18:00] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/baseSecurityCodeVerify")
@Api(value = "BaseSecurityCodeVerify", tags = "防伪验证信息")
public class BaseSecurityCodeVerifyController extends SuperController<BaseSecurityCodeVerifyService, Long, BaseSecurityCodeVerify, BaseSecurityCodeVerifySaveVO,
    BaseSecurityCodeVerifyUpdateVO, BaseSecurityCodeVerifyPageQuery, BaseSecurityCodeVerifyResultVO> {
    private final EchoService echoService;
    @Override
    public EchoService getEchoService() {
        return echoService;
    }

}


