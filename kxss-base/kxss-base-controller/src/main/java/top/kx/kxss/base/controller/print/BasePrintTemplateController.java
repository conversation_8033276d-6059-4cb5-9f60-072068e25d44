package top.kx.kxss.base.controller.print;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.kxss.base.entity.print.BasePrintTemplate;
import top.kx.kxss.base.service.print.BasePrintTemplateService;
import top.kx.kxss.base.vo.query.print.BasePrintTemplatePageQuery;
import top.kx.kxss.base.vo.result.print.BasePrintTemplateResultVO;
import top.kx.kxss.base.vo.save.print.BasePrintTemplateSaveVO;
import top.kx.kxss.base.vo.update.print.BasePrintTemplateUpdateVO;

import java.util.List;

/**
 * <p>
 * 前端控制器
 * 打印模板
 * </p>
 *
 * <AUTHOR>
 * @date 2025-08-27 10:48:00
 * @create [2025-08-27 10:48:00] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/basePrintTemplate")
@Api(value = "BasePrintTemplate", tags = "打印模板")
public class BasePrintTemplateController extends SuperController<BasePrintTemplateService, Long, BasePrintTemplate, BasePrintTemplateSaveVO,
    BasePrintTemplateUpdateVO, BasePrintTemplatePageQuery, BasePrintTemplateResultVO> {
    private final EchoService echoService;
    @Override
    public EchoService getEchoService() {
        return echoService;
    }

    @ApiOperation(value = "根据券码查询信息")
    @GetMapping("/getListByProductId")
    @WebLog("根据券码查询信息")
    public R<List<BasePrintTemplateResultVO>> getListByProductId(@RequestParam Long productId) {
        return R.success(superService.getListByProductId(productId));
    }


}


