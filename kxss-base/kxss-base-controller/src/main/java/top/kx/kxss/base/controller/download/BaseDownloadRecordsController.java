package top.kx.kxss.base.controller.download;

import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.base.controller.SuperController;
import top.kx.kxss.base.service.download.BaseDownloadRecordsService;
import top.kx.kxss.base.entity.download.BaseDownloadRecords;
import top.kx.kxss.base.vo.save.download.BaseDownloadRecordsSaveVO;
import top.kx.kxss.base.vo.update.download.BaseDownloadRecordsUpdateVO;
import top.kx.kxss.base.vo.result.download.BaseDownloadRecordsResultVO;
import top.kx.kxss.base.vo.query.download.BaseDownloadRecordsPageQuery;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 前端控制器
 * 下载记录
 * </p>
 *
 * <AUTHOR>
 * @date 2025-04-25 17:45:12
 * @create [2025-04-25 17:45:12] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/baseDownloadRecords")
@Api(value = "BaseDownloadRecords", tags = "下载记录")
public class BaseDownloadRecordsController extends SuperController<BaseDownloadRecordsService, Long, BaseDownloadRecords, BaseDownloadRecordsSaveVO,
    BaseDownloadRecordsUpdateVO, BaseDownloadRecordsPageQuery, BaseDownloadRecordsResultVO> {
    private final EchoService echoService;
    @Override
    public EchoService getEchoService() {
        return echoService;
    }

}


