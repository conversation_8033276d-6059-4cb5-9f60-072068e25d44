package top.kx.kxss.base.controller.security;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.io.*;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 前端控制器
 * 防伪信息
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-25 13:40:18
 * @create [2023-05-25 13:40:18] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
public class GenSecurityCodeController {


    // 产生要储存的集合

    /**
     * 生成防伪码
     * @param j
     * @return
     */
    protected static List<String> getUsers(int j) {
        List<String> users = new ArrayList<>();
        for (int i = 0; i < 20000; i++) {
            String str = IdUtil.getSnowflake().nextIdStr();
            int random6 = (int) ((Math.random() * 9 + 1) * 100000);
            String substring = str.substring(str.length() - 10, str.length());
            substring = substring.concat(random6 + "");
            substring = substring.substring(substring.length() - 12, substring.length());
            String pre = "071425".concat(j + "");
            if (!users.contains(pre.concat(substring))) {
                users.add(pre.concat(substring));
            }
        }
        return users;
    }

    /**
     * 生成序列号
     * @param j
     * @return
     */
    protected static List<String> getCode(int j) {
        List<String> users = new ArrayList<>();
        for (int i = 0; i < 50000; i++) {
            users.add(getCodeStr(users, i + 1, j + 1));
        }
        return users;
    }

    protected static String getCodeStr(List<String> users, int i, int j) {
        int digit = 6;
//        int num = (int) ((Math.random() * 9 + 1) * 100000);
        int num = i;
        String format = String.format("%0" + digit + "d", num);
        String concat = "250714".concat(j + "").concat(format);
        if (users.contains(concat)) {
            return getCodeStr(users, i+1, j);
        }
        return concat;
    }

    public static void main(String[] args) {
        for (int j = 0; j < 1; j++) {
            try {
                String filePath = "/Users/<USER>/Desktop/" + j + "_0714.txt";
                FileWriter fw = null;
                File file = new File(filePath);
                if (!file.exists()) {
                    file.createNewFile();
                }
                fw = new FileWriter(filePath);
                BufferedWriter bw = new BufferedWriter(fw);
                //第一步，创建一个workbook对应一个excel文件
                HSSFWorkbook workbook = new HSSFWorkbook();
                //第二部，在workbook中创建一个sheet对应excel中的sheet
                HSSFSheet sheet = workbook.createSheet("sheet1");
                //第三部，在sheet表中添加表头第0行，老版本的poi对sheet的行列有限制
                HSSFRow row = sheet.createRow(0);
                //第四步，创建单元格，设置表头
                HSSFCell cell = row.createCell(0);
                cell.setCellValue("防伪号");
                HSSFCell cell1 = row.createCell(1);
                cell1.setCellValue("防伪号");
                HSSFCell cell2 = row.createCell(2);
                cell2.setCellValue("链接");

                //第五步，写入实体数据，实际应用中这些数据从数据库得到,对象封装数据，集合包对象。对象的属性值对应表的每行的值
                List<String> users = getUsers(j + 1);
                List<String> code = getCode(j);
                System.out.println("数据：" + JSON.toJSONString(users));
                for (int i = 0; i < users.size(); i++) {
                    HSSFRow row1 = sheet.createRow(i + 1);
                    String user = users.get(i);
                    //创建单元格设值
                    row1.createCell(0).setCellValue(code.get(i));
                    row1.createCell(1).setCellValue(user);
                    row1.createCell(2).setCellValue("https://fw.kxss147.com/fw/?c=" + user);
                    bw.write(code.get(i).concat(",").concat(user).concat(",")
                            .concat("https://fw.kxss147.com/fw/?c=" + user).concat("\n"));
                }

                //将文件保存到指定的位置
                bw.close();
                FileOutputStream fos = new FileOutputStream("/Users/<USER>/Desktop/" + (j + 1) + "-0714.xls");
                workbook.write(fos);
                System.out.println("写入成功");
                fos.close();

            } catch (IOException e) {
                e.printStackTrace();
            }
        }

    }

}


