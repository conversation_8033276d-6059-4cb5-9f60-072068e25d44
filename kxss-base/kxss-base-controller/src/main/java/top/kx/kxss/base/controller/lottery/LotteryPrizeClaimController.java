package top.kx.kxss.base.controller.lottery;

import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.base.controller.SuperController;
import top.kx.kxss.base.service.lottery.LotteryPrizeClaimService;
import top.kx.kxss.base.entity.lottery.LotteryPrizeClaim;
import top.kx.kxss.base.vo.save.lottery.LotteryPrizeClaimSaveVO;
import top.kx.kxss.base.vo.update.lottery.LotteryPrizeClaimUpdateVO;
import top.kx.kxss.base.vo.result.lottery.LotteryPrizeClaimResultVO;
import top.kx.kxss.base.vo.query.lottery.LotteryPrizeClaimPageQuery;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 前端控制器
 * 奖品领取表(含收货地址快照+物流信息)
 * </p>
 *
 * <AUTHOR>
 * @date 2025-09-15 17:23:11
 * @create [2025-09-15 17:23:11] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/lotteryPrizeClaim")
@Api(value = "LotteryPrizeClaim", tags = "奖品领取表(含收货地址快照+物流信息)")
public class LotteryPrizeClaimController extends SuperController<LotteryPrizeClaimService, Long, LotteryPrizeClaim, LotteryPrizeClaimSaveVO,
    LotteryPrizeClaimUpdateVO, LotteryPrizeClaimPageQuery, LotteryPrizeClaimResultVO> {
    private final EchoService echoService;
    @Override
    public EchoService getEchoService() {
        return echoService;
    }

}


