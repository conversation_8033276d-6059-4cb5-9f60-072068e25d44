package top.kx.kxss.msg.controller;

import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.kxss.msg.entity.ExtendInterfaceLogging;
import top.kx.kxss.msg.service.ExtendInterfaceLoggingService;
import top.kx.kxss.msg.vo.query.ExtendInterfaceLoggingPageQuery;
import top.kx.kxss.msg.vo.result.ExtendInterfaceLoggingResultVO;
import top.kx.kxss.msg.vo.save.ExtendInterfaceLoggingSaveVO;
import top.kx.kxss.msg.vo.update.ExtendInterfaceLoggingUpdateVO;

/**
 * <p>
 * 前端控制器
 * 接口执行日志记录
 * </p>
 *
 * <AUTHOR>
 * @date 2022-07-09 23:58:59
 * @create [2022-07-09 23:58:59] [zuihou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/extendInterfaceLogging")
@Api(value = "ExtendInterfaceLogging", tags = "接口执行日志记录")
public class ExtendInterfaceLoggingController extends SuperController<ExtendInterfaceLoggingService, Long, ExtendInterfaceLogging, ExtendInterfaceLoggingSaveVO,
        ExtendInterfaceLoggingUpdateVO, ExtendInterfaceLoggingPageQuery, ExtendInterfaceLoggingResultVO> {
    private final EchoService echoService;

    @Override
    public EchoService getEchoService() {
        return echoService;
    }

}


