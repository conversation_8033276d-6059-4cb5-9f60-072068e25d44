package top.kx.kxss.base.controller.product;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.kxss.base.entity.product.BaseProduct;
import top.kx.kxss.base.entity.product.BaseProductAttribute;
import top.kx.kxss.base.service.product.BaseProductAttributeService;
import top.kx.kxss.base.service.product.BaseProductService;
import top.kx.kxss.base.vo.query.product.BaseProductAttributeQuery;
import top.kx.kxss.base.vo.query.product.BaseProductPageQuery;
import top.kx.kxss.base.vo.result.product.BaseProductResultVO;
import top.kx.kxss.base.vo.save.product.BaseProductSaveVO;
import top.kx.kxss.base.vo.update.product.BaseProductUpdateVO;
import top.kx.kxss.file.entity.File;
import top.kx.kxss.file.service.FileService;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 前端控制器
 * 商品表
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-25 13:52:40
 * @create [2023-05-25 13:52:40] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/baseProduct")
@Api(value = "BaseProduct", tags = "商品表")
public class BaseProductController extends SuperController<BaseProductService, Long, BaseProduct, BaseProductSaveVO,
        BaseProductUpdateVO, BaseProductPageQuery, BaseProductResultVO> {
    private final EchoService echoService;
    private final BaseProductAttributeService baseProductAttributeService;

    @Override
    public EchoService getEchoService() {
        return echoService;
    }

    @Resource
    private FileService fileService;


    @Override
    public void handlerResult(IPage<BaseProductResultVO> page) {
        List<BaseProductResultVO> records = page.getRecords();
        //文件信息
        List<Long> imageIds = records.stream().map(BaseProductResultVO::getProductImage).collect(Collectors.toList());
        Map<Long, File> fileMap = CollectionUtil.isNotEmpty(imageIds) ? fileService.list(Wraps.<File>lbQ().in(File::getId, imageIds))
                .stream().collect(Collectors.toMap(File::getId, k -> k)) : new HashMap<>();
        List<Long> productIds = records.stream().map(BaseProductResultVO::getId).distinct().collect(Collectors.toList());
        Map<Long, List<Long>> productAttributeMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(productIds)) {
            List<BaseProductAttribute> productAttributeList = baseProductAttributeService.list(Wraps.<BaseProductAttribute>lbQ().in(BaseProductAttribute::getProductId, productIds));
            productAttributeMap = productAttributeList.stream().collect(Collectors.groupingBy(BaseProductAttribute::getProductId, Collectors.mapping(BaseProductAttribute::getAttributeId, Collectors.toList())));
        }

        for (BaseProductResultVO record : records) {
            Long image = record.getProductImage();
            if (ObjectUtil.isNotNull(image)) {
                record.setProductImageFile(fileMap.get(image));
            }
            record.setAttributeIds(productAttributeMap.get(record.getId()));
        }
        super.handlerResult(page);
    }


    /**
     * 修改状态
     *
     * @param id    用户id
     * @param state 用户状态
     * @return 是否成功
     */
    @ApiOperation(value = "修改状态", notes = "修改状态")
    @PutMapping("/updateState")
    @WebLog("'修改状态:id=' + #id + ', state=' + #state")
    public R<Boolean> updateState(
            @NotNull(message = "请选择用户") @RequestParam Long id,
            @NotNull(message = "请设置正确的状态值") @RequestParam Boolean state) {
        return success(superService.updateState(id, state));
    }


    /**
     * 关联属性
     */
    @ApiOperation(value = "商品关联属性", notes = "商品关联属性")
    @PostMapping("/relationAttribute")
    public R<Boolean> relationAttribute(@RequestBody @Validated BaseProductAttributeQuery query) {
        return success(superService.relationAttribute(query));
    }


}


