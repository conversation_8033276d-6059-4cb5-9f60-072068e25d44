package top.kx.kxss.base.controller.security;

import cn.hutool.core.util.IdUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.io.*;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 前端控制器
 * 防伪信息
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-25 13:40:18
 * @create [2023-05-25 13:40:18] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
public class GenChildSecurityCodeController {


    //大码数据
    protected static List<String> getBigCode(String prefix, int totalNum) {
        List<String> data = new ArrayList<>();
        for (int i = 1; i <= totalNum; i++) {
            data.add(prefix.concat(String.format("%08d", i)));
        }
        return data;
    }

    //小码防伪码
    protected static List<String> getSmallCode(int totalNum, int j, String prefix) {
        List<String> users = new ArrayList<>();
        for (int i = 0; i < totalNum; i++) {
            String str = IdUtil.getSnowflake().nextIdStr();
            int random6 = (int) ((Math.random() * 9 + 1) * 100000);
            String substring = str.substring(str.length() - 10, str.length());
            substring = substring.concat(random6 + "");
            substring = substring.substring(substring.length() - 12, substring.length());
            String pre = prefix.concat(j + "");
            if (!users.contains(pre.concat(substring))) {
                users.add(pre.concat(substring));
            }
        }
        return users;
    }


    // 序列号
    protected static List<String> getCode(int totalNum, int j, String prefix) {
        List<String> users = new ArrayList<>();
        for (int i = 0; i < totalNum; i++) {
            users.add(getCodeStr(users, i, j, prefix));
        }
        return users;
    }

    protected static String getCodeStr(List<String> users, int i, int j, String prefix) {
        int digit = 6;
//        int num = (int) ((Math.random() * 9 + 1) * 100000);
        int num = i + 1;
        String format = String.format("%0" + digit + "d", num);
        String concat = prefix.concat(j + "").concat(format);
        if (users.contains(concat)) {
            return getCodeStr(users, i, j, prefix);
        }
        return concat;
    }

    public static void main(String[] args) {
        // 总套数
        int setNum = 1000;
        //每套子码数量
        int childNum = 50;
        // 每个Excel文件最多包含的数据量
        int maxDataPerFile = 50000;
        //总数量
        int totalNum = setNum * childNum;
        //每个表格最多有多少套
        int maxPerFileSetNum = maxDataPerFile / childNum;
        int excelNum = 1;
        //计算需要多少个excel 每个表格最多5万数据
        if (setNum > maxPerFileSetNum) {
            excelNum = setNum % maxPerFileSetNum == 0 ? setNum / maxPerFileSetNum : (setNum / maxPerFileSetNum) + 1;
        }
        //母码前缀
        String bigCodePrefix = "kxp20250607";
        //子码前缀
        String smallCodePrefix = "060725";
        //序列号前缀
        String codePrefix = "250607";
        //母码的序列号递增 kap
        List<String> bigCodeList = getBigCode(bigCodePrefix, setNum);
        System.out.println("套码数量：" + bigCodeList.size());
        for (int j = 0; j < excelNum; j++) {
            try {
                String filePath = "/Users/<USER>/Desktop/" + j + "_" + codePrefix + ".txt";
                FileWriter fw = null;
                File file = new File(filePath);
                if (!file.exists()) {
                    file.createNewFile();
                }
                fw = new FileWriter(filePath);
                BufferedWriter bw = new BufferedWriter(fw);
                //第一步，创建一个workbook对应一个excel文件
                HSSFWorkbook workbook = new HSSFWorkbook();
                //第二部，在workbook中创建一个sheet对应excel中的sheet
                HSSFSheet sheet = workbook.createSheet("sheet1");
                //第三部，在sheet表中添加表头第0行，老版本的poi对sheet的行列有限制
                HSSFRow row = sheet.createRow(0);
                //第四步，创建单元格，设置表头
                HSSFCell cell = row.createCell(0);
                cell.setCellValue("序列号");
                HSSFCell cell1 = row.createCell(1);
                cell1.setCellValue("防伪码");
                HSSFCell cell2 = row.createCell(2);
                cell2.setCellValue("防伪链接");
                HSSFCell cell3 = row.createCell(3);
                cell3.setCellValue("盒码");
                HSSFCell cell4 = row.createCell(4);
                cell4.setCellValue("盒码链接");

                //第五步，写入实体数据，实际应用中这些数据从数据库得到,对象封装数据，集合包对象。对象的属性值对应表的每行的值

                int currFileSetNum = (j + 1) * maxPerFileSetNum;
                if (currFileSetNum > setNum) {
                    currFileSetNum = setNum % maxPerFileSetNum;
                }
                //小码
                int i1 = (j + 1 == excelNum) ? currFileSetNum * childNum : maxPerFileSetNum * childNum;
                List<String> smallCodeList = getSmallCode(i1, j + 1, smallCodePrefix);
                //序列号
                List<String> codeList = getCode(i1, j + 1, codePrefix);
                System.out.println("序列号数量：" + codeList.size());
                System.out.println("防伪码数量：" + smallCodeList.size());
                for (int i = 0; i < codeList.size(); i++) {
                    int currSetNum = j * maxPerFileSetNum;
                    int index = (i + 1);
                    if (index <= childNum) {
                        currSetNum = currSetNum + 1;
                    } else {
                        currSetNum = currSetNum + (index % childNum == 0 ? index / childNum : (index / childNum) + 1);
                    }
                    HSSFRow row1 = sheet.createRow(i + 1);
                    String smallCode = smallCodeList.get(i);
                    String bigCode = bigCodeList.get(currSetNum - 1);
                    //创建单元格设值
                    row1.createCell(0).setCellValue(codeList.get(i));
                    row1.createCell(1).setCellValue(smallCode);
                    row1.createCell(2).setCellValue("https://fw.kxss147.com/fw/?c=" + smallCode);
                    row1.createCell(3).setCellValue(bigCode);
                    row1.createCell(4).setCellValue("https://fw.kxss147.com/fw/?d=" + bigCode);
                    bw.write(codeList.get(i).concat(",")
                            .concat(smallCode).concat(",")
                            .concat("https://fw.kxss147.com/fw/?c=" + smallCode).concat(",")
                            .concat(bigCode).concat(",")
                            .concat("https://fw.kxss147.com/fw/?d=" + bigCode).concat("\n"));
                }

                //将文件保存到指定的位置
                bw.close();
                FileOutputStream fos = new FileOutputStream("/Users/<USER>/Desktop/" + (j + 1) + "-" + codePrefix + ".xls");
                workbook.write(fos);
                System.out.println("写入成功");
                fos.close();

            } catch (IOException e) {
                e.printStackTrace();
            }
        }

    }

}


