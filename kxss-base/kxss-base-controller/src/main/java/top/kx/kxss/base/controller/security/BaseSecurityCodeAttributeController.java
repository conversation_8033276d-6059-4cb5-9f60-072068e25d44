package top.kx.kxss.base.controller.security;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.base.entity.SuperEntity;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.QueryWrap;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.base.entity.BaseSecurityCodeAttribute;
import top.kx.kxss.base.entity.BaseSecurityCodeAttributeDetails;
import top.kx.kxss.base.entity.attribute.BaseAttribute;
import top.kx.kxss.base.service.BaseSecurityCodeAttributeDetailsService;
import top.kx.kxss.base.service.BaseSecurityCodeAttributeService;
import top.kx.kxss.base.service.attribute.BaseAttributeService;
import top.kx.kxss.base.vo.query.BaseSecurityCodeAttributePageQuery;
import top.kx.kxss.base.vo.result.BaseSecurityCodeAttributeDetailsResultVO;
import top.kx.kxss.base.vo.result.BaseSecurityCodeAttributeResultVO;
import top.kx.kxss.base.vo.save.BaseSecurityCodeAddAttributeVO;
import top.kx.kxss.base.vo.save.BaseSecurityCodeAttributeSaveVO;
import top.kx.kxss.base.vo.update.BaseSecurityCodeAttributeUpdateVO;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 前端控制器
 * 防伪信息规格属性
 * </p>
 *
 * <AUTHOR>
 * @date 2025-02-26 16:17:06
 * @create [2025-02-26 16:17:06] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/baseSecurityCodeAttribute")
@Api(value = "BaseSecurityCodeAttribute", tags = "防伪信息规格属性")
public class BaseSecurityCodeAttributeController extends SuperController<BaseSecurityCodeAttributeService, Long, BaseSecurityCodeAttribute, BaseSecurityCodeAttributeSaveVO,
        BaseSecurityCodeAttributeUpdateVO, BaseSecurityCodeAttributePageQuery, BaseSecurityCodeAttributeResultVO> {
    private final EchoService echoService;
    private final BaseSecurityCodeAttributeDetailsService baseSecurityCodeAttributeDetailsService;
    private final BaseAttributeService baseAttributeService;

    @Override
    public EchoService getEchoService() {
        return echoService;
    }


    /**
     * 添加属性
     */
    @ApiOperation(value = "添加属性")
    @PostMapping("/addAttribute")
    public R<BaseSecurityCodeAttributeResultVO> addAttribute(@RequestBody @Validated BaseSecurityCodeAddAttributeVO saveVO) {
        return R.success(superService.saveAttribute(saveVO));
    }

    /**
     * 修改属性
     */
    @ApiOperation(value = "修改属性")
    @PostMapping("/updateAttribute")
    public R<BaseSecurityCodeAttributeResultVO> updateAttribute(@RequestBody @Validated BaseSecurityCodeAddAttributeVO saveVO) {
        return R.success(superService.updateAttribute(saveVO));
    }

    @Override
    public R<BaseSecurityCodeAttributeResultVO> getDetail(Long aLong) {
        ;
        return R.success(superService.getDetail(aLong));
    }


    @Override
    public QueryWrap<BaseSecurityCodeAttribute> handlerWrapper(BaseSecurityCodeAttribute model, PageParams<BaseSecurityCodeAttributePageQuery> params) {
        QueryWrap<BaseSecurityCodeAttribute> queryWrap = super.handlerWrapper(model, params);
        BaseSecurityCodeAttributePageQuery paramsModel = params.getModel();
        if (StringUtils.isNotBlank(paramsModel.getKeyword())) {
            queryWrap.and(e ->
                    e.lambda().eq(BaseSecurityCodeAttribute::getSecurityCode, paramsModel.getKeyword())
                            .or()
                            .inSql(BaseSecurityCodeAttribute::getProductId, "select distinct bp.id from base_product bp where bp.delete_flag = 0 and instr(bp.name, '" + paramsModel.getKeyword() + "')")
//                            .or()
//                            .inSql(BaseSecurityCodeAttribute::getId, "select distinct bscad.code_attribute_id " +
//                                    " from base_security_code_attribute_details bscad " +
//                                    "         left join base_attribute ba on bscad.attribute_id = ba.id " +
//                                    " where bscad.delete_flag = 0 and ba.delete_flag = 0 " +
//                                    "  and instr(ba.name, '" + paramsModel.getKeyword() + "')")
            );
        }
        if (Objects.nonNull(paramsModel.getStartTime()) && Objects.nonNull(paramsModel.getEndTime())) {
            if (StringUtils.equals(paramsModel.getType(), "1")) {
                queryWrap.lambda().between(SuperEntity::getCreatedTime, paramsModel.getStartTime(), paramsModel.getEndTime());
            }
            if (StringUtils.equals(paramsModel.getType(), "2")) {
                queryWrap.lambda().between(BaseSecurityCodeAttribute::getUpdatedTime, paramsModel.getStartTime(), paramsModel.getEndTime());
            }

        }

        return queryWrap;
    }

    @Override
    public void handlerResult(IPage<BaseSecurityCodeAttributeResultVO> page) {
        if (CollUtil.isNotEmpty(page.getRecords())) {
            List<Long> ids = page.getRecords().stream().map(BaseSecurityCodeAttributeResultVO::getId).collect(Collectors.toList());
            List<BaseSecurityCodeAttributeDetails> attributeDetailsList = baseSecurityCodeAttributeDetailsService.list(Wraps.<BaseSecurityCodeAttributeDetails>lbQ()
                    .in(BaseSecurityCodeAttributeDetails::getCodeAttributeId, ids));
            if (CollUtil.isNotEmpty(attributeDetailsList)) {
                List<Long> attributeIds = attributeDetailsList.stream().map(BaseSecurityCodeAttributeDetails::getAttributeId).distinct().collect(Collectors.toList());
                Map<Long, List<BaseSecurityCodeAttributeDetails>> attributeDetailsMap = attributeDetailsList.stream().collect(Collectors.groupingBy(BaseSecurityCodeAttributeDetails::getCodeAttributeId));
                List<BaseAttribute> baseAttributeList = baseAttributeService.listByIds(attributeIds);
                Map<Long, BaseAttribute> attributeMap = baseAttributeList.stream().collect(Collectors.toMap(BaseAttribute::getId, item -> item));
                page.getRecords().forEach(item -> {
                    List<BaseSecurityCodeAttributeDetails> securityCodeAttributeDetailsList = attributeDetailsMap.get(item.getId());
                    if (CollUtil.isNotEmpty(securityCodeAttributeDetailsList)) {
                        List<BaseSecurityCodeAttributeDetailsResultVO> detailsResultVOList = BeanPlusUtil.toBeanList(securityCodeAttributeDetailsList, BaseSecurityCodeAttributeDetailsResultVO.class);
                        detailsResultVOList.forEach(s -> {
                            BaseAttribute baseAttribute = attributeMap.get(s.getAttributeId());
                            if (Objects.nonNull(baseAttribute)) {
                                s.setAttributeName(baseAttribute.getName());
                                s.setAttributeUnit(baseAttribute.getUnit());
                                s.setSortValue(baseAttribute.getSortValue());
                            }
                        });
                        detailsResultVOList.sort((o1, o2) -> o2.getSortValue().compareTo(o1.getSortValue()));
                        item.setAttributeDetailsResultVOList(detailsResultVOList);
                    }
                });
            }
        }
        super.handlerResult(page);
    }

    @Override
    public R<Boolean> delete(List<Long> longs) {
        return super.delete(longs);
    }
}


