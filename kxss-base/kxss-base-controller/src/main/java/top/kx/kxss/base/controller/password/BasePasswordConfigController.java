package top.kx.kxss.base.controller.password;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.kxss.base.entity.password.BasePasswordConfig;
import top.kx.kxss.base.service.password.BasePasswordConfigService;
import top.kx.kxss.base.vo.query.password.BasePasswordConfigPageQuery;
import top.kx.kxss.base.vo.result.password.BasePasswordConfigResultVO;
import top.kx.kxss.base.vo.save.password.BasePasswordConfigSaveVO;
import top.kx.kxss.base.vo.update.password.BasePasswordConfigUpdateVO;
import top.kx.kxss.base.vo.update.password.BasePasswordUpdateVO;

/**
 * <p>
 * 前端控制器
 * 密码验证配置表
 * </p>
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/basePasswordConfig")
@Api(value = "BasePasswordConfig", tags = "密码验证配置")
public class BasePasswordConfigController extends SuperController<BasePasswordConfigService, Long, BasePasswordConfig, BasePasswordConfigSaveVO,
    BasePasswordConfigUpdateVO, BasePasswordConfigPageQuery, BasePasswordConfigResultVO> {

    private final EchoService echoService;

    @Override
    public EchoService getEchoService() {
        return echoService;
    }

    /**
     * 修改密码
     * @param
     * @return
     */
    @ApiOperation(value = "修改密码")
    @PostMapping("/updatePassword")
    @WebLog("修改密码")
    public R<Boolean> updatePassword(@RequestBody @Validated BasePasswordUpdateVO updateVO) {
        return R.success(superService.updatePassword(updateVO));
    }

    @ApiOperation(value = "根据业务代码检查是否需要密码验证")
    @GetMapping("/checkNeedValidation")
    @WebLog("检查是否需要密码验证")
    public R<Boolean> checkNeedValidation(@RequestParam String businessCode) {
        boolean needValidation = superService.needPasswordValidation(businessCode);
        return R.success(needValidation);
    }

    @ApiOperation(value = "验证密码")
    @PostMapping("/validatePassword")
    @WebLog("验证密码")
    public R<Boolean> validatePassword(@RequestParam String businessCode, @RequestParam String password) {
        boolean isValid = superService.validatePassword(businessCode, password);
        return R.success(isValid);
    }
}