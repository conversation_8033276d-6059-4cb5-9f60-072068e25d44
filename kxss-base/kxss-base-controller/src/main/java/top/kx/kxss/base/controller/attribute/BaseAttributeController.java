package top.kx.kxss.base.controller.attribute;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.base.entity.attribute.BaseAttribute;
import top.kx.kxss.base.entity.attribute.BaseAttributeValue;
import top.kx.kxss.base.service.attribute.BaseAttributeService;
import top.kx.kxss.base.service.attribute.BaseAttributeValueService;
import top.kx.kxss.base.vo.query.attribute.BaseAttributePageQuery;
import top.kx.kxss.base.vo.result.attribute.BaseAttributeResultVO;
import top.kx.kxss.base.vo.save.attribute.BaseAttributeSaveVO;
import top.kx.kxss.base.vo.update.attribute.BaseAttributeUpdateVO;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 前端控制器
 * 商品基础属性
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-16 18:44:16
 * @create [2025-06-16 18:44:16] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/baseAttribute")
@Api(value = "BaseAttribute", tags = "商品基础属性")
public class BaseAttributeController extends SuperController<BaseAttributeService, Long, BaseAttribute, BaseAttributeSaveVO,
    BaseAttributeUpdateVO, BaseAttributePageQuery, BaseAttributeResultVO> {
    private final EchoService echoService;
    private final BaseAttributeValueService baseAttributeValueService;
    @Override
    public EchoService getEchoService() {
        return echoService;
    }


    @Override
    public R<BaseAttributeResultVO> getDetail(Long aLong) {
        BaseAttribute attribute = superService.getById(aLong);
        BaseAttributeResultVO resultVO = BeanPlusUtil.toBean(attribute, BaseAttributeResultVO.class);
        if (Objects.nonNull(attribute)) {
            List<BaseAttributeValue> attributeValueList = baseAttributeValueService.list(Wraps.<BaseAttributeValue>lbQ().eq(BaseAttributeValue::getAttributeId, aLong));
            if (CollUtil.isNotEmpty(attributeValueList)) {
                resultVO.setValueList(attributeValueList.stream().map(BaseAttributeValue::getValue).collect(Collectors.toList()));
            }
        }
        echoService.action(resultVO);
        return R.success(resultVO);
    }


    @Override
    public void handlerResult(IPage<BaseAttributeResultVO> page) {
        List<BaseAttributeResultVO> records = page.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            List<Long> attributeIds = records.stream().map(BaseAttributeResultVO::getId).collect(Collectors.toList());
            List<BaseAttributeValue> attributeValueList = baseAttributeValueService.list(Wraps.<BaseAttributeValue>lbQ().in(BaseAttributeValue::getAttributeId, attributeIds));
            Map<Long, List<String>> attributeValueMap = attributeValueList.stream().collect(Collectors.groupingBy(BaseAttributeValue::getAttributeId, Collectors.mapping(BaseAttributeValue::getValue, Collectors.toList())));
            records.forEach(item -> {
                List<String> valueList = attributeValueMap.get(item.getId());
                if (CollUtil.isNotEmpty(valueList)) {
                    item.setValueList(valueList);
                }
            });
        }
        super.handlerResult(page);
    }
}


