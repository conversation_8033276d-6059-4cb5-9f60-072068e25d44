package top.kx.kxss.base.controller.biz;

import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.base.controller.SuperController;
import top.kx.kxss.base.service.biz.BaseBizLogService;
import top.kx.kxss.base.entity.biz.BaseBizLog;
import top.kx.kxss.base.vo.save.biz.BaseBizLogSaveVO;
import top.kx.kxss.base.vo.update.biz.BaseBizLogUpdateVO;
import top.kx.kxss.base.vo.result.biz.BaseBizLogResultVO;
import top.kx.kxss.base.vo.query.biz.BaseBizLogPageQuery;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 前端控制器
 * 防伪码操作日志
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-29 14:02:40
 * @create [2023-05-29 14:02:40] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/baseBizLog")
@Api(value = "BaseBizLog", tags = "防伪码操作日志")
public class BaseBizLogController extends SuperController<BaseBizLogService, Long, BaseBizLog, BaseBizLogSaveVO,
    BaseBizLogUpdateVO, BaseBizLogPageQuery, BaseBizLogResultVO> {
    private final EchoService echoService;
    @Override
    public EchoService getEchoService() {
        return echoService;
    }

}


