package top.kx.kxss.base.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.kxss.base.service.pact.BasePactService;
import top.kx.kxss.base.vo.result.pact.PactResultVO;
import top.kx.kxss.model.enumeration.base.PactTypeEnum;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 前端控制器
 * 协议配置
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-25 17:21:04
 * @create [2023-05-25 17:21:04] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/appAnyone")
@Api(value = "AppAnyone", tags = "用户端无需验证权限")
public class AppAnyoneController {

    @Resource
    private BasePactService pactService;

    /**
     * 查询登录协议
     */
    @ApiOperation(value = "查询登录协议", notes = "查询登录协议")
    @GetMapping("/list")
    @WebLog("查询登录协议")
    public R<List<PactResultVO>> list() {
        return R.success(pactService.getListByType(PactTypeEnum.LOGIN.getCode()));
    }


}


