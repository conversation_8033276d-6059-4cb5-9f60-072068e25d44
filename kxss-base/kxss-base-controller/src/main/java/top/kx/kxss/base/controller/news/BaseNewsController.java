package top.kx.kxss.base.controller.news;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.kxss.base.entity.news.BaseNews;
import top.kx.kxss.base.service.news.BaseNewsService;
import top.kx.kxss.base.vo.query.news.BaseNewsPageQuery;
import top.kx.kxss.base.vo.result.news.BaseNewsResultVO;
import top.kx.kxss.base.vo.save.news.BaseNewsSaveVO;
import top.kx.kxss.base.vo.update.news.BaseNewsUpdateVO;
import top.kx.kxss.file.entity.File;
import top.kx.kxss.file.service.FileService;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 前端控制器
 * 新闻资讯
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-25 16:12:08
 * @create [2023-05-25 16:12:08] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/baseNews")
@Api(value = "BaseNews", tags = "新闻资讯")
public class BaseNewsController extends SuperController<BaseNewsService, Long, BaseNews, BaseNewsSaveVO,
        BaseNewsUpdateVO, BaseNewsPageQuery, BaseNewsResultVO> {
    private final EchoService echoService;

    @Override
    public EchoService getEchoService() {
        return echoService;
    }

    @Resource
    private FileService fileService;

    @Override
    public void handlerResult(IPage<BaseNewsResultVO> page) {
        List<BaseNewsResultVO> records = page.getRecords();
        //文件信息
        List<Long> imageIds = records.stream().map(BaseNewsResultVO::getCoverImage).collect(Collectors.toList());
        Map<Long, File> fileMap = CollectionUtil.isNotEmpty(imageIds) ? fileService.list(Wraps.<File>lbQ().in(File::getId, imageIds))
                .stream().collect(Collectors.toMap(File::getId, k -> k)) : new HashMap<>();
        for (BaseNewsResultVO record : records) {
            Long image = record.getCoverImage();
            if (ObjectUtil.isNotNull(image)) {
                record.setCoverImageFile(fileMap.get(image));
            }
        }
        super.handlerResult(page);
    }

    @Override
    public R<BaseNews> handlerSave(BaseNewsSaveVO model) {
        model.setViewCount(0);
        return super.handlerSave(model);
    }
    /**
     * 新闻资讯撤销
     *
     * @param id    id
     * @return 是否成功
     */
    @ApiOperation(value = "新闻资讯撤销", notes = "新闻资讯撤销")
    @PostMapping("/revoke")
    @WebLog("新闻资讯撤销")
    public R<Boolean> revoke(
            @NotNull(message = "请选择id") @RequestParam Long id) {
        return success(superService.revoke(id));
    }
}


