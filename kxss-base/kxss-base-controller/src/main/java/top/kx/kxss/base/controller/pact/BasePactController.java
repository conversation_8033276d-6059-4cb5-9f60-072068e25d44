package top.kx.kxss.base.controller.pact;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.base.controller.SuperController;
import top.kx.kxss.base.service.pact.BasePactService;
import top.kx.kxss.base.entity.pact.BasePact;
import top.kx.kxss.base.vo.save.pact.BasePactSaveVO;
import top.kx.kxss.base.vo.update.pact.BasePactUpdateVO;
import top.kx.kxss.base.vo.result.pact.BasePactResultVO;
import top.kx.kxss.base.vo.query.pact.BasePactPageQuery;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 前端控制器
 * 协议配置
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-25 17:21:04
 * @create [2023-05-25 17:21:04] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/basePact")
@Api(value = "BasePact", tags = "协议配置")
public class BasePactController extends SuperController<BasePactService, Long, BasePact, BasePactSaveVO,
    BasePactUpdateVO, BasePactPageQuery, BasePactResultVO> {
    private final EchoService echoService;
    @Override
    public EchoService getEchoService() {
        return echoService;
    }

    /**
     * 修改状态
     *
     * @param id    用户id
     * @param state 用户状态
     * @return 是否成功
     */
    @ApiOperation(value = "修改状态", notes = "修改状态")
    @PutMapping("/updateState")
    @WebLog("'修改状态:id=' + #id + ', state=' + #state")
    public R<Boolean> updateState(
            @NotNull(message = "请选择用户") @RequestParam Long id,
            @NotNull(message = "请设置正确的状态值") @RequestParam Boolean state) {
        return success(superService.updateState(id, state));
    }


}


