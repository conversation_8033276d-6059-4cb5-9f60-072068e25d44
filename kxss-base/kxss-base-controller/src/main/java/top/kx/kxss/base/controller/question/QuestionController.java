package top.kx.kxss.base.controller.question;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.base.entity.question.BaseQuestion;
import top.kx.kxss.base.service.question.BaseQuestionService;
import top.kx.kxss.base.vo.query.question.BaseQuestionPageQuery;
import top.kx.kxss.base.vo.result.question.QuestionResultVO;

import javax.annotation.Resource;

/**
 * <p>
 * 前端控制器
 * 常见问题
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-26 16:24:10
 * @create [2023-05-26 16:24:10] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/question")
@Api(value = "Question", tags = "常见问题-用户端")
public class QuestionController {

    @Resource
    private BaseQuestionService questionService;

    @ApiOperation(value = "分页查询常见问题记录", notes = "分页查询常见问题记录")
    @PostMapping("/page")
    @WebLog(value = "'分页查询常见问题记录:第' + #params?.current + '页, 显示' + #params?.size + '行'", response = false)
    public R<IPage<QuestionResultVO>> page(@RequestBody PageParams<BaseQuestionPageQuery> params) {
        IPage<BaseQuestion> page = params.buildPage(BaseQuestion.class);
        BaseQuestionPageQuery query = params.getModel();
        query.setState(true);
        BaseQuestion model = BeanUtil.toBean(query, BaseQuestion.class);
        LbQueryWrap<BaseQuestion> wraps = Wraps.lbq(model, params.getExtra(), BaseQuestion.class);
        wraps.orderByAsc(BaseQuestion::getSortValue);
        questionService.page(page, wraps);
        IPage<QuestionResultVO> pageVO = BeanPlusUtil.toBeanPage(page, QuestionResultVO.class);
        return R.success(pageVO);
    }
}


