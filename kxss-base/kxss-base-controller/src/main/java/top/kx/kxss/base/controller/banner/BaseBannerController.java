package top.kx.kxss.base.controller.banner;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.kxss.base.entity.banner.BaseBanner;
import top.kx.kxss.base.service.banner.BaseBannerService;
import top.kx.kxss.base.vo.query.banner.BaseBannerPageQuery;
import top.kx.kxss.base.vo.result.banner.BaseBannerResultVO;
import top.kx.kxss.base.vo.save.banner.BaseBannerSaveVO;
import top.kx.kxss.base.vo.update.banner.BaseBannerUpdateVO;
import top.kx.kxss.file.entity.File;
import top.kx.kxss.file.service.FileService;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 前端控制器
 * 轮播图
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-25 15:35:00
 * @create [2023-05-25 15:35:00] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/baseBanner")
@Api(value = "BaseBanner", tags = "轮播图")
public class BaseBannerController extends SuperController<BaseBannerService, Long, BaseBanner, BaseBannerSaveVO,
        BaseBannerUpdateVO, BaseBannerPageQuery, BaseBannerResultVO> {
    private final EchoService echoService;

    @Override
    public EchoService getEchoService() {
        return echoService;
    }

    @Resource
    private FileService fileService;

    @Override
    public void handlerResult(IPage<BaseBannerResultVO> page) {
        List<BaseBannerResultVO> records = page.getRecords();
        //文件信息
        List<Long> imageIds = records.stream().map(BaseBannerResultVO::getImage).collect(Collectors.toList());
        Map<Long, File> fileMap = CollectionUtil.isNotEmpty(imageIds) ? fileService.list(Wraps.<File>lbQ().in(File::getId, imageIds))
                .stream().collect(Collectors.toMap(File::getId, k -> k)) : new HashMap<>();
        for (BaseBannerResultVO record : records) {
            Long image = record.getImage();
            if (ObjectUtil.isNotNull(image)) {
                record.setImageFile(fileMap.get(image));
            }
        }
        super.handlerResult(page);
    }

    /**
     * 修改状态
     *
     * @param id    用户id
     * @param state 用户状态
     * @return 是否成功
     */
    @ApiOperation(value = "修改状态", notes = "修改状态")
    @PutMapping("/updateState")
    @WebLog("'修改状态:id=' + #id + ', state=' + #state")
    public R<Boolean> updateState(
            @NotNull(message = "请选择用户") @RequestParam Long id,
            @NotNull(message = "请设置正确的状态值") @RequestParam Boolean state) {
        return success(superService.updateState(id, state));
    }
}


