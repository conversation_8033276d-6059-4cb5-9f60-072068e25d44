package top.kx.kxss.base.controller.enterprise;

import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.base.controller.SuperController;
import top.kx.kxss.base.service.enterprise.BaseEnterpriseService;
import top.kx.kxss.base.entity.enterprise.BaseEnterprise;
import top.kx.kxss.base.vo.save.enterprise.BaseEnterpriseSaveVO;
import top.kx.kxss.base.vo.update.enterprise.BaseEnterpriseUpdateVO;
import top.kx.kxss.base.vo.result.enterprise.BaseEnterpriseResultVO;
import top.kx.kxss.base.vo.query.enterprise.BaseEnterprisePageQuery;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 前端控制器
 * 企业
 * </p>
 *
 * <AUTHOR>
 * @date 2025-04-25 17:45:11
 * @create [2025-04-25 17:45:11] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/baseEnterprise")
@Api(value = "BaseEnterprise", tags = "企业")
public class BaseEnterpriseController extends SuperController<BaseEnterpriseService, Long, BaseEnterprise, BaseEnterpriseSaveVO,
    BaseEnterpriseUpdateVO, BaseEnterprisePageQuery, BaseEnterpriseResultVO> {
    private final EchoService echoService;
    @Override
    public EchoService getEchoService() {
        return echoService;
    }

}


