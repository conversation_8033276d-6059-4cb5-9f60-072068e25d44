package top.kx.kxss.base.controller.purchase;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.kxss.base.entity.purchase.BasePurchaseDetails;
import top.kx.kxss.base.service.purchase.BasePurchaseDetailsService;
import top.kx.kxss.base.vo.query.purchase.BasePurchaseDetailsPageQuery;
import top.kx.kxss.base.vo.query.purchase.BasePurchaseItemPageQuery;
import top.kx.kxss.base.vo.result.purchase.BasePurchaseDetailsResultVO;
import top.kx.kxss.base.vo.result.purchase.BasePurchaseItemResultVO;
import top.kx.kxss.base.vo.save.purchase.BasePurchaseDetailsSaveVO;
import top.kx.kxss.base.vo.update.purchase.BasePurchaseDetailsUpdateVO;

/**
 * <p>
 * 前端控制器
 * 要货详情
 * </p>
 *
 * <AUTHOR>
 * @date 2025-04-10 14:47:18
 * @create [2025-04-10 14:47:18] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/basePurchaseDetails")
@Api(value = "BasePurchaseDetails", tags = "要货详情")
public class BasePurchaseDetailsController extends SuperController<BasePurchaseDetailsService, Long, BasePurchaseDetails, BasePurchaseDetailsSaveVO,
    BasePurchaseDetailsUpdateVO, BasePurchaseDetailsPageQuery, BasePurchaseDetailsResultVO> {
    private final EchoService echoService;
    @Override
    public EchoService getEchoService() {
        return echoService;
    }

    @ApiOperation("防伪码拿货列表")
    @PostMapping("/securityCodePurchasePage")
    public R<IPage<BasePurchaseItemResultVO>> securityCodePurchasePage(@RequestBody PageParams<BasePurchaseItemPageQuery> query) {
        return R.success(superService.securityCodePurchasePage(query));
    }


}


