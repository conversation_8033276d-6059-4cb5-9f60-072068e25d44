package top.kx.kxss.base.controller.security;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.kxss.base.entity.BaseSecurityCode;
import top.kx.kxss.base.service.BaseSecurityCodeService;
import top.kx.kxss.base.vo.query.*;
import top.kx.kxss.base.vo.result.*;
import top.kx.kxss.base.vo.save.SecurityCodeBindSaveVO;
import top.kx.kxss.base.vo.save.SecurityCodePurchaseControlSaveVO;
import top.kx.kxss.base.vo.save.SecurityCodeScrapSaveVO;
import top.kx.kxss.base.vo.save.purchase.BasePurchaseRemarksVO;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 前端控制器
 * 防伪信息
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-25 13:40:18
 * @create [2023-05-25 13:40:18] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/empSecurityCode")
@Api(value = "EmpSecurityCode", tags = "防伪信息-员工端")
public class EmpSecurityCodeController {

    @Resource
    private BaseSecurityCodeService securityCodeService;

    @ApiOperation("数量统计")
    @GetMapping("/statistics")
    @WebLog("数量统计")
    public R<CodeCountStatisticsVO> statistics() {
        return R.success(securityCodeService.statistics());
    }


    @ApiOperation("标签绑定列表")
    @PostMapping("/bindList")
    @WebLog("标签绑定列表")
    public R<List<EmpSecurityBindResultVO>> bindList(@RequestBody @Validated SecurityCodeBindQuery query) {
        return R.success(securityCodeService.bindList(query));
    }

    @ApiOperation("标签绑定分页列表")
    @PostMapping("/bindPageList")
    @WebLog("标签绑定分页列表")
    public R<IPage<SecurityCodeBindResultVO>> bindPageList(@RequestBody @Validated PageParams<SecurityCodeBindPageQuery> params) {
        return R.success(securityCodeService.bindPageList(params));
    }


    @ApiOperation("标签报废列表")
    @PostMapping("/scrapList")
    @WebLog("标签报废列表")
    public R<IPage<SecurityCodeScrapResultVO>> scrapPageList(@RequestBody @Validated PageParams<SecurityCodeScrapQuery> params) {
        return R.success(securityCodeService.scrapPageList(params));
    }

    @Deprecated
    @ApiOperation("要货管理分页列表")
    @PostMapping("/purchaseControlPageList")
    @WebLog("要货管理分页列表")
    public R<IPage<SecurityCodePurchaseControlResultVO>> purchaseControlPageList(@RequestBody @Validated PageParams<SecurityCodePurchaseControlQuery> params) {
        return R.success(securityCodeService.purchaseControlPageList(params));
    }


    @ApiOperation("要货单详情列表")
    @PostMapping("/purchaseItemPageList")
    @WebLog("要货单详情列表")
    public R<IPage<SecurityCodePurchaseControlResultVO>> purchaseItemPageList(@RequestBody @Validated PageParams<SecurityCodePurchaseQuery> params) {
        return R.success(securityCodeService.purchaseItemPageList(params));
    }


    @ApiOperation("添加要货")
    @PostMapping("/purchaseControl")
    @WebLog("添加要货")
    public R<BaseSecurityCode> purchaseControl(@RequestBody @Validated SecurityCodePurchaseControlSaveVO model) {
        return R.success(securityCodeService.purchaseControl(model));
    }

    @ApiOperation("添加要货(支持大小码)")
    @PostMapping("/purchaseControlNew")
    @WebLog("添加要货(支持大小码)")
    public R<List<BaseSecurityCode>> purchaseControlNew(@RequestBody @Validated SecurityCodePurchaseControlSaveVO model) {
        return R.success(securityCodeService.purchaseControlNew(model));
    }


    @ApiOperation("批量删除-要货信息")
    @PostMapping("/removePurchaseControl")
    @WebLog("批量删除-要货信息")
    public R<Boolean> removePurchaseControl(@RequestBody @Validated SecurityCodePurchaseControlRemoveVO model) {
        return R.success(securityCodeService.removePurchaseControl(model));
    }

    @ApiOperation("要货详情")
    @GetMapping("/purchaseDetail")
    @WebLog("要货详情")
    public R<List<SecurityCodePurchaseControlResultVO>> purchaseDetail(@RequestParam String securityCode) {
        return R.success(securityCodeService.purchaseDetail(securityCode));
    }

    @ApiOperation("要货删除")
    @PostMapping("/delPurchase")
    @WebLog("要货删除")
    public R<Boolean> delPurchase(@RequestParam @ApiParam(value = "要货删除", required = true) String securityCode,
                                  @RequestParam @ApiParam(value = "拿货单ID", required = true) Long purchaseId) {
        if (ObjectUtil.isNull(purchaseId)) {
            return R.fail("请选择需要删除的拿货单");
        }
        if (StringUtils.isBlank(securityCode)) {
            return R.fail("请选择需要删除的码");
        }
        return R.success(securityCodeService.delPurchase(securityCode, purchaseId));
    }


    @ApiOperation("修改拿货备注")
    @PostMapping("/updatePurchaseRemarks")
    @WebLog("修改拿货备注")
    public R<Boolean> updatePurchaseRemarks(@RequestBody @Validated BasePurchaseRemarksVO model) {
        return R.success(securityCodeService.updatePurchaseRemarks(model));
    }

    @ApiOperation("标签绑定")
    @PostMapping("/bind")
    @WebLog("标签绑定")
    public R<BaseSecurityCode> bind(@RequestBody @Validated SecurityCodeBindSaveVO model) {
        return R.success(securityCodeService.bindCode(model));
    }


    @ApiOperation("标签报废")
    @PostMapping("/scrap")
    @WebLog("标签报废")
    public R<BaseSecurityCode> scrap(@RequestBody @Validated SecurityCodeScrapSaveVO model) {
        return R.success(securityCodeService.scrap(model));
    }

    @ApiOperation("标签删除")
    @PostMapping("/del")
    @WebLog("标签删除")
    public R<Boolean> del(@RequestParam @ApiParam(value = "标签删除", required = true) String securityCode) {
        return R.success(securityCodeService.del(securityCode));
    }

    @ApiOperation("标签统计")
    @PostMapping("/codeStatistics")
    @WebLog("标签绑定统计")
    public R<CodeStatisticsVO> bindStatistics(@RequestBody @Validated SecurityCodeStatisticsQuery query) {
        query.setSelectType(ObjectUtil.isNotNull(query.getSelectType()) ? query.getSelectType() : 1);
        return R.success(securityCodeService.statisticsData(query));
    }

    @ApiOperation("查询操作数据统计")
    @PostMapping("/operateStatistics")
    @WebLog("查询操作数据统计")
    public R<List<StatisticsVO>> operateStatistics(@RequestBody @Validated SecurityCodeStatisticsQuery query) {
        query.setSelectType(ObjectUtil.isNotNull(query.getSelectType()) ? query.getSelectType() : 1);
        return R.success(securityCodeService.operateStatistics(query));
    }

    /**
     * 防伪码追踪
     */
    @ApiOperation("防伪码追踪")
    @PostMapping("/securityCodeTrace")
    @WebLog("防伪码追踪")
    public R<BaseSecurityCodeResultVO> securityCodeTrace(@RequestParam @ApiParam(value = "防伪码", required = true) String securityCode) {
        return R.success(securityCodeService.securityCodeTrace(securityCode));
    }

}


