package top.kx.kxss.base.controller.lottery;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.kxss.base.entity.lottery.LotteryActivity;
import top.kx.kxss.base.service.lottery.LotteryActivityService;
import top.kx.kxss.base.vo.query.lottery.LotteryActivityPageQuery;
import top.kx.kxss.base.vo.result.lottery.LotteryActivityResultVO;
import top.kx.kxss.base.vo.result.prize.DefPrizeResultVO;
import top.kx.kxss.base.vo.save.lottery.LotteryActivitySaveVO;
import top.kx.kxss.base.vo.update.lottery.LotteryActivityStatusUpdateVO;
import top.kx.kxss.base.vo.update.lottery.LotteryActivityUpdateVO;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <p>
 * 前端控制器
 * 抽奖活动表
 * </p>
 *
 * <AUTHOR>
 * @date 2025-09-15 17:23:11
 * @create [2025-09-15 17:23:11] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/lotteryActivity")
@Api(value = "LotteryActivity", tags = "抽奖活动表")
public class LotteryActivityController extends SuperController<LotteryActivityService, Long, LotteryActivity, LotteryActivitySaveVO,
    LotteryActivityUpdateVO, LotteryActivityPageQuery, LotteryActivityResultVO> {
    private final EchoService echoService;
    @Override
    public EchoService getEchoService() {
        return echoService;
    }


    /**
     * 修改活动状态
     */
    @ApiOperation(value = "修改活动状态")
    @PostMapping("/updateStatus")
    public R<Boolean> updateStatus(@RequestBody @Validated LotteryActivityStatusUpdateVO updateVO) {
        return R.success(superService.updateStatus(updateVO));
    }

    @Override
    public R<LotteryActivityResultVO> getDetail(Long aLong) {
        return R.success(superService.getDetail(aLong));
    }


    @Override
    public void handlerResult(IPage<LotteryActivityResultVO> page) {
        if (CollUtil.isNotEmpty(page.getRecords())) {
            for (LotteryActivityResultVO record : page.getRecords()) {
                // 计算中奖率
                if (record.getLotteryCount() > 0) {
                    BigDecimal winRate = new BigDecimal(record.getPrizeCount())
                            .divide(new BigDecimal(record.getLotteryCount()), 4, RoundingMode.HALF_UP)
                            .multiply(new BigDecimal(100));
                    record.setWinRate(winRate);
                } else {
                    record.setWinRate(BigDecimal.ZERO);
                }
            }
        }
        super.handlerResult(page);
    }

    /**
     * 抽奖
     */
    @ApiOperation(value = "抽奖")
    @GetMapping("/lottery/{id}")
    public R<DefPrizeResultVO> lottery(@PathVariable Long id) {
        return R.success(superService.lottery(id));
    }

}


