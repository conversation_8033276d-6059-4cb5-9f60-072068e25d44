package top.kx.kxss.base.controller.security;

import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.base.controller.SuperController;
import top.kx.kxss.base.service.security.BaseSecurityCodeBatchDetailsService;
import top.kx.kxss.base.entity.security.BaseSecurityCodeBatchDetails;
import top.kx.kxss.base.vo.save.security.BaseSecurityCodeBatchDetailsSaveVO;
import top.kx.kxss.base.vo.update.security.BaseSecurityCodeBatchDetailsUpdateVO;
import top.kx.kxss.base.vo.result.security.BaseSecurityCodeBatchDetailsResultVO;
import top.kx.kxss.base.vo.query.security.BaseSecurityCodeBatchDetailsPageQuery;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 前端控制器
 * 防伪码批次-详情
 * </p>
 *
 * <AUTHOR>
 * @date 2025-04-25 17:45:12
 * @create [2025-04-25 17:45:12] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/baseSecurityCodeBatchDetails")
@Api(value = "BaseSecurityCodeBatchDetails", tags = "防伪码批次-详情")
public class BaseSecurityCodeBatchDetailsController extends SuperController<BaseSecurityCodeBatchDetailsService, Long, BaseSecurityCodeBatchDetails, BaseSecurityCodeBatchDetailsSaveVO,
    BaseSecurityCodeBatchDetailsUpdateVO, BaseSecurityCodeBatchDetailsPageQuery, BaseSecurityCodeBatchDetailsResultVO> {
    private final EchoService echoService;
    @Override
    public EchoService getEchoService() {
        return echoService;
    }

}


