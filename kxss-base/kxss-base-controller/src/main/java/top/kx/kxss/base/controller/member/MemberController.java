package top.kx.kxss.base.controller.member;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.base.R;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.exception.BizException;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.kxss.base.entity.member.MemberInfo;
import top.kx.kxss.base.service.BaseSecurityCodeService;
import top.kx.kxss.base.service.member.MemberInfoService;
import top.kx.kxss.base.vo.result.member.MemberInfoResultVO;
import top.kx.kxss.base.vo.result.product.ProductResultVO;
import top.kx.kxss.base.vo.update.member.MemberUpdateVO;
import top.kx.kxss.file.entity.File;
import top.kx.kxss.file.service.FileService;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 前端控制器
 * 会员信息
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-24 18:03:33
 * @create [2023-05-24 18:03:33] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/member")
@Api(value = "Member", tags = "会员信息-用户端")
public class MemberController {

    @Resource
    private MemberInfoService memberInfoService;
    @Resource
    private BaseSecurityCodeService securityCodeService;
    @Resource
    private FileService fileService;
    @Resource
    private EchoService echoService;

    /**
     * 获取当前登录的用户信息
     */
    @ApiOperation(value = "获取当前会员信息", notes = "获取当前会员信息")
    @GetMapping(value = "/getMemberInfo")
    public R<MemberInfoResultVO> getMemberInfo(@RequestParam(required = false) Long userId) throws BizException {
        if (userId == null) {
            userId = ContextUtil.getUserId();
        }
        MemberInfo byUserId = memberInfoService.getByUserId(userId);
        if (ObjectUtil.isNull(byUserId)) {
            return R.success(null);
        }
        //头像信息
        MemberInfoResultVO resultVO = BeanUtil.copyProperties(byUserId, MemberInfoResultVO.class);
        if (ObjectUtil.isNotNull(resultVO.getAvatarId())) {
            File byId = fileService.getById(resultVO.getAvatarId());
            resultVO.setAvatarFile(byId);
        }
        return R.success(resultVO);
    }

    @ApiOperation(value = "更新会员信息", notes = "更新会员信息")
    @PutMapping(value = "/updateMember")
    public R<Boolean> updateMember(@RequestBody @Validated MemberUpdateVO updateVO) {
        return R.success(memberInfoService.updateMember(updateVO));
    }

    @ApiOperation(value = "获取我的商品信息", notes = "获取我的商品信息")
    @GetMapping(value = "/productList")
    public R<List<ProductResultVO>> listByMemberId(@RequestParam(required = false) Long userId) throws BizException {
        if (userId == null) {
            userId = ContextUtil.getUserId();
        }
        MemberInfo byUserId = memberInfoService.getByUserId(userId);
        List<ProductResultVO> productByMemberId = securityCodeService.getProductByMemberId(byUserId.getId());
        echoService.action(productByMemberId);
        return R.success(productByMemberId);
    }


}


