package top.kx.kxss.base.controller.product;

import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.base.controller.SuperController;
import top.kx.kxss.base.service.product.BaseProductAttributeService;
import top.kx.kxss.base.entity.product.BaseProductAttribute;
import top.kx.kxss.base.vo.save.product.BaseProductAttributeSaveVO;
import top.kx.kxss.base.vo.update.product.BaseProductAttributeUpdateVO;
import top.kx.kxss.base.vo.result.product.BaseProductAttributeResultVO;
import top.kx.kxss.base.vo.query.product.BaseProductAttributePageQuery;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 前端控制器
 * 商品基础属性关联
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-16 18:44:16
 * @create [2025-06-16 18:44:16] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/baseProductAttribute")
@Api(value = "BaseProductAttribute", tags = "商品基础属性关联")
public class BaseProductAttributeController extends SuperController<BaseProductAttributeService, Long, BaseProductAttribute, BaseProductAttributeSaveVO,
    BaseProductAttributeUpdateVO, BaseProductAttributePageQuery, BaseProductAttributeResultVO> {
    private final EchoService echoService;
    @Override
    public EchoService getEchoService() {
        return echoService;
    }

}


