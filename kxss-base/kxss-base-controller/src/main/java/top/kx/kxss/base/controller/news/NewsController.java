package top.kx.kxss.base.controller.news;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.base.entity.news.BaseNews;
import top.kx.kxss.base.service.news.BaseNewsService;
import top.kx.kxss.base.vo.query.news.BaseNewsPageQuery;
import top.kx.kxss.base.vo.result.news.BaseNewsResultVO;
import top.kx.kxss.base.vo.result.news.NewsResultVO;
import top.kx.kxss.file.entity.File;
import top.kx.kxss.file.service.FileService;
import top.kx.kxss.model.enumeration.base.BannerTypeEnum;
import top.kx.kxss.model.enumeration.base.NewsStatusEnum;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 前端控制器
 * 新闻资讯
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-25 16:12:08
 * @create [2023-05-25 16:12:08] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/news")
@Api(value = "News", tags = "新闻资讯-用户端")
public class NewsController {

    @Resource
    private BaseNewsService baseNewsService;
    @Resource
    private FileService fileService;

    @ApiOperation(value = "分页查询新闻资讯记录", notes = "分页查询新闻资讯记录")
    @PostMapping("/page")
    @WebLog(value = "'分页查询新闻资讯记录:第' + #params?.current + '页, 显示' + #params?.size + '行'", response = false)
    public R<IPage<NewsResultVO>> page(@RequestBody PageParams<BaseNewsPageQuery> params) {
        IPage<BaseNews> page = params.buildPage(BaseNews.class);
        BaseNewsPageQuery query = params.getModel();
        query.setType(StrUtil.isBlank(query.getType()) ? BannerTypeEnum.HOME.getCode() : query.getType());
        query.setStatus(NewsStatusEnum.RELEASE.getCode());
        BaseNews model = BeanUtil.toBean(query, BaseNews.class);
        LbQueryWrap<BaseNews> wraps = Wraps.lbq(model, params.getExtra(), BaseNews.class);
        baseNewsService.page(page, wraps);
        IPage<NewsResultVO> pageVO = BeanPlusUtil.toBeanPage(page, NewsResultVO.class);
        List<NewsResultVO> records = pageVO.getRecords();
        //获取图片信息
        List<Long> coverImages = records.stream().map(NewsResultVO::getCoverImage).collect(Collectors.toList());
        Map<Long, File> fileMap = CollectionUtil.isNotEmpty(coverImages) ? fileService.list(Wraps.<File>lbQ().in(File::getId, coverImages))
                .stream().collect(Collectors.toMap(File::getId, k -> k)) : new HashMap<>();
        for (NewsResultVO record : records) {
            Long coverImage = record.getCoverImage();
            if (ObjectUtil.isNotNull(coverImage)) {
                record.setCoverImageFile(fileMap.get(coverImage));
            }
        }
        return R.success(pageVO);
    }

    @ApiOperation(value = "查询详情")
    @GetMapping("/detail")
    public R<BaseNewsResultVO> detail(@RequestParam Long id) {
        return R.success(baseNewsService.getInfoById(id));
    }
}


