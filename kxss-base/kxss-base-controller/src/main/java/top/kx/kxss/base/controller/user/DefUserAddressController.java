package top.kx.kxss.base.controller.user;

import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.base.controller.SuperController;
import top.kx.kxss.base.service.user.DefUserAddressService;
import top.kx.kxss.base.entity.user.DefUserAddress;
import top.kx.kxss.base.vo.save.user.DefUserAddressSaveVO;
import top.kx.kxss.base.vo.update.user.DefUserAddressUpdateVO;
import top.kx.kxss.base.vo.result.user.DefUserAddressResultVO;
import top.kx.kxss.base.vo.query.user.DefUserAddressPageQuery;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 前端控制器
 * 用户地址库表
 * </p>
 *
 * <AUTHOR>
 * @date 2025-09-15 17:23:11
 * @create [2025-09-15 17:23:11] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/defUserAddress")
@Api(value = "DefUserAddress", tags = "用户地址库表")
public class DefUserAddressController extends SuperController<DefUserAddressService, Long, DefUserAddress, DefUserAddressSaveVO,
    DefUserAddressUpdateVO, DefUserAddressPageQuery, DefUserAddressResultVO> {
    private final EchoService echoService;
    @Override
    public EchoService getEchoService() {
        return echoService;
    }

}


